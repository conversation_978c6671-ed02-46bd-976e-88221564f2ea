# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.22

# compile C with /home/<USER>/.espressif/tools/xtensa-esp-elf/esp-13.2.0_20240530/xtensa-esp-elf/bin/xtensa-esp32s3-elf-gcc
C_DEFINES = -DLV_CONF_INCLUDE_SIMPLE -DMBEDTLS_CONFIG_FILE=\"mbedtls/esp_config.h\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -DUNITY_INCLUDE_CONFIG_H

C_INCLUDES = -I/home/<USER>/esp/esp-idf-v5.3/components/xtensa/esp32s3/include -I/home/<USER>/esp/esp-idf-v5.3/components/xtensa/include -I/home/<USER>/esp/esp-idf-v5.3/components/xtensa/deprecated_include -I/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/config -I/home/<USER>/esp/esp-idf-v5.3/components/newlib/platform_include -I/home/<USER>/esp/esp-idf-v5.3/components/freertos/config/include -I/home/<USER>/esp/esp-idf-v5.3/components/freertos/config/include/freertos -I/home/<USER>/esp/esp-idf-v5.3/components/freertos/config/xtensa/include -I/home/<USER>/esp/esp-idf-v5.3/components/freertos/FreeRTOS-Kernel/include -I/home/<USER>/esp/esp-idf-v5.3/components/freertos/FreeRTOS-Kernel/portable/xtensa/include -I/home/<USER>/esp/esp-idf-v5.3/components/freertos/FreeRTOS-Kernel/portable/xtensa/include/freertos -I/home/<USER>/esp/esp-idf-v5.3/components/freertos/esp_additions/include -I/home/<USER>/esp/esp-idf-v5.3/components/esp_hw_support/include -I/home/<USER>/esp/esp-idf-v5.3/components/esp_hw_support/include/soc -I/home/<USER>/esp/esp-idf-v5.3/components/esp_hw_support/include/soc/esp32s3 -I/home/<USER>/esp/esp-idf-v5.3/components/esp_hw_support/dma/include -I/home/<USER>/esp/esp-idf-v5.3/components/esp_hw_support/ldo/include -I/home/<USER>/esp/esp-idf-v5.3/components/esp_hw_support/port/esp32s3/. -I/home/<USER>/esp/esp-idf-v5.3/components/esp_hw_support/port/esp32s3/include -I/home/<USER>/esp/esp-idf-v5.3/components/heap/include -I/home/<USER>/esp/esp-idf-v5.3/components/log/include -I/home/<USER>/esp/esp-idf-v5.3/components/soc/include -I/home/<USER>/esp/esp-idf-v5.3/components/soc/esp32s3 -I/home/<USER>/esp/esp-idf-v5.3/components/soc/esp32s3/include -I/home/<USER>/esp/esp-idf-v5.3/components/hal/platform_port/include -I/home/<USER>/esp/esp-idf-v5.3/components/hal/esp32s3/include -I/home/<USER>/esp/esp-idf-v5.3/components/hal/include -I/home/<USER>/esp/esp-idf-v5.3/components/esp_rom/include -I/home/<USER>/esp/esp-idf-v5.3/components/esp_rom/include/esp32s3 -I/home/<USER>/esp/esp-idf-v5.3/components/esp_rom/esp32s3 -I/home/<USER>/esp/esp-idf-v5.3/components/esp_common/include -I/home/<USER>/esp/esp-idf-v5.3/components/esp_system/include -I/home/<USER>/esp/esp-idf-v5.3/components/esp_system/port/soc -I/home/<USER>/esp/esp-idf-v5.3/components/esp_system/port/include/private -I/home/<USER>/esp/esp-idf-v5.3/components/esp_timer/include -I/home/<USER>/esp/esp-idf-v5.3/components/lwip/include -I/home/<USER>/esp/esp-idf-v5.3/components/lwip/include/apps -I/home/<USER>/esp/esp-idf-v5.3/components/lwip/include/apps/sntp -I/home/<USER>/esp/esp-idf-v5.3/components/lwip/lwip/src/include -I/home/<USER>/esp/esp-idf-v5.3/components/lwip/port/include -I/home/<USER>/esp/esp-idf-v5.3/components/lwip/port/freertos/include -I/home/<USER>/esp/esp-idf-v5.3/components/lwip/port/esp32xx/include -I/home/<USER>/esp/esp-idf-v5.3/components/lwip/port/esp32xx/include/arch -I/home/<USER>/esp/esp-idf-v5.3/components/lwip/port/esp32xx/include/sys -I/home/<USER>/esp/esp-idf-v5.3/components/esp_driver_gpio/include -I/home/<USER>/esp/esp-idf-v5.3/components/esp_pm/include -I/home/<USER>/esp/esp-idf-v5.3/components/mbedtls/port/include -I/home/<USER>/esp/esp-idf-v5.3/components/mbedtls/mbedtls/include -I/home/<USER>/esp/esp-idf-v5.3/components/mbedtls/mbedtls/library -I/home/<USER>/esp/esp-idf-v5.3/components/mbedtls/esp_crt_bundle/include -I/home/<USER>/esp/esp-idf-v5.3/components/mbedtls/mbedtls/3rdparty/everest/include -I/home/<USER>/esp/esp-idf-v5.3/components/mbedtls/mbedtls/3rdparty/p256-m -I/home/<USER>/esp/esp-idf-v5.3/components/mbedtls/mbedtls/3rdparty/p256-m/p256-m -I/home/<USER>/esp/esp-idf-v5.3/components/esp_app_format/include -I/home/<USER>/esp/esp-idf-v5.3/components/esp_bootloader_format/include -I/home/<USER>/esp/esp-idf-v5.3/components/app_update/include -I/home/<USER>/esp/esp-idf-v5.3/components/bootloader_support/include -I/home/<USER>/esp/esp-idf-v5.3/components/bootloader_support/bootloader_flash/include -I/home/<USER>/esp/esp-idf-v5.3/components/esp_partition/include -I/home/<USER>/esp/esp-idf-v5.3/components/efuse/include -I/home/<USER>/esp/esp-idf-v5.3/components/efuse/esp32s3/include -I/home/<USER>/esp/esp-idf-v5.3/components/esp_mm/include -I/home/<USER>/esp/esp-idf-v5.3/components/spi_flash/include -I/home/<USER>/esp/esp-idf-v5.3/components/pthread/include -I/home/<USER>/esp/esp-idf-v5.3/components/esp_driver_gptimer/include -I/home/<USER>/esp/esp-idf-v5.3/components/esp_ringbuf/include -I/home/<USER>/esp/esp-idf-v5.3/components/esp_driver_uart/include -I/home/<USER>/esp/esp-idf-v5.3/components/vfs/include -I/home/<USER>/esp/esp-idf-v5.3/components/app_trace/include -I/home/<USER>/esp/esp-idf-v5.3/components/esp_event/include -I/home/<USER>/esp/esp-idf-v5.3/components/nvs_flash/include -I/home/<USER>/esp/esp-idf-v5.3/components/esp_driver_pcnt/include -I/home/<USER>/esp/esp-idf-v5.3/components/esp_driver_spi/include -I/home/<USER>/esp/esp-idf-v5.3/components/esp_driver_mcpwm/include -I/home/<USER>/esp/esp-idf-v5.3/components/esp_driver_ana_cmpr/include -I/home/<USER>/esp/esp-idf-v5.3/components/esp_driver_i2s/include -I/home/<USER>/esp/esp-idf-v5.3/components/sdmmc/include -I/home/<USER>/esp/esp-idf-v5.3/components/esp_driver_sdmmc/include -I/home/<USER>/esp/esp-idf-v5.3/components/esp_driver_sdspi/include -I/home/<USER>/esp/esp-idf-v5.3/components/esp_driver_sdio/include -I/home/<USER>/esp/esp-idf-v5.3/components/esp_driver_dac/include -I/home/<USER>/esp/esp-idf-v5.3/components/esp_driver_rmt/include -I/home/<USER>/esp/esp-idf-v5.3/components/esp_driver_tsens/include -I/home/<USER>/esp/esp-idf-v5.3/components/esp_driver_sdm/include -I/home/<USER>/esp/esp-idf-v5.3/components/esp_driver_i2c/include -I/home/<USER>/esp/esp-idf-v5.3/components/esp_driver_ledc/include -I/home/<USER>/esp/esp-idf-v5.3/components/esp_driver_parlio/include -I/home/<USER>/esp/esp-idf-v5.3/components/esp_driver_usb_serial_jtag/include -I/home/<USER>/esp/esp-idf-v5.3/components/driver/deprecated -I/home/<USER>/esp/esp-idf-v5.3/components/driver/i2c/include -I/home/<USER>/esp/esp-idf-v5.3/components/driver/touch_sensor/include -I/home/<USER>/esp/esp-idf-v5.3/components/driver/twai/include -I/home/<USER>/esp/esp-idf-v5.3/components/driver/touch_sensor/esp32s3/include -I/home/<USER>/esp/esp-idf-v5.3/components/esp_phy/include -I/home/<USER>/esp/esp-idf-v5.3/components/esp_phy/esp32s3/include -I/home/<USER>/esp/esp-idf-v5.3/components/esp_vfs_console/include -I/home/<USER>/esp/esp-idf-v5.3/components/esp_netif/include -I/home/<USER>/esp/esp-idf-v5.3/components/wpa_supplicant/include -I/home/<USER>/esp/esp-idf-v5.3/components/wpa_supplicant/port/include -I/home/<USER>/esp/esp-idf-v5.3/components/wpa_supplicant/esp_supplicant/include -I/home/<USER>/esp/esp-idf-v5.3/components/esp_coex/include -I/home/<USER>/esp/esp-idf-v5.3/components/esp_wifi/include -I/home/<USER>/esp/esp-idf-v5.3/components/esp_wifi/wifi_apps/include -I/home/<USER>/esp/esp-idf-v5.3/components/esp_wifi/wifi_apps/nan_app/include -I/home/<USER>/esp/esp-idf-v5.3/components/esp_wifi/include/local -I/home/<USER>/esp/esp-idf-v5.3/components/unity/include -I/home/<USER>/esp/esp-idf-v5.3/components/unity/unity/src -I/home/<USER>/esp/esp-idf-v5.3/components/cmock/CMock/src -I/home/<USER>/esp/esp-idf-v5.3/components/console -I/home/<USER>/esp/esp-idf-v5.3/components/http_parser -I/home/<USER>/esp/esp-idf-v5.3/components/esp-tls -I/home/<USER>/esp/esp-idf-v5.3/components/esp-tls/esp-tls-crypto -I/home/<USER>/esp/esp-idf-v5.3/components/esp_adc/include -I/home/<USER>/esp/esp-idf-v5.3/components/esp_adc/interface -I/home/<USER>/esp/esp-idf-v5.3/components/esp_adc/esp32s3/include -I/home/<USER>/esp/esp-idf-v5.3/components/esp_adc/deprecated/include -I/home/<USER>/esp/esp-idf-v5.3/components/esp_driver_isp/include -I/home/<USER>/esp/esp-idf-v5.3/components/esp_driver_cam/include -I/home/<USER>/esp/esp-idf-v5.3/components/esp_driver_cam/interface -I/home/<USER>/esp/esp-idf-v5.3/components/esp_driver_jpeg/include -I/home/<USER>/esp/esp-idf-v5.3/components/esp_driver_ppa/include -I/home/<USER>/esp/esp-idf-v5.3/components/esp_eth/include -I/home/<USER>/esp/esp-idf-v5.3/components/esp_gdbstub/include -I/home/<USER>/esp/esp-idf-v5.3/components/esp_hid/include -I/home/<USER>/esp/esp-idf-v5.3/components/tcp_transport/include -I/home/<USER>/esp/esp-idf-v5.3/components/esp_http_client/include -I/home/<USER>/esp/esp-idf-v5.3/components/esp_http_server/include -I/home/<USER>/esp/esp-idf-v5.3/components/esp_https_ota/include -I/home/<USER>/esp/esp-idf-v5.3/components/esp_https_server/include -I/home/<USER>/esp/esp-idf-v5.3/components/esp_psram/include -I/home/<USER>/esp/esp-idf-v5.3/components/esp_lcd/include -I/home/<USER>/esp/esp-idf-v5.3/components/esp_lcd/interface -I/home/<USER>/esp/esp-idf-v5.3/components/esp_lcd/rgb/include -I/home/<USER>/esp/esp-idf-v5.3/components/protobuf-c/protobuf-c -I/home/<USER>/esp/esp-idf-v5.3/components/protocomm/include/common -I/home/<USER>/esp/esp-idf-v5.3/components/protocomm/include/security -I/home/<USER>/esp/esp-idf-v5.3/components/protocomm/include/transports -I/home/<USER>/esp/esp-idf-v5.3/components/protocomm/include/crypto/srp6a -I/home/<USER>/esp/esp-idf-v5.3/components/protocomm/proto-c -I/home/<USER>/esp/esp-idf-v5.3/components/esp_local_ctrl/include -I/home/<USER>/esp/esp-idf-v5.3/components/espcoredump/include -I/home/<USER>/esp/esp-idf-v5.3/components/espcoredump/include/port/xtensa -I/home/<USER>/esp/esp-idf-v5.3/components/wear_levelling/include -I/home/<USER>/esp/esp-idf-v5.3/components/fatfs/diskio -I/home/<USER>/esp/esp-idf-v5.3/components/fatfs/src -I/home/<USER>/esp/esp-idf-v5.3/components/fatfs/vfs -I/home/<USER>/esp/esp-idf-v5.3/components/idf_test/include -I/home/<USER>/esp/esp-idf-v5.3/components/idf_test/include/esp32s3 -I/home/<USER>/esp/esp-idf-v5.3/components/ieee802154/include -I/home/<USER>/esp/esp-idf-v5.3/components/json/cJSON -I/home/<USER>/esp/esp-idf-v5.3/components/mqtt/esp-mqtt/include -I/home/<USER>/esp/esp-idf-v5.3/components/nvs_sec_provider/include -I/home/<USER>/esp/esp-idf-v5.3/components/perfmon/include -I/home/<USER>/esp/esp-idf-v5.3/components/spiffs/include -I/home/<USER>/esp/esp-idf-v5.3/components/touch_element/include -I/home/<USER>/esp/esp-idf-v5.3/components/usb/include -I/home/<USER>/esp/esp-idf-v5.3/components/wifi_provisioning/include -I/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/managed_components/78__esp-ml307/include -I/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/managed_components/78__esp-opus/include -I/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/managed_components/78__esp-opus-encoder/include -I/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/managed_components/78__esp-wifi-connect/include -I/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/managed_components/espressif__button/include -I/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/managed_components/espressif__esp-dsp/modules/dotprod/include -I/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/managed_components/espressif__esp-dsp/modules/support/include -I/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/managed_components/espressif__esp-dsp/modules/support/mem/include -I/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/managed_components/espressif__esp-dsp/modules/windows/include -I/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/managed_components/espressif__esp-dsp/modules/windows/hann/include -I/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/managed_components/espressif__esp-dsp/modules/windows/blackman/include -I/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/managed_components/espressif__esp-dsp/modules/windows/blackman_harris/include -I/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/managed_components/espressif__esp-dsp/modules/windows/blackman_nuttall/include -I/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/managed_components/espressif__esp-dsp/modules/windows/nuttall/include -I/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/managed_components/espressif__esp-dsp/modules/windows/flat_top/include -I/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/managed_components/espressif__esp-dsp/modules/iir/include -I/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/managed_components/espressif__esp-dsp/modules/fir/include -I/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/managed_components/espressif__esp-dsp/modules/math/include -I/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/managed_components/espressif__esp-dsp/modules/math/add/include -I/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/managed_components/espressif__esp-dsp/modules/math/sub/include -I/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/managed_components/espressif__esp-dsp/modules/math/mul/include -I/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/managed_components/espressif__esp-dsp/modules/math/addc/include -I/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/managed_components/espressif__esp-dsp/modules/math/mulc/include -I/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/managed_components/espressif__esp-dsp/modules/math/sqrt/include -I/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/managed_components/espressif__esp-dsp/modules/matrix/mul/include -I/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/managed_components/espressif__esp-dsp/modules/matrix/add/include -I/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/managed_components/espressif__esp-dsp/modules/matrix/addc/include -I/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/managed_components/espressif__esp-dsp/modules/matrix/mulc/include -I/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/managed_components/espressif__esp-dsp/modules/matrix/sub/include -I/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/managed_components/espressif__esp-dsp/modules/matrix/include -I/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/managed_components/espressif__esp-dsp/modules/fft/include -I/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/managed_components/espressif__esp-dsp/modules/dct/include -I/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/managed_components/espressif__esp-dsp/modules/conv/include -I/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/managed_components/espressif__esp-dsp/modules/common/include -I/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/managed_components/espressif__esp-dsp/modules/matrix/mul/test/include -I/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/managed_components/espressif__esp-dsp/modules/kalman/ekf/include -I/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/managed_components/espressif__esp-dsp/modules/kalman/ekf_imu13states/include -I/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/managed_components/espressif__esp-sr/src/include -I/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/managed_components/espressif__esp-sr/esp-tts/esp_tts_chinese/include -I/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/managed_components/espressif__esp-sr/include/esp32s3 -I/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/managed_components/espressif__esp_codec_dev/include -I/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/managed_components/espressif__esp_codec_dev/interface -I/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/managed_components/espressif__esp_codec_dev/device/include -I/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/managed_components/lvgl__lvgl -I/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/managed_components/lvgl__lvgl/src -I/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/managed_components -I/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/managed_components/lvgl__lvgl/examples -I/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/managed_components/lvgl__lvgl/demos -I/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/managed_components/espressif__esp_lvgl_port/include -I/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/managed_components/espressif__led_strip/include -I/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/managed_components/espressif__led_strip/interface -I/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/main -I/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/main/display -I/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/main/audio_codecs -I/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/main/protocols -I/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/main/audio_processing -I/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/main/fonts -I/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/main/boards/common

C_FLAGS = -mlongcalls  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -fdiagnostics-color=always -DBUTTON_VER_MAJOR=3 -DBUTTON_VER_MINOR=5 -DBUTTON_VER_PATCH=0

