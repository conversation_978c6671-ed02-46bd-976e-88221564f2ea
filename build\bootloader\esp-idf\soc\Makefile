# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.22

# Default target executed when no arguments are given to make.
default_target: all
.PHONY : default_target

# Allow only one "make -f Makefile2" at a time, but pass parallelism.
.NOTPARALLEL:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/esp/esp-idf-v5.3/components/bootloader/subproject

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/bootloader

#=============================================================================
# Targets provided globally by CMake.

# Special rule for the target edit_cache
edit_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "No interactive CMake dialog available..."
	/usr/bin/cmake -E echo No\ interactive\ CMake\ dialog\ available.
.PHONY : edit_cache

# Special rule for the target edit_cache
edit_cache/fast: edit_cache
.PHONY : edit_cache/fast

# Special rule for the target rebuild_cache
rebuild_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Running CMake to regenerate build system..."
	/usr/bin/cmake --regenerate-during-build -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : rebuild_cache

# Special rule for the target rebuild_cache
rebuild_cache/fast: rebuild_cache
.PHONY : rebuild_cache/fast

# The main all target
all: cmake_check_build_system
	cd /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/bootloader && $(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/bootloader/CMakeFiles /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/bootloader/esp-idf/soc//CMakeFiles/progress.marks
	cd /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/bootloader && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 esp-idf/soc/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/bootloader/CMakeFiles 0
.PHONY : all

# The main clean target
clean:
	cd /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/bootloader && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 esp-idf/soc/clean
.PHONY : clean

# The main clean target
clean/fast: clean
.PHONY : clean/fast

# Prepare targets for installation.
preinstall: all
	cd /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/bootloader && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 esp-idf/soc/preinstall
.PHONY : preinstall

# Prepare targets for installation.
preinstall/fast:
	cd /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/bootloader && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 esp-idf/soc/preinstall
.PHONY : preinstall/fast

# clear depends
depend:
	cd /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/bootloader && $(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 1
.PHONY : depend

# Convenience name for target.
esp-idf/soc/CMakeFiles/__idf_soc.dir/rule:
	cd /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/bootloader && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 esp-idf/soc/CMakeFiles/__idf_soc.dir/rule
.PHONY : esp-idf/soc/CMakeFiles/__idf_soc.dir/rule

# Convenience name for target.
__idf_soc: esp-idf/soc/CMakeFiles/__idf_soc.dir/rule
.PHONY : __idf_soc

# fast build rule for target.
__idf_soc/fast:
	cd /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/bootloader && $(MAKE) $(MAKESILENT) -f esp-idf/soc/CMakeFiles/__idf_soc.dir/build.make esp-idf/soc/CMakeFiles/__idf_soc.dir/build
.PHONY : __idf_soc/fast

dport_access_common.obj: dport_access_common.c.obj
.PHONY : dport_access_common.obj

# target to build an object file
dport_access_common.c.obj:
	cd /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/bootloader && $(MAKE) $(MAKESILENT) -f esp-idf/soc/CMakeFiles/__idf_soc.dir/build.make esp-idf/soc/CMakeFiles/__idf_soc.dir/dport_access_common.c.obj
.PHONY : dport_access_common.c.obj

dport_access_common.i: dport_access_common.c.i
.PHONY : dport_access_common.i

# target to preprocess a source file
dport_access_common.c.i:
	cd /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/bootloader && $(MAKE) $(MAKESILENT) -f esp-idf/soc/CMakeFiles/__idf_soc.dir/build.make esp-idf/soc/CMakeFiles/__idf_soc.dir/dport_access_common.c.i
.PHONY : dport_access_common.c.i

dport_access_common.s: dport_access_common.c.s
.PHONY : dport_access_common.s

# target to generate assembly for a file
dport_access_common.c.s:
	cd /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/bootloader && $(MAKE) $(MAKESILENT) -f esp-idf/soc/CMakeFiles/__idf_soc.dir/build.make esp-idf/soc/CMakeFiles/__idf_soc.dir/dport_access_common.c.s
.PHONY : dport_access_common.c.s

esp32s3/adc_periph.obj: esp32s3/adc_periph.c.obj
.PHONY : esp32s3/adc_periph.obj

# target to build an object file
esp32s3/adc_periph.c.obj:
	cd /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/bootloader && $(MAKE) $(MAKESILENT) -f esp-idf/soc/CMakeFiles/__idf_soc.dir/build.make esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/adc_periph.c.obj
.PHONY : esp32s3/adc_periph.c.obj

esp32s3/adc_periph.i: esp32s3/adc_periph.c.i
.PHONY : esp32s3/adc_periph.i

# target to preprocess a source file
esp32s3/adc_periph.c.i:
	cd /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/bootloader && $(MAKE) $(MAKESILENT) -f esp-idf/soc/CMakeFiles/__idf_soc.dir/build.make esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/adc_periph.c.i
.PHONY : esp32s3/adc_periph.c.i

esp32s3/adc_periph.s: esp32s3/adc_periph.c.s
.PHONY : esp32s3/adc_periph.s

# target to generate assembly for a file
esp32s3/adc_periph.c.s:
	cd /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/bootloader && $(MAKE) $(MAKESILENT) -f esp-idf/soc/CMakeFiles/__idf_soc.dir/build.make esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/adc_periph.c.s
.PHONY : esp32s3/adc_periph.c.s

esp32s3/dedic_gpio_periph.obj: esp32s3/dedic_gpio_periph.c.obj
.PHONY : esp32s3/dedic_gpio_periph.obj

# target to build an object file
esp32s3/dedic_gpio_periph.c.obj:
	cd /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/bootloader && $(MAKE) $(MAKESILENT) -f esp-idf/soc/CMakeFiles/__idf_soc.dir/build.make esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/dedic_gpio_periph.c.obj
.PHONY : esp32s3/dedic_gpio_periph.c.obj

esp32s3/dedic_gpio_periph.i: esp32s3/dedic_gpio_periph.c.i
.PHONY : esp32s3/dedic_gpio_periph.i

# target to preprocess a source file
esp32s3/dedic_gpio_periph.c.i:
	cd /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/bootloader && $(MAKE) $(MAKESILENT) -f esp-idf/soc/CMakeFiles/__idf_soc.dir/build.make esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/dedic_gpio_periph.c.i
.PHONY : esp32s3/dedic_gpio_periph.c.i

esp32s3/dedic_gpio_periph.s: esp32s3/dedic_gpio_periph.c.s
.PHONY : esp32s3/dedic_gpio_periph.s

# target to generate assembly for a file
esp32s3/dedic_gpio_periph.c.s:
	cd /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/bootloader && $(MAKE) $(MAKESILENT) -f esp-idf/soc/CMakeFiles/__idf_soc.dir/build.make esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/dedic_gpio_periph.c.s
.PHONY : esp32s3/dedic_gpio_periph.c.s

esp32s3/gdma_periph.obj: esp32s3/gdma_periph.c.obj
.PHONY : esp32s3/gdma_periph.obj

# target to build an object file
esp32s3/gdma_periph.c.obj:
	cd /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/bootloader && $(MAKE) $(MAKESILENT) -f esp-idf/soc/CMakeFiles/__idf_soc.dir/build.make esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/gdma_periph.c.obj
.PHONY : esp32s3/gdma_periph.c.obj

esp32s3/gdma_periph.i: esp32s3/gdma_periph.c.i
.PHONY : esp32s3/gdma_periph.i

# target to preprocess a source file
esp32s3/gdma_periph.c.i:
	cd /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/bootloader && $(MAKE) $(MAKESILENT) -f esp-idf/soc/CMakeFiles/__idf_soc.dir/build.make esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/gdma_periph.c.i
.PHONY : esp32s3/gdma_periph.c.i

esp32s3/gdma_periph.s: esp32s3/gdma_periph.c.s
.PHONY : esp32s3/gdma_periph.s

# target to generate assembly for a file
esp32s3/gdma_periph.c.s:
	cd /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/bootloader && $(MAKE) $(MAKESILENT) -f esp-idf/soc/CMakeFiles/__idf_soc.dir/build.make esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/gdma_periph.c.s
.PHONY : esp32s3/gdma_periph.c.s

esp32s3/gpio_periph.obj: esp32s3/gpio_periph.c.obj
.PHONY : esp32s3/gpio_periph.obj

# target to build an object file
esp32s3/gpio_periph.c.obj:
	cd /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/bootloader && $(MAKE) $(MAKESILENT) -f esp-idf/soc/CMakeFiles/__idf_soc.dir/build.make esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/gpio_periph.c.obj
.PHONY : esp32s3/gpio_periph.c.obj

esp32s3/gpio_periph.i: esp32s3/gpio_periph.c.i
.PHONY : esp32s3/gpio_periph.i

# target to preprocess a source file
esp32s3/gpio_periph.c.i:
	cd /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/bootloader && $(MAKE) $(MAKESILENT) -f esp-idf/soc/CMakeFiles/__idf_soc.dir/build.make esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/gpio_periph.c.i
.PHONY : esp32s3/gpio_periph.c.i

esp32s3/gpio_periph.s: esp32s3/gpio_periph.c.s
.PHONY : esp32s3/gpio_periph.s

# target to generate assembly for a file
esp32s3/gpio_periph.c.s:
	cd /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/bootloader && $(MAKE) $(MAKESILENT) -f esp-idf/soc/CMakeFiles/__idf_soc.dir/build.make esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/gpio_periph.c.s
.PHONY : esp32s3/gpio_periph.c.s

esp32s3/i2c_periph.obj: esp32s3/i2c_periph.c.obj
.PHONY : esp32s3/i2c_periph.obj

# target to build an object file
esp32s3/i2c_periph.c.obj:
	cd /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/bootloader && $(MAKE) $(MAKESILENT) -f esp-idf/soc/CMakeFiles/__idf_soc.dir/build.make esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/i2c_periph.c.obj
.PHONY : esp32s3/i2c_periph.c.obj

esp32s3/i2c_periph.i: esp32s3/i2c_periph.c.i
.PHONY : esp32s3/i2c_periph.i

# target to preprocess a source file
esp32s3/i2c_periph.c.i:
	cd /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/bootloader && $(MAKE) $(MAKESILENT) -f esp-idf/soc/CMakeFiles/__idf_soc.dir/build.make esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/i2c_periph.c.i
.PHONY : esp32s3/i2c_periph.c.i

esp32s3/i2c_periph.s: esp32s3/i2c_periph.c.s
.PHONY : esp32s3/i2c_periph.s

# target to generate assembly for a file
esp32s3/i2c_periph.c.s:
	cd /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/bootloader && $(MAKE) $(MAKESILENT) -f esp-idf/soc/CMakeFiles/__idf_soc.dir/build.make esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/i2c_periph.c.s
.PHONY : esp32s3/i2c_periph.c.s

esp32s3/i2s_periph.obj: esp32s3/i2s_periph.c.obj
.PHONY : esp32s3/i2s_periph.obj

# target to build an object file
esp32s3/i2s_periph.c.obj:
	cd /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/bootloader && $(MAKE) $(MAKESILENT) -f esp-idf/soc/CMakeFiles/__idf_soc.dir/build.make esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/i2s_periph.c.obj
.PHONY : esp32s3/i2s_periph.c.obj

esp32s3/i2s_periph.i: esp32s3/i2s_periph.c.i
.PHONY : esp32s3/i2s_periph.i

# target to preprocess a source file
esp32s3/i2s_periph.c.i:
	cd /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/bootloader && $(MAKE) $(MAKESILENT) -f esp-idf/soc/CMakeFiles/__idf_soc.dir/build.make esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/i2s_periph.c.i
.PHONY : esp32s3/i2s_periph.c.i

esp32s3/i2s_periph.s: esp32s3/i2s_periph.c.s
.PHONY : esp32s3/i2s_periph.s

# target to generate assembly for a file
esp32s3/i2s_periph.c.s:
	cd /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/bootloader && $(MAKE) $(MAKESILENT) -f esp-idf/soc/CMakeFiles/__idf_soc.dir/build.make esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/i2s_periph.c.s
.PHONY : esp32s3/i2s_periph.c.s

esp32s3/interrupts.obj: esp32s3/interrupts.c.obj
.PHONY : esp32s3/interrupts.obj

# target to build an object file
esp32s3/interrupts.c.obj:
	cd /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/bootloader && $(MAKE) $(MAKESILENT) -f esp-idf/soc/CMakeFiles/__idf_soc.dir/build.make esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/interrupts.c.obj
.PHONY : esp32s3/interrupts.c.obj

esp32s3/interrupts.i: esp32s3/interrupts.c.i
.PHONY : esp32s3/interrupts.i

# target to preprocess a source file
esp32s3/interrupts.c.i:
	cd /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/bootloader && $(MAKE) $(MAKESILENT) -f esp-idf/soc/CMakeFiles/__idf_soc.dir/build.make esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/interrupts.c.i
.PHONY : esp32s3/interrupts.c.i

esp32s3/interrupts.s: esp32s3/interrupts.c.s
.PHONY : esp32s3/interrupts.s

# target to generate assembly for a file
esp32s3/interrupts.c.s:
	cd /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/bootloader && $(MAKE) $(MAKESILENT) -f esp-idf/soc/CMakeFiles/__idf_soc.dir/build.make esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/interrupts.c.s
.PHONY : esp32s3/interrupts.c.s

esp32s3/lcd_periph.obj: esp32s3/lcd_periph.c.obj
.PHONY : esp32s3/lcd_periph.obj

# target to build an object file
esp32s3/lcd_periph.c.obj:
	cd /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/bootloader && $(MAKE) $(MAKESILENT) -f esp-idf/soc/CMakeFiles/__idf_soc.dir/build.make esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/lcd_periph.c.obj
.PHONY : esp32s3/lcd_periph.c.obj

esp32s3/lcd_periph.i: esp32s3/lcd_periph.c.i
.PHONY : esp32s3/lcd_periph.i

# target to preprocess a source file
esp32s3/lcd_periph.c.i:
	cd /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/bootloader && $(MAKE) $(MAKESILENT) -f esp-idf/soc/CMakeFiles/__idf_soc.dir/build.make esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/lcd_periph.c.i
.PHONY : esp32s3/lcd_periph.c.i

esp32s3/lcd_periph.s: esp32s3/lcd_periph.c.s
.PHONY : esp32s3/lcd_periph.s

# target to generate assembly for a file
esp32s3/lcd_periph.c.s:
	cd /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/bootloader && $(MAKE) $(MAKESILENT) -f esp-idf/soc/CMakeFiles/__idf_soc.dir/build.make esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/lcd_periph.c.s
.PHONY : esp32s3/lcd_periph.c.s

esp32s3/ledc_periph.obj: esp32s3/ledc_periph.c.obj
.PHONY : esp32s3/ledc_periph.obj

# target to build an object file
esp32s3/ledc_periph.c.obj:
	cd /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/bootloader && $(MAKE) $(MAKESILENT) -f esp-idf/soc/CMakeFiles/__idf_soc.dir/build.make esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/ledc_periph.c.obj
.PHONY : esp32s3/ledc_periph.c.obj

esp32s3/ledc_periph.i: esp32s3/ledc_periph.c.i
.PHONY : esp32s3/ledc_periph.i

# target to preprocess a source file
esp32s3/ledc_periph.c.i:
	cd /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/bootloader && $(MAKE) $(MAKESILENT) -f esp-idf/soc/CMakeFiles/__idf_soc.dir/build.make esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/ledc_periph.c.i
.PHONY : esp32s3/ledc_periph.c.i

esp32s3/ledc_periph.s: esp32s3/ledc_periph.c.s
.PHONY : esp32s3/ledc_periph.s

# target to generate assembly for a file
esp32s3/ledc_periph.c.s:
	cd /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/bootloader && $(MAKE) $(MAKESILENT) -f esp-idf/soc/CMakeFiles/__idf_soc.dir/build.make esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/ledc_periph.c.s
.PHONY : esp32s3/ledc_periph.c.s

esp32s3/mcpwm_periph.obj: esp32s3/mcpwm_periph.c.obj
.PHONY : esp32s3/mcpwm_periph.obj

# target to build an object file
esp32s3/mcpwm_periph.c.obj:
	cd /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/bootloader && $(MAKE) $(MAKESILENT) -f esp-idf/soc/CMakeFiles/__idf_soc.dir/build.make esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/mcpwm_periph.c.obj
.PHONY : esp32s3/mcpwm_periph.c.obj

esp32s3/mcpwm_periph.i: esp32s3/mcpwm_periph.c.i
.PHONY : esp32s3/mcpwm_periph.i

# target to preprocess a source file
esp32s3/mcpwm_periph.c.i:
	cd /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/bootloader && $(MAKE) $(MAKESILENT) -f esp-idf/soc/CMakeFiles/__idf_soc.dir/build.make esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/mcpwm_periph.c.i
.PHONY : esp32s3/mcpwm_periph.c.i

esp32s3/mcpwm_periph.s: esp32s3/mcpwm_periph.c.s
.PHONY : esp32s3/mcpwm_periph.s

# target to generate assembly for a file
esp32s3/mcpwm_periph.c.s:
	cd /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/bootloader && $(MAKE) $(MAKESILENT) -f esp-idf/soc/CMakeFiles/__idf_soc.dir/build.make esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/mcpwm_periph.c.s
.PHONY : esp32s3/mcpwm_periph.c.s

esp32s3/mpi_periph.obj: esp32s3/mpi_periph.c.obj
.PHONY : esp32s3/mpi_periph.obj

# target to build an object file
esp32s3/mpi_periph.c.obj:
	cd /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/bootloader && $(MAKE) $(MAKESILENT) -f esp-idf/soc/CMakeFiles/__idf_soc.dir/build.make esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/mpi_periph.c.obj
.PHONY : esp32s3/mpi_periph.c.obj

esp32s3/mpi_periph.i: esp32s3/mpi_periph.c.i
.PHONY : esp32s3/mpi_periph.i

# target to preprocess a source file
esp32s3/mpi_periph.c.i:
	cd /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/bootloader && $(MAKE) $(MAKESILENT) -f esp-idf/soc/CMakeFiles/__idf_soc.dir/build.make esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/mpi_periph.c.i
.PHONY : esp32s3/mpi_periph.c.i

esp32s3/mpi_periph.s: esp32s3/mpi_periph.c.s
.PHONY : esp32s3/mpi_periph.s

# target to generate assembly for a file
esp32s3/mpi_periph.c.s:
	cd /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/bootloader && $(MAKE) $(MAKESILENT) -f esp-idf/soc/CMakeFiles/__idf_soc.dir/build.make esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/mpi_periph.c.s
.PHONY : esp32s3/mpi_periph.c.s

esp32s3/pcnt_periph.obj: esp32s3/pcnt_periph.c.obj
.PHONY : esp32s3/pcnt_periph.obj

# target to build an object file
esp32s3/pcnt_periph.c.obj:
	cd /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/bootloader && $(MAKE) $(MAKESILENT) -f esp-idf/soc/CMakeFiles/__idf_soc.dir/build.make esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/pcnt_periph.c.obj
.PHONY : esp32s3/pcnt_periph.c.obj

esp32s3/pcnt_periph.i: esp32s3/pcnt_periph.c.i
.PHONY : esp32s3/pcnt_periph.i

# target to preprocess a source file
esp32s3/pcnt_periph.c.i:
	cd /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/bootloader && $(MAKE) $(MAKESILENT) -f esp-idf/soc/CMakeFiles/__idf_soc.dir/build.make esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/pcnt_periph.c.i
.PHONY : esp32s3/pcnt_periph.c.i

esp32s3/pcnt_periph.s: esp32s3/pcnt_periph.c.s
.PHONY : esp32s3/pcnt_periph.s

# target to generate assembly for a file
esp32s3/pcnt_periph.c.s:
	cd /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/bootloader && $(MAKE) $(MAKESILENT) -f esp-idf/soc/CMakeFiles/__idf_soc.dir/build.make esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/pcnt_periph.c.s
.PHONY : esp32s3/pcnt_periph.c.s

esp32s3/rmt_periph.obj: esp32s3/rmt_periph.c.obj
.PHONY : esp32s3/rmt_periph.obj

# target to build an object file
esp32s3/rmt_periph.c.obj:
	cd /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/bootloader && $(MAKE) $(MAKESILENT) -f esp-idf/soc/CMakeFiles/__idf_soc.dir/build.make esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/rmt_periph.c.obj
.PHONY : esp32s3/rmt_periph.c.obj

esp32s3/rmt_periph.i: esp32s3/rmt_periph.c.i
.PHONY : esp32s3/rmt_periph.i

# target to preprocess a source file
esp32s3/rmt_periph.c.i:
	cd /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/bootloader && $(MAKE) $(MAKESILENT) -f esp-idf/soc/CMakeFiles/__idf_soc.dir/build.make esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/rmt_periph.c.i
.PHONY : esp32s3/rmt_periph.c.i

esp32s3/rmt_periph.s: esp32s3/rmt_periph.c.s
.PHONY : esp32s3/rmt_periph.s

# target to generate assembly for a file
esp32s3/rmt_periph.c.s:
	cd /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/bootloader && $(MAKE) $(MAKESILENT) -f esp-idf/soc/CMakeFiles/__idf_soc.dir/build.make esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/rmt_periph.c.s
.PHONY : esp32s3/rmt_periph.c.s

esp32s3/rtc_io_periph.obj: esp32s3/rtc_io_periph.c.obj
.PHONY : esp32s3/rtc_io_periph.obj

# target to build an object file
esp32s3/rtc_io_periph.c.obj:
	cd /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/bootloader && $(MAKE) $(MAKESILENT) -f esp-idf/soc/CMakeFiles/__idf_soc.dir/build.make esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/rtc_io_periph.c.obj
.PHONY : esp32s3/rtc_io_periph.c.obj

esp32s3/rtc_io_periph.i: esp32s3/rtc_io_periph.c.i
.PHONY : esp32s3/rtc_io_periph.i

# target to preprocess a source file
esp32s3/rtc_io_periph.c.i:
	cd /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/bootloader && $(MAKE) $(MAKESILENT) -f esp-idf/soc/CMakeFiles/__idf_soc.dir/build.make esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/rtc_io_periph.c.i
.PHONY : esp32s3/rtc_io_periph.c.i

esp32s3/rtc_io_periph.s: esp32s3/rtc_io_periph.c.s
.PHONY : esp32s3/rtc_io_periph.s

# target to generate assembly for a file
esp32s3/rtc_io_periph.c.s:
	cd /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/bootloader && $(MAKE) $(MAKESILENT) -f esp-idf/soc/CMakeFiles/__idf_soc.dir/build.make esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/rtc_io_periph.c.s
.PHONY : esp32s3/rtc_io_periph.c.s

esp32s3/sdm_periph.obj: esp32s3/sdm_periph.c.obj
.PHONY : esp32s3/sdm_periph.obj

# target to build an object file
esp32s3/sdm_periph.c.obj:
	cd /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/bootloader && $(MAKE) $(MAKESILENT) -f esp-idf/soc/CMakeFiles/__idf_soc.dir/build.make esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/sdm_periph.c.obj
.PHONY : esp32s3/sdm_periph.c.obj

esp32s3/sdm_periph.i: esp32s3/sdm_periph.c.i
.PHONY : esp32s3/sdm_periph.i

# target to preprocess a source file
esp32s3/sdm_periph.c.i:
	cd /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/bootloader && $(MAKE) $(MAKESILENT) -f esp-idf/soc/CMakeFiles/__idf_soc.dir/build.make esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/sdm_periph.c.i
.PHONY : esp32s3/sdm_periph.c.i

esp32s3/sdm_periph.s: esp32s3/sdm_periph.c.s
.PHONY : esp32s3/sdm_periph.s

# target to generate assembly for a file
esp32s3/sdm_periph.c.s:
	cd /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/bootloader && $(MAKE) $(MAKESILENT) -f esp-idf/soc/CMakeFiles/__idf_soc.dir/build.make esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/sdm_periph.c.s
.PHONY : esp32s3/sdm_periph.c.s

esp32s3/sdmmc_periph.obj: esp32s3/sdmmc_periph.c.obj
.PHONY : esp32s3/sdmmc_periph.obj

# target to build an object file
esp32s3/sdmmc_periph.c.obj:
	cd /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/bootloader && $(MAKE) $(MAKESILENT) -f esp-idf/soc/CMakeFiles/__idf_soc.dir/build.make esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/sdmmc_periph.c.obj
.PHONY : esp32s3/sdmmc_periph.c.obj

esp32s3/sdmmc_periph.i: esp32s3/sdmmc_periph.c.i
.PHONY : esp32s3/sdmmc_periph.i

# target to preprocess a source file
esp32s3/sdmmc_periph.c.i:
	cd /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/bootloader && $(MAKE) $(MAKESILENT) -f esp-idf/soc/CMakeFiles/__idf_soc.dir/build.make esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/sdmmc_periph.c.i
.PHONY : esp32s3/sdmmc_periph.c.i

esp32s3/sdmmc_periph.s: esp32s3/sdmmc_periph.c.s
.PHONY : esp32s3/sdmmc_periph.s

# target to generate assembly for a file
esp32s3/sdmmc_periph.c.s:
	cd /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/bootloader && $(MAKE) $(MAKESILENT) -f esp-idf/soc/CMakeFiles/__idf_soc.dir/build.make esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/sdmmc_periph.c.s
.PHONY : esp32s3/sdmmc_periph.c.s

esp32s3/spi_periph.obj: esp32s3/spi_periph.c.obj
.PHONY : esp32s3/spi_periph.obj

# target to build an object file
esp32s3/spi_periph.c.obj:
	cd /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/bootloader && $(MAKE) $(MAKESILENT) -f esp-idf/soc/CMakeFiles/__idf_soc.dir/build.make esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/spi_periph.c.obj
.PHONY : esp32s3/spi_periph.c.obj

esp32s3/spi_periph.i: esp32s3/spi_periph.c.i
.PHONY : esp32s3/spi_periph.i

# target to preprocess a source file
esp32s3/spi_periph.c.i:
	cd /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/bootloader && $(MAKE) $(MAKESILENT) -f esp-idf/soc/CMakeFiles/__idf_soc.dir/build.make esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/spi_periph.c.i
.PHONY : esp32s3/spi_periph.c.i

esp32s3/spi_periph.s: esp32s3/spi_periph.c.s
.PHONY : esp32s3/spi_periph.s

# target to generate assembly for a file
esp32s3/spi_periph.c.s:
	cd /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/bootloader && $(MAKE) $(MAKESILENT) -f esp-idf/soc/CMakeFiles/__idf_soc.dir/build.make esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/spi_periph.c.s
.PHONY : esp32s3/spi_periph.c.s

esp32s3/temperature_sensor_periph.obj: esp32s3/temperature_sensor_periph.c.obj
.PHONY : esp32s3/temperature_sensor_periph.obj

# target to build an object file
esp32s3/temperature_sensor_periph.c.obj:
	cd /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/bootloader && $(MAKE) $(MAKESILENT) -f esp-idf/soc/CMakeFiles/__idf_soc.dir/build.make esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/temperature_sensor_periph.c.obj
.PHONY : esp32s3/temperature_sensor_periph.c.obj

esp32s3/temperature_sensor_periph.i: esp32s3/temperature_sensor_periph.c.i
.PHONY : esp32s3/temperature_sensor_periph.i

# target to preprocess a source file
esp32s3/temperature_sensor_periph.c.i:
	cd /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/bootloader && $(MAKE) $(MAKESILENT) -f esp-idf/soc/CMakeFiles/__idf_soc.dir/build.make esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/temperature_sensor_periph.c.i
.PHONY : esp32s3/temperature_sensor_periph.c.i

esp32s3/temperature_sensor_periph.s: esp32s3/temperature_sensor_periph.c.s
.PHONY : esp32s3/temperature_sensor_periph.s

# target to generate assembly for a file
esp32s3/temperature_sensor_periph.c.s:
	cd /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/bootloader && $(MAKE) $(MAKESILENT) -f esp-idf/soc/CMakeFiles/__idf_soc.dir/build.make esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/temperature_sensor_periph.c.s
.PHONY : esp32s3/temperature_sensor_periph.c.s

esp32s3/timer_periph.obj: esp32s3/timer_periph.c.obj
.PHONY : esp32s3/timer_periph.obj

# target to build an object file
esp32s3/timer_periph.c.obj:
	cd /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/bootloader && $(MAKE) $(MAKESILENT) -f esp-idf/soc/CMakeFiles/__idf_soc.dir/build.make esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/timer_periph.c.obj
.PHONY : esp32s3/timer_periph.c.obj

esp32s3/timer_periph.i: esp32s3/timer_periph.c.i
.PHONY : esp32s3/timer_periph.i

# target to preprocess a source file
esp32s3/timer_periph.c.i:
	cd /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/bootloader && $(MAKE) $(MAKESILENT) -f esp-idf/soc/CMakeFiles/__idf_soc.dir/build.make esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/timer_periph.c.i
.PHONY : esp32s3/timer_periph.c.i

esp32s3/timer_periph.s: esp32s3/timer_periph.c.s
.PHONY : esp32s3/timer_periph.s

# target to generate assembly for a file
esp32s3/timer_periph.c.s:
	cd /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/bootloader && $(MAKE) $(MAKESILENT) -f esp-idf/soc/CMakeFiles/__idf_soc.dir/build.make esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/timer_periph.c.s
.PHONY : esp32s3/timer_periph.c.s

esp32s3/touch_sensor_periph.obj: esp32s3/touch_sensor_periph.c.obj
.PHONY : esp32s3/touch_sensor_periph.obj

# target to build an object file
esp32s3/touch_sensor_periph.c.obj:
	cd /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/bootloader && $(MAKE) $(MAKESILENT) -f esp-idf/soc/CMakeFiles/__idf_soc.dir/build.make esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/touch_sensor_periph.c.obj
.PHONY : esp32s3/touch_sensor_periph.c.obj

esp32s3/touch_sensor_periph.i: esp32s3/touch_sensor_periph.c.i
.PHONY : esp32s3/touch_sensor_periph.i

# target to preprocess a source file
esp32s3/touch_sensor_periph.c.i:
	cd /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/bootloader && $(MAKE) $(MAKESILENT) -f esp-idf/soc/CMakeFiles/__idf_soc.dir/build.make esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/touch_sensor_periph.c.i
.PHONY : esp32s3/touch_sensor_periph.c.i

esp32s3/touch_sensor_periph.s: esp32s3/touch_sensor_periph.c.s
.PHONY : esp32s3/touch_sensor_periph.s

# target to generate assembly for a file
esp32s3/touch_sensor_periph.c.s:
	cd /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/bootloader && $(MAKE) $(MAKESILENT) -f esp-idf/soc/CMakeFiles/__idf_soc.dir/build.make esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/touch_sensor_periph.c.s
.PHONY : esp32s3/touch_sensor_periph.c.s

esp32s3/twai_periph.obj: esp32s3/twai_periph.c.obj
.PHONY : esp32s3/twai_periph.obj

# target to build an object file
esp32s3/twai_periph.c.obj:
	cd /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/bootloader && $(MAKE) $(MAKESILENT) -f esp-idf/soc/CMakeFiles/__idf_soc.dir/build.make esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/twai_periph.c.obj
.PHONY : esp32s3/twai_periph.c.obj

esp32s3/twai_periph.i: esp32s3/twai_periph.c.i
.PHONY : esp32s3/twai_periph.i

# target to preprocess a source file
esp32s3/twai_periph.c.i:
	cd /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/bootloader && $(MAKE) $(MAKESILENT) -f esp-idf/soc/CMakeFiles/__idf_soc.dir/build.make esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/twai_periph.c.i
.PHONY : esp32s3/twai_periph.c.i

esp32s3/twai_periph.s: esp32s3/twai_periph.c.s
.PHONY : esp32s3/twai_periph.s

# target to generate assembly for a file
esp32s3/twai_periph.c.s:
	cd /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/bootloader && $(MAKE) $(MAKESILENT) -f esp-idf/soc/CMakeFiles/__idf_soc.dir/build.make esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/twai_periph.c.s
.PHONY : esp32s3/twai_periph.c.s

esp32s3/uart_periph.obj: esp32s3/uart_periph.c.obj
.PHONY : esp32s3/uart_periph.obj

# target to build an object file
esp32s3/uart_periph.c.obj:
	cd /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/bootloader && $(MAKE) $(MAKESILENT) -f esp-idf/soc/CMakeFiles/__idf_soc.dir/build.make esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/uart_periph.c.obj
.PHONY : esp32s3/uart_periph.c.obj

esp32s3/uart_periph.i: esp32s3/uart_periph.c.i
.PHONY : esp32s3/uart_periph.i

# target to preprocess a source file
esp32s3/uart_periph.c.i:
	cd /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/bootloader && $(MAKE) $(MAKESILENT) -f esp-idf/soc/CMakeFiles/__idf_soc.dir/build.make esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/uart_periph.c.i
.PHONY : esp32s3/uart_periph.c.i

esp32s3/uart_periph.s: esp32s3/uart_periph.c.s
.PHONY : esp32s3/uart_periph.s

# target to generate assembly for a file
esp32s3/uart_periph.c.s:
	cd /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/bootloader && $(MAKE) $(MAKESILENT) -f esp-idf/soc/CMakeFiles/__idf_soc.dir/build.make esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/uart_periph.c.s
.PHONY : esp32s3/uart_periph.c.s

esp32s3/usb_dwc_periph.obj: esp32s3/usb_dwc_periph.c.obj
.PHONY : esp32s3/usb_dwc_periph.obj

# target to build an object file
esp32s3/usb_dwc_periph.c.obj:
	cd /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/bootloader && $(MAKE) $(MAKESILENT) -f esp-idf/soc/CMakeFiles/__idf_soc.dir/build.make esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/usb_dwc_periph.c.obj
.PHONY : esp32s3/usb_dwc_periph.c.obj

esp32s3/usb_dwc_periph.i: esp32s3/usb_dwc_periph.c.i
.PHONY : esp32s3/usb_dwc_periph.i

# target to preprocess a source file
esp32s3/usb_dwc_periph.c.i:
	cd /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/bootloader && $(MAKE) $(MAKESILENT) -f esp-idf/soc/CMakeFiles/__idf_soc.dir/build.make esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/usb_dwc_periph.c.i
.PHONY : esp32s3/usb_dwc_periph.c.i

esp32s3/usb_dwc_periph.s: esp32s3/usb_dwc_periph.c.s
.PHONY : esp32s3/usb_dwc_periph.s

# target to generate assembly for a file
esp32s3/usb_dwc_periph.c.s:
	cd /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/bootloader && $(MAKE) $(MAKESILENT) -f esp-idf/soc/CMakeFiles/__idf_soc.dir/build.make esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/usb_dwc_periph.c.s
.PHONY : esp32s3/usb_dwc_periph.c.s

esp32s3/usb_periph.obj: esp32s3/usb_periph.c.obj
.PHONY : esp32s3/usb_periph.obj

# target to build an object file
esp32s3/usb_periph.c.obj:
	cd /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/bootloader && $(MAKE) $(MAKESILENT) -f esp-idf/soc/CMakeFiles/__idf_soc.dir/build.make esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/usb_periph.c.obj
.PHONY : esp32s3/usb_periph.c.obj

esp32s3/usb_periph.i: esp32s3/usb_periph.c.i
.PHONY : esp32s3/usb_periph.i

# target to preprocess a source file
esp32s3/usb_periph.c.i:
	cd /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/bootloader && $(MAKE) $(MAKESILENT) -f esp-idf/soc/CMakeFiles/__idf_soc.dir/build.make esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/usb_periph.c.i
.PHONY : esp32s3/usb_periph.c.i

esp32s3/usb_periph.s: esp32s3/usb_periph.c.s
.PHONY : esp32s3/usb_periph.s

# target to generate assembly for a file
esp32s3/usb_periph.c.s:
	cd /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/bootloader && $(MAKE) $(MAKESILENT) -f esp-idf/soc/CMakeFiles/__idf_soc.dir/build.make esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/usb_periph.c.s
.PHONY : esp32s3/usb_periph.c.s

lldesc.obj: lldesc.c.obj
.PHONY : lldesc.obj

# target to build an object file
lldesc.c.obj:
	cd /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/bootloader && $(MAKE) $(MAKESILENT) -f esp-idf/soc/CMakeFiles/__idf_soc.dir/build.make esp-idf/soc/CMakeFiles/__idf_soc.dir/lldesc.c.obj
.PHONY : lldesc.c.obj

lldesc.i: lldesc.c.i
.PHONY : lldesc.i

# target to preprocess a source file
lldesc.c.i:
	cd /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/bootloader && $(MAKE) $(MAKESILENT) -f esp-idf/soc/CMakeFiles/__idf_soc.dir/build.make esp-idf/soc/CMakeFiles/__idf_soc.dir/lldesc.c.i
.PHONY : lldesc.c.i

lldesc.s: lldesc.c.s
.PHONY : lldesc.s

# target to generate assembly for a file
lldesc.c.s:
	cd /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/bootloader && $(MAKE) $(MAKESILENT) -f esp-idf/soc/CMakeFiles/__idf_soc.dir/build.make esp-idf/soc/CMakeFiles/__idf_soc.dir/lldesc.c.s
.PHONY : lldesc.c.s

# Help Target
help:
	@echo "The following are some of the valid targets for this Makefile:"
	@echo "... all (the default if no target is provided)"
	@echo "... clean"
	@echo "... depend"
	@echo "... edit_cache"
	@echo "... rebuild_cache"
	@echo "... __idf_soc"
	@echo "... dport_access_common.obj"
	@echo "... dport_access_common.i"
	@echo "... dport_access_common.s"
	@echo "... esp32s3/adc_periph.obj"
	@echo "... esp32s3/adc_periph.i"
	@echo "... esp32s3/adc_periph.s"
	@echo "... esp32s3/dedic_gpio_periph.obj"
	@echo "... esp32s3/dedic_gpio_periph.i"
	@echo "... esp32s3/dedic_gpio_periph.s"
	@echo "... esp32s3/gdma_periph.obj"
	@echo "... esp32s3/gdma_periph.i"
	@echo "... esp32s3/gdma_periph.s"
	@echo "... esp32s3/gpio_periph.obj"
	@echo "... esp32s3/gpio_periph.i"
	@echo "... esp32s3/gpio_periph.s"
	@echo "... esp32s3/i2c_periph.obj"
	@echo "... esp32s3/i2c_periph.i"
	@echo "... esp32s3/i2c_periph.s"
	@echo "... esp32s3/i2s_periph.obj"
	@echo "... esp32s3/i2s_periph.i"
	@echo "... esp32s3/i2s_periph.s"
	@echo "... esp32s3/interrupts.obj"
	@echo "... esp32s3/interrupts.i"
	@echo "... esp32s3/interrupts.s"
	@echo "... esp32s3/lcd_periph.obj"
	@echo "... esp32s3/lcd_periph.i"
	@echo "... esp32s3/lcd_periph.s"
	@echo "... esp32s3/ledc_periph.obj"
	@echo "... esp32s3/ledc_periph.i"
	@echo "... esp32s3/ledc_periph.s"
	@echo "... esp32s3/mcpwm_periph.obj"
	@echo "... esp32s3/mcpwm_periph.i"
	@echo "... esp32s3/mcpwm_periph.s"
	@echo "... esp32s3/mpi_periph.obj"
	@echo "... esp32s3/mpi_periph.i"
	@echo "... esp32s3/mpi_periph.s"
	@echo "... esp32s3/pcnt_periph.obj"
	@echo "... esp32s3/pcnt_periph.i"
	@echo "... esp32s3/pcnt_periph.s"
	@echo "... esp32s3/rmt_periph.obj"
	@echo "... esp32s3/rmt_periph.i"
	@echo "... esp32s3/rmt_periph.s"
	@echo "... esp32s3/rtc_io_periph.obj"
	@echo "... esp32s3/rtc_io_periph.i"
	@echo "... esp32s3/rtc_io_periph.s"
	@echo "... esp32s3/sdm_periph.obj"
	@echo "... esp32s3/sdm_periph.i"
	@echo "... esp32s3/sdm_periph.s"
	@echo "... esp32s3/sdmmc_periph.obj"
	@echo "... esp32s3/sdmmc_periph.i"
	@echo "... esp32s3/sdmmc_periph.s"
	@echo "... esp32s3/spi_periph.obj"
	@echo "... esp32s3/spi_periph.i"
	@echo "... esp32s3/spi_periph.s"
	@echo "... esp32s3/temperature_sensor_periph.obj"
	@echo "... esp32s3/temperature_sensor_periph.i"
	@echo "... esp32s3/temperature_sensor_periph.s"
	@echo "... esp32s3/timer_periph.obj"
	@echo "... esp32s3/timer_periph.i"
	@echo "... esp32s3/timer_periph.s"
	@echo "... esp32s3/touch_sensor_periph.obj"
	@echo "... esp32s3/touch_sensor_periph.i"
	@echo "... esp32s3/touch_sensor_periph.s"
	@echo "... esp32s3/twai_periph.obj"
	@echo "... esp32s3/twai_periph.i"
	@echo "... esp32s3/twai_periph.s"
	@echo "... esp32s3/uart_periph.obj"
	@echo "... esp32s3/uart_periph.i"
	@echo "... esp32s3/uart_periph.s"
	@echo "... esp32s3/usb_dwc_periph.obj"
	@echo "... esp32s3/usb_dwc_periph.i"
	@echo "... esp32s3/usb_dwc_periph.s"
	@echo "... esp32s3/usb_periph.obj"
	@echo "... esp32s3/usb_periph.i"
	@echo "... esp32s3/usb_periph.s"
	@echo "... lldesc.obj"
	@echo "... lldesc.i"
	@echo "... lldesc.s"
.PHONY : help



#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	cd /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/bootloader && $(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system

