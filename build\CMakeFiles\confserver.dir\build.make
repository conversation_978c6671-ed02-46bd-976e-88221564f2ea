# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.22

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build

# Utility rule file for confserver.

# Include any custom commands dependencies for this target.
include CMakeFiles/confserver.dir/compiler_depend.make

# Include the progress variables for this target.
include CMakeFiles/confserver.dir/progress.make

CMakeFiles/confserver:
	/home/<USER>/.espressif/python_env/idf5.3_py3.11_env/bin/python /home/<USER>/esp/esp-idf-v5.3/tools/kconfig_new/prepare_kconfig_files.py --list-separator=semicolon --env-file /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/config.env
	/home/<USER>/.espressif/python_env/idf5.3_py3.11_env/bin/python -m kconfserver --env-file /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/config.env --kconfig /home/<USER>/esp/esp-idf-v5.3/Kconfig --sdkconfig-rename /home/<USER>/esp/esp-idf-v5.3/sdkconfig.rename --config /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/sdkconfig

confserver: CMakeFiles/confserver
confserver: CMakeFiles/confserver.dir/build.make
.PHONY : confserver

# Rule to build all files generated by this target.
CMakeFiles/confserver.dir/build: confserver
.PHONY : CMakeFiles/confserver.dir/build

CMakeFiles/confserver.dir/clean:
	$(CMAKE_COMMAND) -P CMakeFiles/confserver.dir/cmake_clean.cmake
.PHONY : CMakeFiles/confserver.dir/clean

CMakeFiles/confserver.dir/depend:
	cd /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles/confserver.dir/DependInfo.cmake --color=$(COLOR)
.PHONY : CMakeFiles/confserver.dir/depend

