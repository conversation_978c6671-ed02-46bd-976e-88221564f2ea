# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.22

# Default target executed when no arguments are given to make.
default_target: all
.PHONY : default_target

# Allow only one "make -f Makefile2" at a time, but pass parallelism.
.NOTPARALLEL:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/esp/esp-idf-v5.3/components/bootloader/subproject

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/bootloader

#=============================================================================
# Targets provided globally by CMake.

# Special rule for the target edit_cache
edit_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "No interactive CMake dialog available..."
	/usr/bin/cmake -E echo No\ interactive\ CMake\ dialog\ available.
.PHONY : edit_cache

# Special rule for the target edit_cache
edit_cache/fast: edit_cache
.PHONY : edit_cache/fast

# Special rule for the target rebuild_cache
rebuild_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Running CMake to regenerate build system..."
	/usr/bin/cmake --regenerate-during-build -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : rebuild_cache

# Special rule for the target rebuild_cache
rebuild_cache/fast: rebuild_cache
.PHONY : rebuild_cache/fast

# The main all target
all: cmake_check_build_system
	cd /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/bootloader && $(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/bootloader/CMakeFiles /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/bootloader/esp-idf/esp_rom//CMakeFiles/progress.marks
	cd /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/bootloader && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 esp-idf/esp_rom/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/bootloader/CMakeFiles 0
.PHONY : all

# The main clean target
clean:
	cd /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/bootloader && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 esp-idf/esp_rom/clean
.PHONY : clean

# The main clean target
clean/fast: clean
.PHONY : clean/fast

# Prepare targets for installation.
preinstall: all
	cd /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/bootloader && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 esp-idf/esp_rom/preinstall
.PHONY : preinstall

# Prepare targets for installation.
preinstall/fast:
	cd /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/bootloader && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 esp-idf/esp_rom/preinstall
.PHONY : preinstall/fast

# clear depends
depend:
	cd /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/bootloader && $(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 1
.PHONY : depend

# Convenience name for target.
esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/rule:
	cd /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/bootloader && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/rule
.PHONY : esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/rule

# Convenience name for target.
__idf_esp_rom: esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/rule
.PHONY : __idf_esp_rom

# fast build rule for target.
__idf_esp_rom/fast:
	cd /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/bootloader && $(MAKE) $(MAKESILENT) -f esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/build.make esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/build
.PHONY : __idf_esp_rom/fast

patches/esp_rom_cache_esp32s2_esp32s3.obj: patches/esp_rom_cache_esp32s2_esp32s3.c.obj
.PHONY : patches/esp_rom_cache_esp32s2_esp32s3.obj

# target to build an object file
patches/esp_rom_cache_esp32s2_esp32s3.c.obj:
	cd /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/bootloader && $(MAKE) $(MAKESILENT) -f esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/build.make esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_cache_esp32s2_esp32s3.c.obj
.PHONY : patches/esp_rom_cache_esp32s2_esp32s3.c.obj

patches/esp_rom_cache_esp32s2_esp32s3.i: patches/esp_rom_cache_esp32s2_esp32s3.c.i
.PHONY : patches/esp_rom_cache_esp32s2_esp32s3.i

# target to preprocess a source file
patches/esp_rom_cache_esp32s2_esp32s3.c.i:
	cd /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/bootloader && $(MAKE) $(MAKESILENT) -f esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/build.make esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_cache_esp32s2_esp32s3.c.i
.PHONY : patches/esp_rom_cache_esp32s2_esp32s3.c.i

patches/esp_rom_cache_esp32s2_esp32s3.s: patches/esp_rom_cache_esp32s2_esp32s3.c.s
.PHONY : patches/esp_rom_cache_esp32s2_esp32s3.s

# target to generate assembly for a file
patches/esp_rom_cache_esp32s2_esp32s3.c.s:
	cd /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/bootloader && $(MAKE) $(MAKESILENT) -f esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/build.make esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_cache_esp32s2_esp32s3.c.s
.PHONY : patches/esp_rom_cache_esp32s2_esp32s3.c.s

patches/esp_rom_cache_writeback_esp32s3.obj: patches/esp_rom_cache_writeback_esp32s3.S.obj
.PHONY : patches/esp_rom_cache_writeback_esp32s3.obj

# target to build an object file
patches/esp_rom_cache_writeback_esp32s3.S.obj:
	cd /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/bootloader && $(MAKE) $(MAKESILENT) -f esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/build.make esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_cache_writeback_esp32s3.S.obj
.PHONY : patches/esp_rom_cache_writeback_esp32s3.S.obj

patches/esp_rom_crc.obj: patches/esp_rom_crc.c.obj
.PHONY : patches/esp_rom_crc.obj

# target to build an object file
patches/esp_rom_crc.c.obj:
	cd /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/bootloader && $(MAKE) $(MAKESILENT) -f esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/build.make esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_crc.c.obj
.PHONY : patches/esp_rom_crc.c.obj

patches/esp_rom_crc.i: patches/esp_rom_crc.c.i
.PHONY : patches/esp_rom_crc.i

# target to preprocess a source file
patches/esp_rom_crc.c.i:
	cd /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/bootloader && $(MAKE) $(MAKESILENT) -f esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/build.make esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_crc.c.i
.PHONY : patches/esp_rom_crc.c.i

patches/esp_rom_crc.s: patches/esp_rom_crc.c.s
.PHONY : patches/esp_rom_crc.s

# target to generate assembly for a file
patches/esp_rom_crc.c.s:
	cd /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/bootloader && $(MAKE) $(MAKESILENT) -f esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/build.make esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_crc.c.s
.PHONY : patches/esp_rom_crc.c.s

patches/esp_rom_efuse.obj: patches/esp_rom_efuse.c.obj
.PHONY : patches/esp_rom_efuse.obj

# target to build an object file
patches/esp_rom_efuse.c.obj:
	cd /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/bootloader && $(MAKE) $(MAKESILENT) -f esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/build.make esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_efuse.c.obj
.PHONY : patches/esp_rom_efuse.c.obj

patches/esp_rom_efuse.i: patches/esp_rom_efuse.c.i
.PHONY : patches/esp_rom_efuse.i

# target to preprocess a source file
patches/esp_rom_efuse.c.i:
	cd /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/bootloader && $(MAKE) $(MAKESILENT) -f esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/build.make esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_efuse.c.i
.PHONY : patches/esp_rom_efuse.c.i

patches/esp_rom_efuse.s: patches/esp_rom_efuse.c.s
.PHONY : patches/esp_rom_efuse.s

# target to generate assembly for a file
patches/esp_rom_efuse.c.s:
	cd /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/bootloader && $(MAKE) $(MAKESILENT) -f esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/build.make esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_efuse.c.s
.PHONY : patches/esp_rom_efuse.c.s

patches/esp_rom_longjmp.obj: patches/esp_rom_longjmp.S.obj
.PHONY : patches/esp_rom_longjmp.obj

# target to build an object file
patches/esp_rom_longjmp.S.obj:
	cd /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/bootloader && $(MAKE) $(MAKESILENT) -f esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/build.make esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_longjmp.S.obj
.PHONY : patches/esp_rom_longjmp.S.obj

patches/esp_rom_spiflash.obj: patches/esp_rom_spiflash.c.obj
.PHONY : patches/esp_rom_spiflash.obj

# target to build an object file
patches/esp_rom_spiflash.c.obj:
	cd /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/bootloader && $(MAKE) $(MAKESILENT) -f esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/build.make esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_spiflash.c.obj
.PHONY : patches/esp_rom_spiflash.c.obj

patches/esp_rom_spiflash.i: patches/esp_rom_spiflash.c.i
.PHONY : patches/esp_rom_spiflash.i

# target to preprocess a source file
patches/esp_rom_spiflash.c.i:
	cd /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/bootloader && $(MAKE) $(MAKESILENT) -f esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/build.make esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_spiflash.c.i
.PHONY : patches/esp_rom_spiflash.c.i

patches/esp_rom_spiflash.s: patches/esp_rom_spiflash.c.s
.PHONY : patches/esp_rom_spiflash.s

# target to generate assembly for a file
patches/esp_rom_spiflash.c.s:
	cd /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/bootloader && $(MAKE) $(MAKESILENT) -f esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/build.make esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_spiflash.c.s
.PHONY : patches/esp_rom_spiflash.c.s

patches/esp_rom_sys.obj: patches/esp_rom_sys.c.obj
.PHONY : patches/esp_rom_sys.obj

# target to build an object file
patches/esp_rom_sys.c.obj:
	cd /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/bootloader && $(MAKE) $(MAKESILENT) -f esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/build.make esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_sys.c.obj
.PHONY : patches/esp_rom_sys.c.obj

patches/esp_rom_sys.i: patches/esp_rom_sys.c.i
.PHONY : patches/esp_rom_sys.i

# target to preprocess a source file
patches/esp_rom_sys.c.i:
	cd /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/bootloader && $(MAKE) $(MAKESILENT) -f esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/build.make esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_sys.c.i
.PHONY : patches/esp_rom_sys.c.i

patches/esp_rom_sys.s: patches/esp_rom_sys.c.s
.PHONY : patches/esp_rom_sys.s

# target to generate assembly for a file
patches/esp_rom_sys.c.s:
	cd /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/bootloader && $(MAKE) $(MAKESILENT) -f esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/build.make esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_sys.c.s
.PHONY : patches/esp_rom_sys.c.s

patches/esp_rom_systimer.obj: patches/esp_rom_systimer.c.obj
.PHONY : patches/esp_rom_systimer.obj

# target to build an object file
patches/esp_rom_systimer.c.obj:
	cd /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/bootloader && $(MAKE) $(MAKESILENT) -f esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/build.make esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_systimer.c.obj
.PHONY : patches/esp_rom_systimer.c.obj

patches/esp_rom_systimer.i: patches/esp_rom_systimer.c.i
.PHONY : patches/esp_rom_systimer.i

# target to preprocess a source file
patches/esp_rom_systimer.c.i:
	cd /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/bootloader && $(MAKE) $(MAKESILENT) -f esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/build.make esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_systimer.c.i
.PHONY : patches/esp_rom_systimer.c.i

patches/esp_rom_systimer.s: patches/esp_rom_systimer.c.s
.PHONY : patches/esp_rom_systimer.s

# target to generate assembly for a file
patches/esp_rom_systimer.c.s:
	cd /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/bootloader && $(MAKE) $(MAKESILENT) -f esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/build.make esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_systimer.c.s
.PHONY : patches/esp_rom_systimer.c.s

patches/esp_rom_uart.obj: patches/esp_rom_uart.c.obj
.PHONY : patches/esp_rom_uart.obj

# target to build an object file
patches/esp_rom_uart.c.obj:
	cd /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/bootloader && $(MAKE) $(MAKESILENT) -f esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/build.make esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_uart.c.obj
.PHONY : patches/esp_rom_uart.c.obj

patches/esp_rom_uart.i: patches/esp_rom_uart.c.i
.PHONY : patches/esp_rom_uart.i

# target to preprocess a source file
patches/esp_rom_uart.c.i:
	cd /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/bootloader && $(MAKE) $(MAKESILENT) -f esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/build.make esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_uart.c.i
.PHONY : patches/esp_rom_uart.c.i

patches/esp_rom_uart.s: patches/esp_rom_uart.c.s
.PHONY : patches/esp_rom_uart.s

# target to generate assembly for a file
patches/esp_rom_uart.c.s:
	cd /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/bootloader && $(MAKE) $(MAKESILENT) -f esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/build.make esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_uart.c.s
.PHONY : patches/esp_rom_uart.c.s

patches/esp_rom_wdt.obj: patches/esp_rom_wdt.c.obj
.PHONY : patches/esp_rom_wdt.obj

# target to build an object file
patches/esp_rom_wdt.c.obj:
	cd /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/bootloader && $(MAKE) $(MAKESILENT) -f esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/build.make esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_wdt.c.obj
.PHONY : patches/esp_rom_wdt.c.obj

patches/esp_rom_wdt.i: patches/esp_rom_wdt.c.i
.PHONY : patches/esp_rom_wdt.i

# target to preprocess a source file
patches/esp_rom_wdt.c.i:
	cd /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/bootloader && $(MAKE) $(MAKESILENT) -f esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/build.make esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_wdt.c.i
.PHONY : patches/esp_rom_wdt.c.i

patches/esp_rom_wdt.s: patches/esp_rom_wdt.c.s
.PHONY : patches/esp_rom_wdt.s

# target to generate assembly for a file
patches/esp_rom_wdt.c.s:
	cd /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/bootloader && $(MAKE) $(MAKESILENT) -f esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/build.make esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_wdt.c.s
.PHONY : patches/esp_rom_wdt.c.s

# Help Target
help:
	@echo "The following are some of the valid targets for this Makefile:"
	@echo "... all (the default if no target is provided)"
	@echo "... clean"
	@echo "... depend"
	@echo "... edit_cache"
	@echo "... rebuild_cache"
	@echo "... __idf_esp_rom"
	@echo "... patches/esp_rom_cache_esp32s2_esp32s3.obj"
	@echo "... patches/esp_rom_cache_esp32s2_esp32s3.i"
	@echo "... patches/esp_rom_cache_esp32s2_esp32s3.s"
	@echo "... patches/esp_rom_cache_writeback_esp32s3.obj"
	@echo "... patches/esp_rom_crc.obj"
	@echo "... patches/esp_rom_crc.i"
	@echo "... patches/esp_rom_crc.s"
	@echo "... patches/esp_rom_efuse.obj"
	@echo "... patches/esp_rom_efuse.i"
	@echo "... patches/esp_rom_efuse.s"
	@echo "... patches/esp_rom_longjmp.obj"
	@echo "... patches/esp_rom_spiflash.obj"
	@echo "... patches/esp_rom_spiflash.i"
	@echo "... patches/esp_rom_spiflash.s"
	@echo "... patches/esp_rom_sys.obj"
	@echo "... patches/esp_rom_sys.i"
	@echo "... patches/esp_rom_sys.s"
	@echo "... patches/esp_rom_systimer.obj"
	@echo "... patches/esp_rom_systimer.i"
	@echo "... patches/esp_rom_systimer.s"
	@echo "... patches/esp_rom_uart.obj"
	@echo "... patches/esp_rom_uart.i"
	@echo "... patches/esp_rom_uart.s"
	@echo "... patches/esp_rom_wdt.obj"
	@echo "... patches/esp_rom_wdt.i"
	@echo "... patches/esp_rom_wdt.s"
.PHONY : help



#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	cd /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/bootloader && $(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system

