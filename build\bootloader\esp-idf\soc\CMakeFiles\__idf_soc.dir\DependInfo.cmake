
# Consider dependencies only in project.
set(CMAKE_DEPENDS_IN_PROJECT_ONLY OFF)

# The set of languages for which implicit dependencies are needed:
set(CMAKE_DEPENDS_LANGUAGES
  )

# The set of dependency files which are needed:
set(CMAKE_DEPENDS_DEPENDENCY_FILES
  "/home/<USER>/esp/esp-idf-v5.3/components/soc/dport_access_common.c" "esp-idf/soc/CMakeFiles/__idf_soc.dir/dport_access_common.c.obj" "gcc" "esp-idf/soc/CMakeFiles/__idf_soc.dir/dport_access_common.c.obj.d"
  "/home/<USER>/esp/esp-idf-v5.3/components/soc/esp32s3/adc_periph.c" "esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/adc_periph.c.obj" "gcc" "esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/adc_periph.c.obj.d"
  "/home/<USER>/esp/esp-idf-v5.3/components/soc/esp32s3/dedic_gpio_periph.c" "esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/dedic_gpio_periph.c.obj" "gcc" "esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/dedic_gpio_periph.c.obj.d"
  "/home/<USER>/esp/esp-idf-v5.3/components/soc/esp32s3/gdma_periph.c" "esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/gdma_periph.c.obj" "gcc" "esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/gdma_periph.c.obj.d"
  "/home/<USER>/esp/esp-idf-v5.3/components/soc/esp32s3/gpio_periph.c" "esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/gpio_periph.c.obj" "gcc" "esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/gpio_periph.c.obj.d"
  "/home/<USER>/esp/esp-idf-v5.3/components/soc/esp32s3/i2c_periph.c" "esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/i2c_periph.c.obj" "gcc" "esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/i2c_periph.c.obj.d"
  "/home/<USER>/esp/esp-idf-v5.3/components/soc/esp32s3/i2s_periph.c" "esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/i2s_periph.c.obj" "gcc" "esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/i2s_periph.c.obj.d"
  "/home/<USER>/esp/esp-idf-v5.3/components/soc/esp32s3/interrupts.c" "esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/interrupts.c.obj" "gcc" "esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/interrupts.c.obj.d"
  "/home/<USER>/esp/esp-idf-v5.3/components/soc/esp32s3/lcd_periph.c" "esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/lcd_periph.c.obj" "gcc" "esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/lcd_periph.c.obj.d"
  "/home/<USER>/esp/esp-idf-v5.3/components/soc/esp32s3/ledc_periph.c" "esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/ledc_periph.c.obj" "gcc" "esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/ledc_periph.c.obj.d"
  "/home/<USER>/esp/esp-idf-v5.3/components/soc/esp32s3/mcpwm_periph.c" "esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/mcpwm_periph.c.obj" "gcc" "esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/mcpwm_periph.c.obj.d"
  "/home/<USER>/esp/esp-idf-v5.3/components/soc/esp32s3/mpi_periph.c" "esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/mpi_periph.c.obj" "gcc" "esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/mpi_periph.c.obj.d"
  "/home/<USER>/esp/esp-idf-v5.3/components/soc/esp32s3/pcnt_periph.c" "esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/pcnt_periph.c.obj" "gcc" "esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/pcnt_periph.c.obj.d"
  "/home/<USER>/esp/esp-idf-v5.3/components/soc/esp32s3/rmt_periph.c" "esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/rmt_periph.c.obj" "gcc" "esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/rmt_periph.c.obj.d"
  "/home/<USER>/esp/esp-idf-v5.3/components/soc/esp32s3/rtc_io_periph.c" "esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/rtc_io_periph.c.obj" "gcc" "esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/rtc_io_periph.c.obj.d"
  "/home/<USER>/esp/esp-idf-v5.3/components/soc/esp32s3/sdm_periph.c" "esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/sdm_periph.c.obj" "gcc" "esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/sdm_periph.c.obj.d"
  "/home/<USER>/esp/esp-idf-v5.3/components/soc/esp32s3/sdmmc_periph.c" "esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/sdmmc_periph.c.obj" "gcc" "esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/sdmmc_periph.c.obj.d"
  "/home/<USER>/esp/esp-idf-v5.3/components/soc/esp32s3/spi_periph.c" "esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/spi_periph.c.obj" "gcc" "esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/spi_periph.c.obj.d"
  "/home/<USER>/esp/esp-idf-v5.3/components/soc/esp32s3/temperature_sensor_periph.c" "esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/temperature_sensor_periph.c.obj" "gcc" "esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/temperature_sensor_periph.c.obj.d"
  "/home/<USER>/esp/esp-idf-v5.3/components/soc/esp32s3/timer_periph.c" "esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/timer_periph.c.obj" "gcc" "esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/timer_periph.c.obj.d"
  "/home/<USER>/esp/esp-idf-v5.3/components/soc/esp32s3/touch_sensor_periph.c" "esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/touch_sensor_periph.c.obj" "gcc" "esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/touch_sensor_periph.c.obj.d"
  "/home/<USER>/esp/esp-idf-v5.3/components/soc/esp32s3/twai_periph.c" "esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/twai_periph.c.obj" "gcc" "esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/twai_periph.c.obj.d"
  "/home/<USER>/esp/esp-idf-v5.3/components/soc/esp32s3/uart_periph.c" "esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/uart_periph.c.obj" "gcc" "esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/uart_periph.c.obj.d"
  "/home/<USER>/esp/esp-idf-v5.3/components/soc/esp32s3/usb_dwc_periph.c" "esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/usb_dwc_periph.c.obj" "gcc" "esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/usb_dwc_periph.c.obj.d"
  "/home/<USER>/esp/esp-idf-v5.3/components/soc/esp32s3/usb_periph.c" "esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/usb_periph.c.obj" "gcc" "esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/usb_periph.c.obj.d"
  "/home/<USER>/esp/esp-idf-v5.3/components/soc/lldesc.c" "esp-idf/soc/CMakeFiles/__idf_soc.dir/lldesc.c.obj" "gcc" "esp-idf/soc/CMakeFiles/__idf_soc.dir/lldesc.c.obj.d"
  )

# Targets to which this target links.
set(CMAKE_TARGET_LINKED_INFO_FILES
  "/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/bootloader/esp-idf/log/CMakeFiles/__idf_log.dir/DependInfo.cmake"
  "/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/bootloader/esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/DependInfo.cmake"
  "/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/bootloader/esp-idf/esp_common/CMakeFiles/__idf_esp_common.dir/DependInfo.cmake"
  "/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/bootloader/esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/DependInfo.cmake"
  "/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/bootloader/esp-idf/xtensa/CMakeFiles/__idf_xtensa.dir/DependInfo.cmake"
  "/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/bootloader/esp-idf/hal/CMakeFiles/__idf_hal.dir/DependInfo.cmake"
  "/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/bootloader/esp-idf/efuse/CMakeFiles/__idf_efuse.dir/DependInfo.cmake"
  "/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/bootloader/esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/DependInfo.cmake"
  "/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/bootloader/esp-idf/spi_flash/CMakeFiles/__idf_spi_flash.dir/DependInfo.cmake"
  "/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/bootloader/esp-idf/esp_system/CMakeFiles/__idf_esp_system.dir/DependInfo.cmake"
  "/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/bootloader/esp-idf/micro-ecc/CMakeFiles/__idf_micro-ecc.dir/DependInfo.cmake"
  "/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/bootloader/esp-idf/esp_bootloader_format/CMakeFiles/__idf_esp_bootloader_format.dir/DependInfo.cmake"
  )

# Fortran module output directory.
set(CMAKE_Fortran_TARGET_MODULE_DIR "")
