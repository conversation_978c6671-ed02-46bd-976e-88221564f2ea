#include <esp_log.h>
#include <freertos/FreeRTOS.h>
#include <freertos/task.h>
#include "iot/thing_manager.h"
#include "iot/register_things.h"

#define TAG "FanTest"

/**
 * 风扇功能测试程序
 * 这个程序可以帮助您验证风扇模块是否正常工作
 */

void test_fan_basic_functionality() {
    ESP_LOGI(TAG, "开始基础风扇功能测试...");
    
    // 获取ThingManager实例
    auto& thing_manager = iot::ThingManager::GetInstance();
    
    // 查找风扇设备
    auto fan = thing_manager.GetThingByName("Fan");
    if (!fan) {
        ESP_LOGE(TAG, "未找到风扇设备！请检查风扇模块是否正确注册");
        return;
    }
    
    ESP_LOGI(TAG, "找到风扇设备: %s", fan->name().c_str());
    
    // 测试风扇开关
    ESP_LOGI(TAG, "测试1: 开启风扇");
    fan->InvokeMethod("TurnOn", iot::ParameterList());
    vTaskDelay(pdMS_TO_TICKS(3000));  // 等待3秒
    
    ESP_LOGI(TAG, "测试2: 设置风速为30%");
    iot::ParameterList speed_params;
    speed_params.AddParameter(iot::Parameter("speed", "30", iot::kValueTypeNumber));
    fan->InvokeMethod("SetSpeed", speed_params);
    vTaskDelay(pdMS_TO_TICKS(3000));  // 等待3秒
    
    ESP_LOGI(TAG, "测试3: 设置风速为80%");
    speed_params.Clear();
    speed_params.AddParameter(iot::Parameter("speed", "80", iot::kValueTypeNumber));
    fan->InvokeMethod("SetSpeed", speed_params);
    vTaskDelay(pdMS_TO_TICKS(3000));  // 等待3秒
    
    ESP_LOGI(TAG, "测试4: 关闭风扇");
    fan->InvokeMethod("TurnOff", iot::ParameterList());
    vTaskDelay(pdMS_TO_TICKS(1000));  // 等待1秒
    
    ESP_LOGI(TAG, "基础风扇功能测试完成");
}

void test_fan_timer_functionality() {
    ESP_LOGI(TAG, "开始定时器功能测试...");
    
    auto& thing_manager = iot::ThingManager::GetInstance();
    auto fan = thing_manager.GetThingByName("Fan");
    if (!fan) {
        ESP_LOGE(TAG, "未找到风扇设备！");
        return;
    }
    
    ESP_LOGI(TAG, "测试定时器功能: 设置1分钟后自动关闭");
    iot::ParameterList timer_params;
    timer_params.AddParameter(iot::Parameter("minutes", "1", iot::kValueTypeNumber));
    fan->InvokeMethod("SetTimer", timer_params);
    
    // 等待65秒，观察风扇是否自动关闭
    for (int i = 0; i < 65; i++) {
        vTaskDelay(pdMS_TO_TICKS(1000));
        if (i % 10 == 0) {
            ESP_LOGI(TAG, "等待中... %d秒", i);
        }
    }
    
    ESP_LOGI(TAG, "定时器功能测试完成");
}

void print_fan_status() {
    ESP_LOGI(TAG, "获取风扇状态...");
    
    auto& thing_manager = iot::ThingManager::GetInstance();
    auto fan = thing_manager.GetThingByName("Fan");
    if (!fan) {
        ESP_LOGE(TAG, "未找到风扇设备！");
        return;
    }
    
    // 获取风扇状态JSON
    std::string status = fan->GetStateJson();
    ESP_LOGI(TAG, "风扇状态: %s", status.c_str());
}

extern "C" void app_main(void) {
    ESP_LOGI(TAG, "风扇测试程序启动");
    
    // 注册所有设备
    iot::RegisterAllThings();
    
    // 等待一段时间让设备初始化完成
    vTaskDelay(pdMS_TO_TICKS(2000));
    
    // 打印风扇状态
    print_fan_status();
    
    // 执行基础功能测试
    test_fan_basic_functionality();
    
    // 打印风扇状态
    print_fan_status();
    
    // 执行定时器功能测试
    test_fan_timer_functionality();
    
    // 最终状态
    print_fan_status();
    
    ESP_LOGI(TAG, "所有测试完成");
    
    // 保持程序运行
    while (true) {
        vTaskDelay(pdMS_TO_TICKS(10000));
    }
}
