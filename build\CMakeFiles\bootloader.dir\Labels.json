{"sources": [{"file": "D:/xiaozhis/fixed_package/fixed_project/build/CMakeFiles/bootloader"}, {"file": "D:/xiaozhis/fixed_package/fixed_project/build/CMakeFiles/bootloader.rule"}, {"file": "D:/xiaozhis/fixed_package/fixed_project/build/CMakeFiles/bootloader-complete.rule"}, {"file": "D:/xiaozhis/fixed_package/fixed_project/build/bootloader-prefix/src/bootloader-stamp/bootloader-build.rule"}, {"file": "D:/xiaozhis/fixed_package/fixed_project/build/bootloader-prefix/src/bootloader-stamp/bootloader-configure.rule"}, {"file": "D:/xiaozhis/fixed_package/fixed_project/build/bootloader-prefix/src/bootloader-stamp/bootloader-download.rule"}, {"file": "D:/xiaozhis/fixed_package/fixed_project/build/bootloader-prefix/src/bootloader-stamp/bootloader-install.rule"}, {"file": "D:/xiaozhis/fixed_package/fixed_project/build/bootloader-prefix/src/bootloader-stamp/bootloader-mkdir.rule"}, {"file": "D:/xiaozhis/fixed_package/fixed_project/build/bootloader-prefix/src/bootloader-stamp/bootloader-patch.rule"}, {"file": "D:/xiaozhis/fixed_package/fixed_project/build/bootloader-prefix/src/bootloader-stamp/bootloader-update.rule"}], "target": {"labels": ["bootloader"], "name": "bootloader"}}