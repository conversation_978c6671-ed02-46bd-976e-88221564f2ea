{"sources": [{"file": "/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles/bootloader"}, {"file": "/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles/bootloader.rule"}, {"file": "/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles/bootloader-complete.rule"}, {"file": "/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/bootloader-prefix/src/bootloader-stamp/bootloader-build.rule"}, {"file": "/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/bootloader-prefix/src/bootloader-stamp/bootloader-configure.rule"}, {"file": "/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/bootloader-prefix/src/bootloader-stamp/bootloader-download.rule"}, {"file": "/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/bootloader-prefix/src/bootloader-stamp/bootloader-install.rule"}, {"file": "/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/bootloader-prefix/src/bootloader-stamp/bootloader-mkdir.rule"}, {"file": "/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/bootloader-prefix/src/bootloader-stamp/bootloader-patch.rule"}, {"file": "/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/bootloader-prefix/src/bootloader-stamp/bootloader-update.rule"}], "target": {"labels": ["bootloader"], "name": "bootloader"}}