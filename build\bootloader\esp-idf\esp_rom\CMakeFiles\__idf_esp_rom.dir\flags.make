# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.22

# compile ASM with /home/<USER>/.espressif/tools/xtensa-esp-elf/esp-13.2.0_20240530/xtensa-esp-elf/bin/xtensa-esp32s3-elf-gcc
# compile C with /home/<USER>/.espressif/tools/xtensa-esp-elf/esp-13.2.0_20240530/xtensa-esp-elf/bin/xtensa-esp32s3-elf-gcc
ASM_DEFINES = -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\"v5.3\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE

ASM_INCLUDES = -I/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/bootloader/config -I/home/<USER>/esp/esp-idf-v5.3/components/esp_rom/include -I/home/<USER>/esp/esp-idf-v5.3/components/esp_rom/include/esp32s3 -I/home/<USER>/esp/esp-idf-v5.3/components/esp_rom/esp32s3 -I/home/<USER>/esp/esp-idf-v5.3/components/log/include -I/home/<USER>/esp/esp-idf-v5.3/components/esp_common/include -I/home/<USER>/esp/esp-idf-v5.3/components/esp_hw_support/include -I/home/<USER>/esp/esp-idf-v5.3/components/esp_hw_support/include/soc -I/home/<USER>/esp/esp-idf-v5.3/components/esp_hw_support/include/soc/esp32s3 -I/home/<USER>/esp/esp-idf-v5.3/components/esp_hw_support/dma/include -I/home/<USER>/esp/esp-idf-v5.3/components/esp_hw_support/ldo/include -I/home/<USER>/esp/esp-idf-v5.3/components/esp_hw_support/port/esp32s3/. -I/home/<USER>/esp/esp-idf-v5.3/components/esp_hw_support/port/esp32s3/include -I/home/<USER>/esp/esp-idf-v5.3/components/newlib/platform_include -I/home/<USER>/esp/esp-idf-v5.3/components/xtensa/esp32s3/include -I/home/<USER>/esp/esp-idf-v5.3/components/xtensa/include -I/home/<USER>/esp/esp-idf-v5.3/components/xtensa/deprecated_include -I/home/<USER>/esp/esp-idf-v5.3/components/soc/include -I/home/<USER>/esp/esp-idf-v5.3/components/soc/esp32s3 -I/home/<USER>/esp/esp-idf-v5.3/components/soc/esp32s3/include -I/home/<USER>/esp/esp-idf-v5.3/components/hal/platform_port/include -I/home/<USER>/esp/esp-idf-v5.3/components/hal/esp32s3/include -I/home/<USER>/esp/esp-idf-v5.3/components/hal/include

ASM_FLAGS = -mlongcalls  -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -O2 -fmacro-prefix-map=/home/<USER>/esp/esp-idf-v5.3/components/bootloader/subproject=. -fmacro-prefix-map=/home/<USER>/esp/esp-idf-v5.3=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector

C_DEFINES = -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\"v5.3\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE

C_INCLUDES = -I/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/bootloader/config -I/home/<USER>/esp/esp-idf-v5.3/components/esp_rom/include -I/home/<USER>/esp/esp-idf-v5.3/components/esp_rom/include/esp32s3 -I/home/<USER>/esp/esp-idf-v5.3/components/esp_rom/esp32s3 -I/home/<USER>/esp/esp-idf-v5.3/components/log/include -I/home/<USER>/esp/esp-idf-v5.3/components/esp_common/include -I/home/<USER>/esp/esp-idf-v5.3/components/esp_hw_support/include -I/home/<USER>/esp/esp-idf-v5.3/components/esp_hw_support/include/soc -I/home/<USER>/esp/esp-idf-v5.3/components/esp_hw_support/include/soc/esp32s3 -I/home/<USER>/esp/esp-idf-v5.3/components/esp_hw_support/dma/include -I/home/<USER>/esp/esp-idf-v5.3/components/esp_hw_support/ldo/include -I/home/<USER>/esp/esp-idf-v5.3/components/esp_hw_support/port/esp32s3/. -I/home/<USER>/esp/esp-idf-v5.3/components/esp_hw_support/port/esp32s3/include -I/home/<USER>/esp/esp-idf-v5.3/components/newlib/platform_include -I/home/<USER>/esp/esp-idf-v5.3/components/xtensa/esp32s3/include -I/home/<USER>/esp/esp-idf-v5.3/components/xtensa/include -I/home/<USER>/esp/esp-idf-v5.3/components/xtensa/deprecated_include -I/home/<USER>/esp/esp-idf-v5.3/components/soc/include -I/home/<USER>/esp/esp-idf-v5.3/components/soc/esp32s3 -I/home/<USER>/esp/esp-idf-v5.3/components/soc/esp32s3/include -I/home/<USER>/esp/esp-idf-v5.3/components/hal/platform_port/include -I/home/<USER>/esp/esp-idf-v5.3/components/hal/esp32s3/include -I/home/<USER>/esp/esp-idf-v5.3/components/hal/include

C_FLAGS = -mlongcalls  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -O2 -fmacro-prefix-map=/home/<USER>/esp/esp-idf-v5.3/components/bootloader/subproject=. -fmacro-prefix-map=/home/<USER>/esp/esp-idf-v5.3=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration

