#ifndef REGISTER_THINGS_H
#define REGISTER_THINGS_H

#include "iot/thing_manager.h"
#include "iot/things/lamp.h"
#include "iot/things/fan.h"
#include "iot/things/temperature.h"
#include <esp_log.h>

#define TAG_REGISTER "RegisterThings"

namespace iot {

/**
 * 注册所有物联网设备到系统
 * 确保所有设备都能被语音命令系统识别
 */
inline void RegisterAllThings() {
    ESP_LOGI(TAG_REGISTER, "开始注册所有物联网设备...");

    // 获取ThingManager单例
    auto& thing_manager = ThingManager::GetInstance();

    // 手动创建并注册风扇模块（确保风扇模块被正确注册）
    auto fan = iot::CreateThing("Fan");
    if (fan) {
        thing_manager.AddThing(fan);
        ESP_LOGI(TAG_REGISTER, "风扇控制模块已手动创建并注册");
    } else {
        ESP_LOGE(TAG_REGISTER, "风扇控制模块创建失败！");
    }

    // 手动创建并注册灯控制模块
    auto lamp = iot::CreateThing("Lamp");
    if (lamp) {
        thing_manager.AddThing(lamp);
        ESP_LOGI(TAG_REGISTER, "灯控制模块已手动创建并注册");
    } else {
        ESP_LOGE(TAG_REGISTER, "灯控制模块创建失败！");
    }

    // 手动创建并注册温度传感器模块
    auto temperature = iot::CreateThing("Temperature");
    if (temperature) {
        thing_manager.AddThing(temperature);
        ESP_LOGI(TAG_REGISTER, "温度传感器模块已手动创建并注册");
    } else {
        ESP_LOGE(TAG_REGISTER, "温度传感器模块创建失败！");
    }

    // 验证所有模块是否正确注册
    ESP_LOGI(TAG_REGISTER, "验证模块注册状态...");

    auto registered_fan = thing_manager.GetThingByName("Fan");
    if (registered_fan) {
        ESP_LOGI(TAG_REGISTER, "✓ 风扇控制模块已成功注册");
    } else {
        ESP_LOGE(TAG_REGISTER, "✗ 风扇控制模块注册失败");
    }

    auto registered_lamp = thing_manager.GetThingByName("Lamp");
    if (registered_lamp) {
        ESP_LOGI(TAG_REGISTER, "✓ 灯控制模块已成功注册");
    } else {
        ESP_LOGE(TAG_REGISTER, "✗ 灯控制模块注册失败");
    }

    auto registered_temperature = thing_manager.GetThingByName("Temperature");
    if (registered_temperature) {
        ESP_LOGI(TAG_REGISTER, "✓ 温度传感器模块已成功注册");
    } else {
        ESP_LOGE(TAG_REGISTER, "✗ 温度传感器模块注册失败");
    }

    ESP_LOGI(TAG_REGISTER, "所有物联网设备注册完成");
}

} // namespace iot

#endif // REGISTER_THINGS_H
