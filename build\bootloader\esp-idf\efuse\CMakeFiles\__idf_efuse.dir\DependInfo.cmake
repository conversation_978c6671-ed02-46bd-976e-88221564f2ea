
# Consider dependencies only in project.
set(CMAKE_DEPENDS_IN_PROJECT_ONLY OFF)

# The set of languages for which implicit dependencies are needed:
set(CMAKE_DEPENDS_LANGUAGES
  )

# The set of dependency files which are needed:
set(CMAKE_DEPENDS_DEPENDENCY_FILES
  "/home/<USER>/esp/esp-idf-v5.3/components/efuse/esp32s3/esp_efuse_fields.c" "esp-idf/efuse/CMakeFiles/__idf_efuse.dir/esp32s3/esp_efuse_fields.c.obj" "gcc" "esp-idf/efuse/CMakeFiles/__idf_efuse.dir/esp32s3/esp_efuse_fields.c.obj.d"
  "/home/<USER>/esp/esp-idf-v5.3/components/efuse/esp32s3/esp_efuse_rtc_calib.c" "esp-idf/efuse/CMakeFiles/__idf_efuse.dir/esp32s3/esp_efuse_rtc_calib.c.obj" "gcc" "esp-idf/efuse/CMakeFiles/__idf_efuse.dir/esp32s3/esp_efuse_rtc_calib.c.obj.d"
  "/home/<USER>/esp/esp-idf-v5.3/components/efuse/esp32s3/esp_efuse_table.c" "esp-idf/efuse/CMakeFiles/__idf_efuse.dir/esp32s3/esp_efuse_table.c.obj" "gcc" "esp-idf/efuse/CMakeFiles/__idf_efuse.dir/esp32s3/esp_efuse_table.c.obj.d"
  "/home/<USER>/esp/esp-idf-v5.3/components/efuse/esp32s3/esp_efuse_utility.c" "esp-idf/efuse/CMakeFiles/__idf_efuse.dir/esp32s3/esp_efuse_utility.c.obj" "gcc" "esp-idf/efuse/CMakeFiles/__idf_efuse.dir/esp32s3/esp_efuse_utility.c.obj.d"
  "/home/<USER>/esp/esp-idf-v5.3/components/efuse/src/efuse_controller/keys/with_key_purposes/esp_efuse_api_key.c" "esp-idf/efuse/CMakeFiles/__idf_efuse.dir/src/efuse_controller/keys/with_key_purposes/esp_efuse_api_key.c.obj" "gcc" "esp-idf/efuse/CMakeFiles/__idf_efuse.dir/src/efuse_controller/keys/with_key_purposes/esp_efuse_api_key.c.obj.d"
  "/home/<USER>/esp/esp-idf-v5.3/components/efuse/src/esp_efuse_api.c" "esp-idf/efuse/CMakeFiles/__idf_efuse.dir/src/esp_efuse_api.c.obj" "gcc" "esp-idf/efuse/CMakeFiles/__idf_efuse.dir/src/esp_efuse_api.c.obj.d"
  "/home/<USER>/esp/esp-idf-v5.3/components/efuse/src/esp_efuse_fields.c" "esp-idf/efuse/CMakeFiles/__idf_efuse.dir/src/esp_efuse_fields.c.obj" "gcc" "esp-idf/efuse/CMakeFiles/__idf_efuse.dir/src/esp_efuse_fields.c.obj.d"
  "/home/<USER>/esp/esp-idf-v5.3/components/efuse/src/esp_efuse_utility.c" "esp-idf/efuse/CMakeFiles/__idf_efuse.dir/src/esp_efuse_utility.c.obj" "gcc" "esp-idf/efuse/CMakeFiles/__idf_efuse.dir/src/esp_efuse_utility.c.obj.d"
  )

# Targets to which this target links.
set(CMAKE_TARGET_LINKED_INFO_FILES
  "/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/bootloader/esp-idf/log/CMakeFiles/__idf_log.dir/DependInfo.cmake"
  "/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/bootloader/esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/DependInfo.cmake"
  "/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/bootloader/esp-idf/esp_common/CMakeFiles/__idf_esp_common.dir/DependInfo.cmake"
  "/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/bootloader/esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/DependInfo.cmake"
  "/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/bootloader/esp-idf/xtensa/CMakeFiles/__idf_xtensa.dir/DependInfo.cmake"
  "/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/bootloader/esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/DependInfo.cmake"
  "/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/bootloader/esp-idf/soc/CMakeFiles/__idf_soc.dir/DependInfo.cmake"
  "/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/bootloader/esp-idf/spi_flash/CMakeFiles/__idf_spi_flash.dir/DependInfo.cmake"
  "/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/bootloader/esp-idf/hal/CMakeFiles/__idf_hal.dir/DependInfo.cmake"
  "/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/bootloader/esp-idf/esp_system/CMakeFiles/__idf_esp_system.dir/DependInfo.cmake"
  "/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/bootloader/esp-idf/micro-ecc/CMakeFiles/__idf_micro-ecc.dir/DependInfo.cmake"
  "/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/bootloader/esp-idf/esp_bootloader_format/CMakeFiles/__idf_esp_bootloader_format.dir/DependInfo.cmake"
  )

# Fortran module output directory.
set(CMAKE_Fortran_TARGET_MODULE_DIR "")
