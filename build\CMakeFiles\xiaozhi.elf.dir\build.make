# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.22

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build

# Include any dependencies generated for this target.
include CMakeFiles/xiaozhi.elf.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include CMakeFiles/xiaozhi.elf.dir/compiler_depend.make

# Include the progress variables for this target.
include CMakeFiles/xiaozhi.elf.dir/progress.make

# Include the compile flags for this target's objects.
include CMakeFiles/xiaozhi.elf.dir/flags.make

project_elf_src_esp32s3.c:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --blue --bold --progress-dir=/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Generating project_elf_src_esp32s3.c"
	/usr/bin/cmake -E touch /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/project_elf_src_esp32s3.c

CMakeFiles/xiaozhi.elf.dir/project_elf_src_esp32s3.c.obj: CMakeFiles/xiaozhi.elf.dir/flags.make
CMakeFiles/xiaozhi.elf.dir/project_elf_src_esp32s3.c.obj: project_elf_src_esp32s3.c
CMakeFiles/xiaozhi.elf.dir/project_elf_src_esp32s3.c.obj: CMakeFiles/xiaozhi.elf.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Building C object CMakeFiles/xiaozhi.elf.dir/project_elf_src_esp32s3.c.obj"
	/home/<USER>/.espressif/tools/xtensa-esp-elf/esp-13.2.0_20240530/xtensa-esp-elf/bin/xtensa-esp32s3-elf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/xiaozhi.elf.dir/project_elf_src_esp32s3.c.obj -MF CMakeFiles/xiaozhi.elf.dir/project_elf_src_esp32s3.c.obj.d -o CMakeFiles/xiaozhi.elf.dir/project_elf_src_esp32s3.c.obj -c /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/project_elf_src_esp32s3.c

CMakeFiles/xiaozhi.elf.dir/project_elf_src_esp32s3.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/xiaozhi.elf.dir/project_elf_src_esp32s3.c.i"
	/home/<USER>/.espressif/tools/xtensa-esp-elf/esp-13.2.0_20240530/xtensa-esp-elf/bin/xtensa-esp32s3-elf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/project_elf_src_esp32s3.c > CMakeFiles/xiaozhi.elf.dir/project_elf_src_esp32s3.c.i

CMakeFiles/xiaozhi.elf.dir/project_elf_src_esp32s3.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/xiaozhi.elf.dir/project_elf_src_esp32s3.c.s"
	/home/<USER>/.espressif/tools/xtensa-esp-elf/esp-13.2.0_20240530/xtensa-esp-elf/bin/xtensa-esp32s3-elf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/project_elf_src_esp32s3.c -o CMakeFiles/xiaozhi.elf.dir/project_elf_src_esp32s3.c.s

# Object files for target xiaozhi.elf
xiaozhi_elf_OBJECTS = \
"CMakeFiles/xiaozhi.elf.dir/project_elf_src_esp32s3.c.obj"

# External object files for target xiaozhi.elf
xiaozhi_elf_EXTERNAL_OBJECTS =

xiaozhi.elf: CMakeFiles/xiaozhi.elf.dir/project_elf_src_esp32s3.c.obj
xiaozhi.elf: CMakeFiles/xiaozhi.elf.dir/build.make
xiaozhi.elf: esp-idf/xtensa/libxtensa.a
xiaozhi.elf: esp-idf/esp_driver_gpio/libesp_driver_gpio.a
xiaozhi.elf: esp-idf/esp_pm/libesp_pm.a
xiaozhi.elf: esp-idf/mbedtls/libmbedtls.a
xiaozhi.elf: esp-idf/esp_app_format/libesp_app_format.a
xiaozhi.elf: esp-idf/esp_bootloader_format/libesp_bootloader_format.a
xiaozhi.elf: esp-idf/app_update/libapp_update.a
xiaozhi.elf: esp-idf/esp_partition/libesp_partition.a
xiaozhi.elf: esp-idf/efuse/libefuse.a
xiaozhi.elf: esp-idf/bootloader_support/libbootloader_support.a
xiaozhi.elf: esp-idf/esp_mm/libesp_mm.a
xiaozhi.elf: esp-idf/spi_flash/libspi_flash.a
xiaozhi.elf: esp-idf/esp_system/libesp_system.a
xiaozhi.elf: esp-idf/esp_common/libesp_common.a
xiaozhi.elf: esp-idf/esp_rom/libesp_rom.a
xiaozhi.elf: esp-idf/hal/libhal.a
xiaozhi.elf: esp-idf/log/liblog.a
xiaozhi.elf: esp-idf/heap/libheap.a
xiaozhi.elf: esp-idf/soc/libsoc.a
xiaozhi.elf: esp-idf/esp_hw_support/libesp_hw_support.a
xiaozhi.elf: esp-idf/freertos/libfreertos.a
xiaozhi.elf: esp-idf/newlib/libnewlib.a
xiaozhi.elf: esp-idf/pthread/libpthread.a
xiaozhi.elf: esp-idf/cxx/libcxx.a
xiaozhi.elf: esp-idf/esp_timer/libesp_timer.a
xiaozhi.elf: esp-idf/esp_driver_gptimer/libesp_driver_gptimer.a
xiaozhi.elf: esp-idf/esp_ringbuf/libesp_ringbuf.a
xiaozhi.elf: esp-idf/esp_driver_uart/libesp_driver_uart.a
xiaozhi.elf: esp-idf/app_trace/libapp_trace.a
xiaozhi.elf: esp-idf/esp_event/libesp_event.a
xiaozhi.elf: esp-idf/nvs_flash/libnvs_flash.a
xiaozhi.elf: esp-idf/esp_driver_pcnt/libesp_driver_pcnt.a
xiaozhi.elf: esp-idf/esp_driver_spi/libesp_driver_spi.a
xiaozhi.elf: esp-idf/esp_driver_mcpwm/libesp_driver_mcpwm.a
xiaozhi.elf: esp-idf/esp_driver_i2s/libesp_driver_i2s.a
xiaozhi.elf: esp-idf/sdmmc/libsdmmc.a
xiaozhi.elf: esp-idf/esp_driver_sdmmc/libesp_driver_sdmmc.a
xiaozhi.elf: esp-idf/esp_driver_sdspi/libesp_driver_sdspi.a
xiaozhi.elf: esp-idf/esp_driver_rmt/libesp_driver_rmt.a
xiaozhi.elf: esp-idf/esp_driver_tsens/libesp_driver_tsens.a
xiaozhi.elf: esp-idf/esp_driver_sdm/libesp_driver_sdm.a
xiaozhi.elf: esp-idf/esp_driver_i2c/libesp_driver_i2c.a
xiaozhi.elf: esp-idf/esp_driver_ledc/libesp_driver_ledc.a
xiaozhi.elf: esp-idf/esp_driver_usb_serial_jtag/libesp_driver_usb_serial_jtag.a
xiaozhi.elf: esp-idf/driver/libdriver.a
xiaozhi.elf: esp-idf/esp_phy/libesp_phy.a
xiaozhi.elf: esp-idf/esp_vfs_console/libesp_vfs_console.a
xiaozhi.elf: esp-idf/vfs/libvfs.a
xiaozhi.elf: esp-idf/lwip/liblwip.a
xiaozhi.elf: esp-idf/esp_netif/libesp_netif.a
xiaozhi.elf: esp-idf/wpa_supplicant/libwpa_supplicant.a
xiaozhi.elf: esp-idf/esp_coex/libesp_coex.a
xiaozhi.elf: esp-idf/esp_wifi/libesp_wifi.a
xiaozhi.elf: esp-idf/unity/libunity.a
xiaozhi.elf: esp-idf/cmock/libcmock.a
xiaozhi.elf: esp-idf/console/libconsole.a
xiaozhi.elf: esp-idf/http_parser/libhttp_parser.a
xiaozhi.elf: esp-idf/esp-tls/libesp-tls.a
xiaozhi.elf: esp-idf/esp_adc/libesp_adc.a
xiaozhi.elf: esp-idf/esp_driver_cam/libesp_driver_cam.a
xiaozhi.elf: esp-idf/esp_eth/libesp_eth.a
xiaozhi.elf: esp-idf/esp_gdbstub/libesp_gdbstub.a
xiaozhi.elf: esp-idf/esp_hid/libesp_hid.a
xiaozhi.elf: esp-idf/tcp_transport/libtcp_transport.a
xiaozhi.elf: esp-idf/esp_http_client/libesp_http_client.a
xiaozhi.elf: esp-idf/esp_http_server/libesp_http_server.a
xiaozhi.elf: esp-idf/esp_https_ota/libesp_https_ota.a
xiaozhi.elf: esp-idf/esp_https_server/libesp_https_server.a
xiaozhi.elf: esp-idf/esp_psram/libesp_psram.a
xiaozhi.elf: esp-idf/esp_lcd/libesp_lcd.a
xiaozhi.elf: esp-idf/protobuf-c/libprotobuf-c.a
xiaozhi.elf: esp-idf/protocomm/libprotocomm.a
xiaozhi.elf: esp-idf/esp_local_ctrl/libesp_local_ctrl.a
xiaozhi.elf: esp-idf/espcoredump/libespcoredump.a
xiaozhi.elf: esp-idf/wear_levelling/libwear_levelling.a
xiaozhi.elf: esp-idf/fatfs/libfatfs.a
xiaozhi.elf: esp-idf/json/libjson.a
xiaozhi.elf: esp-idf/mqtt/libmqtt.a
xiaozhi.elf: esp-idf/nvs_sec_provider/libnvs_sec_provider.a
xiaozhi.elf: esp-idf/perfmon/libperfmon.a
xiaozhi.elf: esp-idf/spiffs/libspiffs.a
xiaozhi.elf: esp-idf/touch_element/libtouch_element.a
xiaozhi.elf: esp-idf/usb/libusb.a
xiaozhi.elf: esp-idf/wifi_provisioning/libwifi_provisioning.a
xiaozhi.elf: esp-idf/78__esp-ml307/lib78__esp-ml307.a
xiaozhi.elf: esp-idf/78__esp-opus/lib78__esp-opus.a
xiaozhi.elf: esp-idf/78__esp-opus-encoder/lib78__esp-opus-encoder.a
xiaozhi.elf: esp-idf/78__esp-wifi-connect/lib78__esp-wifi-connect.a
xiaozhi.elf: esp-idf/espressif__button/libespressif__button.a
xiaozhi.elf: esp-idf/espressif__esp-dsp/libespressif__esp-dsp.a
xiaozhi.elf: esp-idf/espressif__esp-sr/libespressif__esp-sr.a
xiaozhi.elf: esp-idf/espressif__esp_codec_dev/libespressif__esp_codec_dev.a
xiaozhi.elf: esp-idf/lvgl__lvgl/liblvgl__lvgl.a
xiaozhi.elf: esp-idf/espressif__led_strip/libespressif__led_strip.a
xiaozhi.elf: esp-idf/main/libmain.a
xiaozhi.elf: esp-idf/app_trace/libapp_trace.a
xiaozhi.elf: esp-idf/app_trace/libapp_trace.a
xiaozhi.elf: esp-idf/cmock/libcmock.a
xiaozhi.elf: esp-idf/unity/libunity.a
xiaozhi.elf: esp-idf/esp_driver_cam/libesp_driver_cam.a
xiaozhi.elf: esp-idf/esp_hid/libesp_hid.a
xiaozhi.elf: esp-idf/esp_local_ctrl/libesp_local_ctrl.a
xiaozhi.elf: esp-idf/esp_https_server/libesp_https_server.a
xiaozhi.elf: esp-idf/espcoredump/libespcoredump.a
xiaozhi.elf: esp-idf/fatfs/libfatfs.a
xiaozhi.elf: esp-idf/wear_levelling/libwear_levelling.a
xiaozhi.elf: esp-idf/nvs_sec_provider/libnvs_sec_provider.a
xiaozhi.elf: esp-idf/perfmon/libperfmon.a
xiaozhi.elf: esp-idf/touch_element/libtouch_element.a
xiaozhi.elf: esp-idf/usb/libusb.a
xiaozhi.elf: esp-idf/wifi_provisioning/libwifi_provisioning.a
xiaozhi.elf: esp-idf/protocomm/libprotocomm.a
xiaozhi.elf: esp-idf/console/libconsole.a
xiaozhi.elf: esp-idf/protobuf-c/libprotobuf-c.a
xiaozhi.elf: esp-idf/78__esp-ml307/lib78__esp-ml307.a
xiaozhi.elf: esp-idf/mqtt/libmqtt.a
xiaozhi.elf: esp-idf/78__esp-opus-encoder/lib78__esp-opus-encoder.a
xiaozhi.elf: esp-idf/78__esp-opus/lib78__esp-opus.a
xiaozhi.elf: esp-idf/78__esp-wifi-connect/lib78__esp-wifi-connect.a
xiaozhi.elf: esp-idf/espressif__esp-sr/libespressif__esp-sr.a
xiaozhi.elf: ../managed_components/espressif__esp-sr/lib/esp32s3/libhufzip.a
xiaozhi.elf: ../managed_components/espressif__esp-sr/lib/esp32s3/libesp_audio_front_end.a
xiaozhi.elf: ../managed_components/espressif__esp-sr/lib/esp32s3/libesp_audio_processor.a
xiaozhi.elf: ../managed_components/espressif__esp-sr/lib/esp32s3/libmultinet.a
xiaozhi.elf: ../managed_components/espressif__esp-sr/lib/esp32s3/libflite_g2p.a
xiaozhi.elf: ../managed_components/espressif__esp-sr/lib/esp32s3/libnsnet.a
xiaozhi.elf: ../managed_components/espressif__esp-sr/lib/esp32s3/libwakenet.a
xiaozhi.elf: esp-idf/espressif__esp-sr/libespressif__esp-sr.a
xiaozhi.elf: ../managed_components/espressif__esp-sr/lib/esp32s3/libhufzip.a
xiaozhi.elf: ../managed_components/espressif__esp-sr/lib/esp32s3/libesp_audio_front_end.a
xiaozhi.elf: ../managed_components/espressif__esp-sr/lib/esp32s3/libesp_audio_processor.a
xiaozhi.elf: ../managed_components/espressif__esp-sr/lib/esp32s3/libmultinet.a
xiaozhi.elf: ../managed_components/espressif__esp-sr/lib/esp32s3/libflite_g2p.a
xiaozhi.elf: ../managed_components/espressif__esp-sr/lib/esp32s3/libnsnet.a
xiaozhi.elf: ../managed_components/espressif__esp-sr/lib/esp32s3/libwakenet.a
xiaozhi.elf: esp-idf/json/libjson.a
xiaozhi.elf: esp-idf/spiffs/libspiffs.a
xiaozhi.elf: esp-idf/espressif__esp-dsp/libespressif__esp-dsp.a
xiaozhi.elf: esp-idf/espressif__esp-dsp/libespressif__esp-dsp.a
xiaozhi.elf: esp-idf/espressif__esp_codec_dev/libespressif__esp_codec_dev.a
xiaozhi.elf: esp-idf/espressif__esp_lvgl_port/liblvgl_port_lib.a
xiaozhi.elf: esp-idf/esp_lcd/libesp_lcd.a
xiaozhi.elf: esp-idf/espressif__button/libespressif__button.a
xiaozhi.elf: esp-idf/lvgl__lvgl/liblvgl__lvgl.a
xiaozhi.elf: esp-idf/espressif__led_strip/libespressif__led_strip.a
xiaozhi.elf: esp-idf/xtensa/libxtensa.a
xiaozhi.elf: esp-idf/esp_driver_gpio/libesp_driver_gpio.a
xiaozhi.elf: esp-idf/esp_pm/libesp_pm.a
xiaozhi.elf: esp-idf/mbedtls/libmbedtls.a
xiaozhi.elf: esp-idf/esp_app_format/libesp_app_format.a
xiaozhi.elf: esp-idf/esp_bootloader_format/libesp_bootloader_format.a
xiaozhi.elf: esp-idf/app_update/libapp_update.a
xiaozhi.elf: esp-idf/esp_partition/libesp_partition.a
xiaozhi.elf: esp-idf/efuse/libefuse.a
xiaozhi.elf: esp-idf/bootloader_support/libbootloader_support.a
xiaozhi.elf: esp-idf/esp_mm/libesp_mm.a
xiaozhi.elf: esp-idf/spi_flash/libspi_flash.a
xiaozhi.elf: esp-idf/esp_system/libesp_system.a
xiaozhi.elf: esp-idf/esp_common/libesp_common.a
xiaozhi.elf: esp-idf/esp_rom/libesp_rom.a
xiaozhi.elf: esp-idf/hal/libhal.a
xiaozhi.elf: esp-idf/log/liblog.a
xiaozhi.elf: esp-idf/heap/libheap.a
xiaozhi.elf: esp-idf/soc/libsoc.a
xiaozhi.elf: esp-idf/esp_hw_support/libesp_hw_support.a
xiaozhi.elf: esp-idf/freertos/libfreertos.a
xiaozhi.elf: esp-idf/newlib/libnewlib.a
xiaozhi.elf: esp-idf/pthread/libpthread.a
xiaozhi.elf: esp-idf/cxx/libcxx.a
xiaozhi.elf: esp-idf/esp_timer/libesp_timer.a
xiaozhi.elf: esp-idf/esp_driver_gptimer/libesp_driver_gptimer.a
xiaozhi.elf: esp-idf/esp_ringbuf/libesp_ringbuf.a
xiaozhi.elf: esp-idf/esp_driver_uart/libesp_driver_uart.a
xiaozhi.elf: esp-idf/esp_event/libesp_event.a
xiaozhi.elf: esp-idf/nvs_flash/libnvs_flash.a
xiaozhi.elf: esp-idf/esp_driver_pcnt/libesp_driver_pcnt.a
xiaozhi.elf: esp-idf/esp_driver_spi/libesp_driver_spi.a
xiaozhi.elf: esp-idf/esp_driver_mcpwm/libesp_driver_mcpwm.a
xiaozhi.elf: esp-idf/esp_driver_i2s/libesp_driver_i2s.a
xiaozhi.elf: esp-idf/sdmmc/libsdmmc.a
xiaozhi.elf: esp-idf/esp_driver_sdmmc/libesp_driver_sdmmc.a
xiaozhi.elf: esp-idf/esp_driver_sdspi/libesp_driver_sdspi.a
xiaozhi.elf: esp-idf/esp_driver_rmt/libesp_driver_rmt.a
xiaozhi.elf: esp-idf/esp_driver_tsens/libesp_driver_tsens.a
xiaozhi.elf: esp-idf/esp_driver_sdm/libesp_driver_sdm.a
xiaozhi.elf: esp-idf/esp_driver_i2c/libesp_driver_i2c.a
xiaozhi.elf: esp-idf/esp_driver_ledc/libesp_driver_ledc.a
xiaozhi.elf: esp-idf/esp_driver_usb_serial_jtag/libesp_driver_usb_serial_jtag.a
xiaozhi.elf: esp-idf/driver/libdriver.a
xiaozhi.elf: esp-idf/esp_phy/libesp_phy.a
xiaozhi.elf: esp-idf/esp_vfs_console/libesp_vfs_console.a
xiaozhi.elf: esp-idf/vfs/libvfs.a
xiaozhi.elf: esp-idf/lwip/liblwip.a
xiaozhi.elf: esp-idf/esp_netif/libesp_netif.a
xiaozhi.elf: esp-idf/wpa_supplicant/libwpa_supplicant.a
xiaozhi.elf: esp-idf/esp_coex/libesp_coex.a
xiaozhi.elf: esp-idf/esp_wifi/libesp_wifi.a
xiaozhi.elf: esp-idf/http_parser/libhttp_parser.a
xiaozhi.elf: esp-idf/esp-tls/libesp-tls.a
xiaozhi.elf: esp-idf/esp_adc/libesp_adc.a
xiaozhi.elf: esp-idf/esp_eth/libesp_eth.a
xiaozhi.elf: esp-idf/esp_gdbstub/libesp_gdbstub.a
xiaozhi.elf: esp-idf/tcp_transport/libtcp_transport.a
xiaozhi.elf: esp-idf/esp_http_client/libesp_http_client.a
xiaozhi.elf: esp-idf/esp_http_server/libesp_http_server.a
xiaozhi.elf: esp-idf/esp_https_ota/libesp_https_ota.a
xiaozhi.elf: esp-idf/esp_psram/libesp_psram.a
xiaozhi.elf: esp-idf/mbedtls/mbedtls/library/libmbedtls.a
xiaozhi.elf: esp-idf/mbedtls/mbedtls/library/libmbedcrypto.a
xiaozhi.elf: esp-idf/mbedtls/mbedtls/library/libmbedx509.a
xiaozhi.elf: esp-idf/mbedtls/mbedtls/3rdparty/everest/libeverest.a
xiaozhi.elf: esp-idf/mbedtls/mbedtls/3rdparty/p256-m/libp256m.a
xiaozhi.elf: /home/<USER>/esp/esp-idf-v5.3/components/esp_wifi/lib/esp32s3/libcore.a
xiaozhi.elf: /home/<USER>/esp/esp-idf-v5.3/components/esp_wifi/lib/esp32s3/libespnow.a
xiaozhi.elf: /home/<USER>/esp/esp-idf-v5.3/components/esp_wifi/lib/esp32s3/libmesh.a
xiaozhi.elf: /home/<USER>/esp/esp-idf-v5.3/components/esp_wifi/lib/esp32s3/libnet80211.a
xiaozhi.elf: /home/<USER>/esp/esp-idf-v5.3/components/esp_wifi/lib/esp32s3/libpp.a
xiaozhi.elf: /home/<USER>/esp/esp-idf-v5.3/components/esp_wifi/lib/esp32s3/libsmartconfig.a
xiaozhi.elf: /home/<USER>/esp/esp-idf-v5.3/components/esp_wifi/lib/esp32s3/libwapi.a
xiaozhi.elf: esp-idf/xtensa/libxtensa.a
xiaozhi.elf: esp-idf/esp_driver_gpio/libesp_driver_gpio.a
xiaozhi.elf: esp-idf/esp_pm/libesp_pm.a
xiaozhi.elf: esp-idf/mbedtls/libmbedtls.a
xiaozhi.elf: esp-idf/esp_app_format/libesp_app_format.a
xiaozhi.elf: esp-idf/esp_bootloader_format/libesp_bootloader_format.a
xiaozhi.elf: esp-idf/app_update/libapp_update.a
xiaozhi.elf: esp-idf/esp_partition/libesp_partition.a
xiaozhi.elf: esp-idf/efuse/libefuse.a
xiaozhi.elf: esp-idf/bootloader_support/libbootloader_support.a
xiaozhi.elf: esp-idf/esp_mm/libesp_mm.a
xiaozhi.elf: esp-idf/spi_flash/libspi_flash.a
xiaozhi.elf: esp-idf/esp_system/libesp_system.a
xiaozhi.elf: esp-idf/esp_common/libesp_common.a
xiaozhi.elf: esp-idf/esp_rom/libesp_rom.a
xiaozhi.elf: esp-idf/hal/libhal.a
xiaozhi.elf: esp-idf/log/liblog.a
xiaozhi.elf: esp-idf/heap/libheap.a
xiaozhi.elf: esp-idf/soc/libsoc.a
xiaozhi.elf: esp-idf/esp_hw_support/libesp_hw_support.a
xiaozhi.elf: esp-idf/freertos/libfreertos.a
xiaozhi.elf: esp-idf/newlib/libnewlib.a
xiaozhi.elf: esp-idf/pthread/libpthread.a
xiaozhi.elf: esp-idf/cxx/libcxx.a
xiaozhi.elf: esp-idf/esp_timer/libesp_timer.a
xiaozhi.elf: esp-idf/esp_driver_gptimer/libesp_driver_gptimer.a
xiaozhi.elf: esp-idf/esp_ringbuf/libesp_ringbuf.a
xiaozhi.elf: esp-idf/esp_driver_uart/libesp_driver_uart.a
xiaozhi.elf: esp-idf/esp_event/libesp_event.a
xiaozhi.elf: esp-idf/nvs_flash/libnvs_flash.a
xiaozhi.elf: esp-idf/esp_driver_pcnt/libesp_driver_pcnt.a
xiaozhi.elf: esp-idf/esp_driver_spi/libesp_driver_spi.a
xiaozhi.elf: esp-idf/esp_driver_mcpwm/libesp_driver_mcpwm.a
xiaozhi.elf: esp-idf/esp_driver_i2s/libesp_driver_i2s.a
xiaozhi.elf: esp-idf/sdmmc/libsdmmc.a
xiaozhi.elf: esp-idf/esp_driver_sdmmc/libesp_driver_sdmmc.a
xiaozhi.elf: esp-idf/esp_driver_sdspi/libesp_driver_sdspi.a
xiaozhi.elf: esp-idf/esp_driver_rmt/libesp_driver_rmt.a
xiaozhi.elf: esp-idf/esp_driver_tsens/libesp_driver_tsens.a
xiaozhi.elf: esp-idf/esp_driver_sdm/libesp_driver_sdm.a
xiaozhi.elf: esp-idf/esp_driver_i2c/libesp_driver_i2c.a
xiaozhi.elf: esp-idf/esp_driver_ledc/libesp_driver_ledc.a
xiaozhi.elf: esp-idf/esp_driver_usb_serial_jtag/libesp_driver_usb_serial_jtag.a
xiaozhi.elf: esp-idf/driver/libdriver.a
xiaozhi.elf: esp-idf/esp_phy/libesp_phy.a
xiaozhi.elf: esp-idf/esp_vfs_console/libesp_vfs_console.a
xiaozhi.elf: esp-idf/vfs/libvfs.a
xiaozhi.elf: esp-idf/lwip/liblwip.a
xiaozhi.elf: esp-idf/esp_netif/libesp_netif.a
xiaozhi.elf: esp-idf/wpa_supplicant/libwpa_supplicant.a
xiaozhi.elf: esp-idf/esp_coex/libesp_coex.a
xiaozhi.elf: esp-idf/esp_wifi/libesp_wifi.a
xiaozhi.elf: esp-idf/http_parser/libhttp_parser.a
xiaozhi.elf: esp-idf/esp-tls/libesp-tls.a
xiaozhi.elf: esp-idf/esp_adc/libesp_adc.a
xiaozhi.elf: esp-idf/esp_eth/libesp_eth.a
xiaozhi.elf: esp-idf/esp_gdbstub/libesp_gdbstub.a
xiaozhi.elf: esp-idf/tcp_transport/libtcp_transport.a
xiaozhi.elf: esp-idf/esp_http_client/libesp_http_client.a
xiaozhi.elf: esp-idf/esp_http_server/libesp_http_server.a
xiaozhi.elf: esp-idf/esp_https_ota/libesp_https_ota.a
xiaozhi.elf: esp-idf/esp_psram/libesp_psram.a
xiaozhi.elf: esp-idf/mbedtls/mbedtls/library/libmbedtls.a
xiaozhi.elf: esp-idf/mbedtls/mbedtls/library/libmbedcrypto.a
xiaozhi.elf: esp-idf/mbedtls/mbedtls/library/libmbedx509.a
xiaozhi.elf: esp-idf/mbedtls/mbedtls/3rdparty/everest/libeverest.a
xiaozhi.elf: esp-idf/mbedtls/mbedtls/3rdparty/p256-m/libp256m.a
xiaozhi.elf: /home/<USER>/esp/esp-idf-v5.3/components/esp_wifi/lib/esp32s3/libcore.a
xiaozhi.elf: /home/<USER>/esp/esp-idf-v5.3/components/esp_wifi/lib/esp32s3/libespnow.a
xiaozhi.elf: /home/<USER>/esp/esp-idf-v5.3/components/esp_wifi/lib/esp32s3/libmesh.a
xiaozhi.elf: /home/<USER>/esp/esp-idf-v5.3/components/esp_wifi/lib/esp32s3/libnet80211.a
xiaozhi.elf: /home/<USER>/esp/esp-idf-v5.3/components/esp_wifi/lib/esp32s3/libpp.a
xiaozhi.elf: /home/<USER>/esp/esp-idf-v5.3/components/esp_wifi/lib/esp32s3/libsmartconfig.a
xiaozhi.elf: /home/<USER>/esp/esp-idf-v5.3/components/esp_wifi/lib/esp32s3/libwapi.a
xiaozhi.elf: esp-idf/xtensa/libxtensa.a
xiaozhi.elf: esp-idf/esp_driver_gpio/libesp_driver_gpio.a
xiaozhi.elf: esp-idf/esp_pm/libesp_pm.a
xiaozhi.elf: esp-idf/mbedtls/libmbedtls.a
xiaozhi.elf: esp-idf/esp_app_format/libesp_app_format.a
xiaozhi.elf: esp-idf/esp_bootloader_format/libesp_bootloader_format.a
xiaozhi.elf: esp-idf/app_update/libapp_update.a
xiaozhi.elf: esp-idf/esp_partition/libesp_partition.a
xiaozhi.elf: esp-idf/efuse/libefuse.a
xiaozhi.elf: esp-idf/bootloader_support/libbootloader_support.a
xiaozhi.elf: esp-idf/esp_mm/libesp_mm.a
xiaozhi.elf: esp-idf/spi_flash/libspi_flash.a
xiaozhi.elf: esp-idf/esp_system/libesp_system.a
xiaozhi.elf: esp-idf/esp_common/libesp_common.a
xiaozhi.elf: esp-idf/esp_rom/libesp_rom.a
xiaozhi.elf: esp-idf/hal/libhal.a
xiaozhi.elf: esp-idf/log/liblog.a
xiaozhi.elf: esp-idf/heap/libheap.a
xiaozhi.elf: esp-idf/soc/libsoc.a
xiaozhi.elf: esp-idf/esp_hw_support/libesp_hw_support.a
xiaozhi.elf: esp-idf/freertos/libfreertos.a
xiaozhi.elf: esp-idf/newlib/libnewlib.a
xiaozhi.elf: esp-idf/pthread/libpthread.a
xiaozhi.elf: esp-idf/cxx/libcxx.a
xiaozhi.elf: esp-idf/esp_timer/libesp_timer.a
xiaozhi.elf: esp-idf/esp_driver_gptimer/libesp_driver_gptimer.a
xiaozhi.elf: esp-idf/esp_ringbuf/libesp_ringbuf.a
xiaozhi.elf: esp-idf/esp_driver_uart/libesp_driver_uart.a
xiaozhi.elf: esp-idf/esp_event/libesp_event.a
xiaozhi.elf: esp-idf/nvs_flash/libnvs_flash.a
xiaozhi.elf: esp-idf/esp_driver_pcnt/libesp_driver_pcnt.a
xiaozhi.elf: esp-idf/esp_driver_spi/libesp_driver_spi.a
xiaozhi.elf: esp-idf/esp_driver_mcpwm/libesp_driver_mcpwm.a
xiaozhi.elf: esp-idf/esp_driver_i2s/libesp_driver_i2s.a
xiaozhi.elf: esp-idf/sdmmc/libsdmmc.a
xiaozhi.elf: esp-idf/esp_driver_sdmmc/libesp_driver_sdmmc.a
xiaozhi.elf: esp-idf/esp_driver_sdspi/libesp_driver_sdspi.a
xiaozhi.elf: esp-idf/esp_driver_rmt/libesp_driver_rmt.a
xiaozhi.elf: esp-idf/esp_driver_tsens/libesp_driver_tsens.a
xiaozhi.elf: esp-idf/esp_driver_sdm/libesp_driver_sdm.a
xiaozhi.elf: esp-idf/esp_driver_i2c/libesp_driver_i2c.a
xiaozhi.elf: esp-idf/esp_driver_ledc/libesp_driver_ledc.a
xiaozhi.elf: esp-idf/esp_driver_usb_serial_jtag/libesp_driver_usb_serial_jtag.a
xiaozhi.elf: esp-idf/driver/libdriver.a
xiaozhi.elf: esp-idf/esp_phy/libesp_phy.a
xiaozhi.elf: esp-idf/esp_vfs_console/libesp_vfs_console.a
xiaozhi.elf: esp-idf/vfs/libvfs.a
xiaozhi.elf: esp-idf/lwip/liblwip.a
xiaozhi.elf: esp-idf/esp_netif/libesp_netif.a
xiaozhi.elf: esp-idf/wpa_supplicant/libwpa_supplicant.a
xiaozhi.elf: esp-idf/esp_coex/libesp_coex.a
xiaozhi.elf: esp-idf/esp_wifi/libesp_wifi.a
xiaozhi.elf: esp-idf/http_parser/libhttp_parser.a
xiaozhi.elf: esp-idf/esp-tls/libesp-tls.a
xiaozhi.elf: esp-idf/esp_adc/libesp_adc.a
xiaozhi.elf: esp-idf/esp_eth/libesp_eth.a
xiaozhi.elf: esp-idf/esp_gdbstub/libesp_gdbstub.a
xiaozhi.elf: esp-idf/tcp_transport/libtcp_transport.a
xiaozhi.elf: esp-idf/esp_http_client/libesp_http_client.a
xiaozhi.elf: esp-idf/esp_http_server/libesp_http_server.a
xiaozhi.elf: esp-idf/esp_https_ota/libesp_https_ota.a
xiaozhi.elf: esp-idf/esp_psram/libesp_psram.a
xiaozhi.elf: esp-idf/mbedtls/mbedtls/library/libmbedtls.a
xiaozhi.elf: esp-idf/mbedtls/mbedtls/library/libmbedcrypto.a
xiaozhi.elf: esp-idf/mbedtls/mbedtls/library/libmbedx509.a
xiaozhi.elf: esp-idf/mbedtls/mbedtls/3rdparty/everest/libeverest.a
xiaozhi.elf: esp-idf/mbedtls/mbedtls/3rdparty/p256-m/libp256m.a
xiaozhi.elf: /home/<USER>/esp/esp-idf-v5.3/components/esp_wifi/lib/esp32s3/libcore.a
xiaozhi.elf: /home/<USER>/esp/esp-idf-v5.3/components/esp_wifi/lib/esp32s3/libespnow.a
xiaozhi.elf: /home/<USER>/esp/esp-idf-v5.3/components/esp_wifi/lib/esp32s3/libmesh.a
xiaozhi.elf: /home/<USER>/esp/esp-idf-v5.3/components/esp_wifi/lib/esp32s3/libnet80211.a
xiaozhi.elf: /home/<USER>/esp/esp-idf-v5.3/components/esp_wifi/lib/esp32s3/libpp.a
xiaozhi.elf: /home/<USER>/esp/esp-idf-v5.3/components/esp_wifi/lib/esp32s3/libsmartconfig.a
xiaozhi.elf: /home/<USER>/esp/esp-idf-v5.3/components/esp_wifi/lib/esp32s3/libwapi.a
xiaozhi.elf: esp-idf/xtensa/libxtensa.a
xiaozhi.elf: esp-idf/esp_driver_gpio/libesp_driver_gpio.a
xiaozhi.elf: esp-idf/esp_pm/libesp_pm.a
xiaozhi.elf: esp-idf/mbedtls/libmbedtls.a
xiaozhi.elf: esp-idf/esp_app_format/libesp_app_format.a
xiaozhi.elf: esp-idf/esp_bootloader_format/libesp_bootloader_format.a
xiaozhi.elf: esp-idf/app_update/libapp_update.a
xiaozhi.elf: esp-idf/esp_partition/libesp_partition.a
xiaozhi.elf: esp-idf/efuse/libefuse.a
xiaozhi.elf: esp-idf/bootloader_support/libbootloader_support.a
xiaozhi.elf: esp-idf/esp_mm/libesp_mm.a
xiaozhi.elf: esp-idf/spi_flash/libspi_flash.a
xiaozhi.elf: esp-idf/esp_system/libesp_system.a
xiaozhi.elf: esp-idf/esp_common/libesp_common.a
xiaozhi.elf: esp-idf/esp_rom/libesp_rom.a
xiaozhi.elf: esp-idf/hal/libhal.a
xiaozhi.elf: esp-idf/log/liblog.a
xiaozhi.elf: esp-idf/heap/libheap.a
xiaozhi.elf: esp-idf/soc/libsoc.a
xiaozhi.elf: esp-idf/esp_hw_support/libesp_hw_support.a
xiaozhi.elf: esp-idf/freertos/libfreertos.a
xiaozhi.elf: esp-idf/newlib/libnewlib.a
xiaozhi.elf: esp-idf/pthread/libpthread.a
xiaozhi.elf: esp-idf/cxx/libcxx.a
xiaozhi.elf: esp-idf/esp_timer/libesp_timer.a
xiaozhi.elf: esp-idf/esp_driver_gptimer/libesp_driver_gptimer.a
xiaozhi.elf: esp-idf/esp_ringbuf/libesp_ringbuf.a
xiaozhi.elf: esp-idf/esp_driver_uart/libesp_driver_uart.a
xiaozhi.elf: esp-idf/esp_event/libesp_event.a
xiaozhi.elf: esp-idf/nvs_flash/libnvs_flash.a
xiaozhi.elf: esp-idf/esp_driver_pcnt/libesp_driver_pcnt.a
xiaozhi.elf: esp-idf/esp_driver_spi/libesp_driver_spi.a
xiaozhi.elf: esp-idf/esp_driver_mcpwm/libesp_driver_mcpwm.a
xiaozhi.elf: esp-idf/esp_driver_i2s/libesp_driver_i2s.a
xiaozhi.elf: esp-idf/sdmmc/libsdmmc.a
xiaozhi.elf: esp-idf/esp_driver_sdmmc/libesp_driver_sdmmc.a
xiaozhi.elf: esp-idf/esp_driver_sdspi/libesp_driver_sdspi.a
xiaozhi.elf: esp-idf/esp_driver_rmt/libesp_driver_rmt.a
xiaozhi.elf: esp-idf/esp_driver_tsens/libesp_driver_tsens.a
xiaozhi.elf: esp-idf/esp_driver_sdm/libesp_driver_sdm.a
xiaozhi.elf: esp-idf/esp_driver_i2c/libesp_driver_i2c.a
xiaozhi.elf: esp-idf/esp_driver_ledc/libesp_driver_ledc.a
xiaozhi.elf: esp-idf/esp_driver_usb_serial_jtag/libesp_driver_usb_serial_jtag.a
xiaozhi.elf: esp-idf/driver/libdriver.a
xiaozhi.elf: esp-idf/esp_phy/libesp_phy.a
xiaozhi.elf: esp-idf/esp_vfs_console/libesp_vfs_console.a
xiaozhi.elf: esp-idf/vfs/libvfs.a
xiaozhi.elf: esp-idf/lwip/liblwip.a
xiaozhi.elf: esp-idf/esp_netif/libesp_netif.a
xiaozhi.elf: esp-idf/wpa_supplicant/libwpa_supplicant.a
xiaozhi.elf: esp-idf/esp_coex/libesp_coex.a
xiaozhi.elf: esp-idf/esp_wifi/libesp_wifi.a
xiaozhi.elf: esp-idf/http_parser/libhttp_parser.a
xiaozhi.elf: esp-idf/esp-tls/libesp-tls.a
xiaozhi.elf: esp-idf/esp_adc/libesp_adc.a
xiaozhi.elf: esp-idf/esp_eth/libesp_eth.a
xiaozhi.elf: esp-idf/esp_gdbstub/libesp_gdbstub.a
xiaozhi.elf: esp-idf/tcp_transport/libtcp_transport.a
xiaozhi.elf: esp-idf/esp_http_client/libesp_http_client.a
xiaozhi.elf: esp-idf/esp_http_server/libesp_http_server.a
xiaozhi.elf: esp-idf/esp_https_ota/libesp_https_ota.a
xiaozhi.elf: esp-idf/esp_psram/libesp_psram.a
xiaozhi.elf: esp-idf/mbedtls/mbedtls/library/libmbedtls.a
xiaozhi.elf: esp-idf/mbedtls/mbedtls/library/libmbedcrypto.a
xiaozhi.elf: esp-idf/mbedtls/mbedtls/library/libmbedx509.a
xiaozhi.elf: esp-idf/mbedtls/mbedtls/3rdparty/everest/libeverest.a
xiaozhi.elf: esp-idf/mbedtls/mbedtls/3rdparty/p256-m/libp256m.a
xiaozhi.elf: /home/<USER>/esp/esp-idf-v5.3/components/esp_wifi/lib/esp32s3/libcore.a
xiaozhi.elf: /home/<USER>/esp/esp-idf-v5.3/components/esp_wifi/lib/esp32s3/libespnow.a
xiaozhi.elf: /home/<USER>/esp/esp-idf-v5.3/components/esp_wifi/lib/esp32s3/libmesh.a
xiaozhi.elf: /home/<USER>/esp/esp-idf-v5.3/components/esp_wifi/lib/esp32s3/libnet80211.a
xiaozhi.elf: /home/<USER>/esp/esp-idf-v5.3/components/esp_wifi/lib/esp32s3/libpp.a
xiaozhi.elf: /home/<USER>/esp/esp-idf-v5.3/components/esp_wifi/lib/esp32s3/libsmartconfig.a
xiaozhi.elf: /home/<USER>/esp/esp-idf-v5.3/components/esp_wifi/lib/esp32s3/libwapi.a
xiaozhi.elf: esp-idf/xtensa/libxtensa.a
xiaozhi.elf: esp-idf/esp_driver_gpio/libesp_driver_gpio.a
xiaozhi.elf: esp-idf/esp_pm/libesp_pm.a
xiaozhi.elf: esp-idf/mbedtls/libmbedtls.a
xiaozhi.elf: esp-idf/esp_app_format/libesp_app_format.a
xiaozhi.elf: esp-idf/esp_bootloader_format/libesp_bootloader_format.a
xiaozhi.elf: esp-idf/app_update/libapp_update.a
xiaozhi.elf: esp-idf/esp_partition/libesp_partition.a
xiaozhi.elf: esp-idf/efuse/libefuse.a
xiaozhi.elf: esp-idf/bootloader_support/libbootloader_support.a
xiaozhi.elf: esp-idf/esp_mm/libesp_mm.a
xiaozhi.elf: esp-idf/spi_flash/libspi_flash.a
xiaozhi.elf: esp-idf/esp_system/libesp_system.a
xiaozhi.elf: esp-idf/esp_common/libesp_common.a
xiaozhi.elf: esp-idf/esp_rom/libesp_rom.a
xiaozhi.elf: esp-idf/hal/libhal.a
xiaozhi.elf: esp-idf/log/liblog.a
xiaozhi.elf: esp-idf/heap/libheap.a
xiaozhi.elf: esp-idf/soc/libsoc.a
xiaozhi.elf: esp-idf/esp_hw_support/libesp_hw_support.a
xiaozhi.elf: esp-idf/freertos/libfreertos.a
xiaozhi.elf: esp-idf/newlib/libnewlib.a
xiaozhi.elf: esp-idf/pthread/libpthread.a
xiaozhi.elf: esp-idf/cxx/libcxx.a
xiaozhi.elf: esp-idf/esp_timer/libesp_timer.a
xiaozhi.elf: esp-idf/esp_driver_gptimer/libesp_driver_gptimer.a
xiaozhi.elf: esp-idf/esp_ringbuf/libesp_ringbuf.a
xiaozhi.elf: esp-idf/esp_driver_uart/libesp_driver_uart.a
xiaozhi.elf: esp-idf/esp_event/libesp_event.a
xiaozhi.elf: esp-idf/nvs_flash/libnvs_flash.a
xiaozhi.elf: esp-idf/esp_driver_pcnt/libesp_driver_pcnt.a
xiaozhi.elf: esp-idf/esp_driver_spi/libesp_driver_spi.a
xiaozhi.elf: esp-idf/esp_driver_mcpwm/libesp_driver_mcpwm.a
xiaozhi.elf: esp-idf/esp_driver_i2s/libesp_driver_i2s.a
xiaozhi.elf: esp-idf/sdmmc/libsdmmc.a
xiaozhi.elf: esp-idf/esp_driver_sdmmc/libesp_driver_sdmmc.a
xiaozhi.elf: esp-idf/esp_driver_sdspi/libesp_driver_sdspi.a
xiaozhi.elf: esp-idf/esp_driver_rmt/libesp_driver_rmt.a
xiaozhi.elf: esp-idf/esp_driver_tsens/libesp_driver_tsens.a
xiaozhi.elf: esp-idf/esp_driver_sdm/libesp_driver_sdm.a
xiaozhi.elf: esp-idf/esp_driver_i2c/libesp_driver_i2c.a
xiaozhi.elf: esp-idf/esp_driver_ledc/libesp_driver_ledc.a
xiaozhi.elf: esp-idf/esp_driver_usb_serial_jtag/libesp_driver_usb_serial_jtag.a
xiaozhi.elf: esp-idf/driver/libdriver.a
xiaozhi.elf: esp-idf/esp_phy/libesp_phy.a
xiaozhi.elf: esp-idf/esp_vfs_console/libesp_vfs_console.a
xiaozhi.elf: esp-idf/vfs/libvfs.a
xiaozhi.elf: esp-idf/lwip/liblwip.a
xiaozhi.elf: esp-idf/esp_netif/libesp_netif.a
xiaozhi.elf: esp-idf/wpa_supplicant/libwpa_supplicant.a
xiaozhi.elf: esp-idf/esp_coex/libesp_coex.a
xiaozhi.elf: esp-idf/esp_wifi/libesp_wifi.a
xiaozhi.elf: esp-idf/http_parser/libhttp_parser.a
xiaozhi.elf: esp-idf/esp-tls/libesp-tls.a
xiaozhi.elf: esp-idf/esp_adc/libesp_adc.a
xiaozhi.elf: esp-idf/esp_eth/libesp_eth.a
xiaozhi.elf: esp-idf/esp_gdbstub/libesp_gdbstub.a
xiaozhi.elf: esp-idf/tcp_transport/libtcp_transport.a
xiaozhi.elf: esp-idf/esp_http_client/libesp_http_client.a
xiaozhi.elf: esp-idf/esp_http_server/libesp_http_server.a
xiaozhi.elf: esp-idf/esp_https_ota/libesp_https_ota.a
xiaozhi.elf: esp-idf/esp_psram/libesp_psram.a
xiaozhi.elf: esp-idf/mbedtls/mbedtls/library/libmbedtls.a
xiaozhi.elf: esp-idf/mbedtls/mbedtls/library/libmbedcrypto.a
xiaozhi.elf: esp-idf/mbedtls/mbedtls/library/libmbedx509.a
xiaozhi.elf: esp-idf/mbedtls/mbedtls/3rdparty/everest/libeverest.a
xiaozhi.elf: esp-idf/mbedtls/mbedtls/3rdparty/p256-m/libp256m.a
xiaozhi.elf: /home/<USER>/esp/esp-idf-v5.3/components/esp_wifi/lib/esp32s3/libcore.a
xiaozhi.elf: /home/<USER>/esp/esp-idf-v5.3/components/esp_wifi/lib/esp32s3/libespnow.a
xiaozhi.elf: /home/<USER>/esp/esp-idf-v5.3/components/esp_wifi/lib/esp32s3/libmesh.a
xiaozhi.elf: /home/<USER>/esp/esp-idf-v5.3/components/esp_wifi/lib/esp32s3/libnet80211.a
xiaozhi.elf: /home/<USER>/esp/esp-idf-v5.3/components/esp_wifi/lib/esp32s3/libpp.a
xiaozhi.elf: /home/<USER>/esp/esp-idf-v5.3/components/esp_wifi/lib/esp32s3/libsmartconfig.a
xiaozhi.elf: /home/<USER>/esp/esp-idf-v5.3/components/esp_wifi/lib/esp32s3/libwapi.a
xiaozhi.elf: esp-idf/xtensa/libxtensa.a
xiaozhi.elf: esp-idf/esp_driver_gpio/libesp_driver_gpio.a
xiaozhi.elf: esp-idf/esp_pm/libesp_pm.a
xiaozhi.elf: esp-idf/mbedtls/libmbedtls.a
xiaozhi.elf: esp-idf/esp_app_format/libesp_app_format.a
xiaozhi.elf: esp-idf/esp_bootloader_format/libesp_bootloader_format.a
xiaozhi.elf: esp-idf/app_update/libapp_update.a
xiaozhi.elf: esp-idf/esp_partition/libesp_partition.a
xiaozhi.elf: esp-idf/efuse/libefuse.a
xiaozhi.elf: esp-idf/bootloader_support/libbootloader_support.a
xiaozhi.elf: esp-idf/esp_mm/libesp_mm.a
xiaozhi.elf: esp-idf/spi_flash/libspi_flash.a
xiaozhi.elf: esp-idf/esp_system/libesp_system.a
xiaozhi.elf: esp-idf/esp_common/libesp_common.a
xiaozhi.elf: esp-idf/esp_rom/libesp_rom.a
xiaozhi.elf: esp-idf/hal/libhal.a
xiaozhi.elf: esp-idf/log/liblog.a
xiaozhi.elf: esp-idf/heap/libheap.a
xiaozhi.elf: esp-idf/soc/libsoc.a
xiaozhi.elf: esp-idf/esp_hw_support/libesp_hw_support.a
xiaozhi.elf: esp-idf/freertos/libfreertos.a
xiaozhi.elf: esp-idf/newlib/libnewlib.a
xiaozhi.elf: esp-idf/pthread/libpthread.a
xiaozhi.elf: esp-idf/cxx/libcxx.a
xiaozhi.elf: esp-idf/esp_timer/libesp_timer.a
xiaozhi.elf: esp-idf/esp_driver_gptimer/libesp_driver_gptimer.a
xiaozhi.elf: esp-idf/esp_ringbuf/libesp_ringbuf.a
xiaozhi.elf: esp-idf/esp_driver_uart/libesp_driver_uart.a
xiaozhi.elf: esp-idf/esp_event/libesp_event.a
xiaozhi.elf: esp-idf/nvs_flash/libnvs_flash.a
xiaozhi.elf: esp-idf/esp_driver_pcnt/libesp_driver_pcnt.a
xiaozhi.elf: esp-idf/esp_driver_spi/libesp_driver_spi.a
xiaozhi.elf: esp-idf/esp_driver_mcpwm/libesp_driver_mcpwm.a
xiaozhi.elf: esp-idf/esp_driver_i2s/libesp_driver_i2s.a
xiaozhi.elf: esp-idf/sdmmc/libsdmmc.a
xiaozhi.elf: esp-idf/esp_driver_sdmmc/libesp_driver_sdmmc.a
xiaozhi.elf: esp-idf/esp_driver_sdspi/libesp_driver_sdspi.a
xiaozhi.elf: esp-idf/esp_driver_rmt/libesp_driver_rmt.a
xiaozhi.elf: esp-idf/esp_driver_tsens/libesp_driver_tsens.a
xiaozhi.elf: esp-idf/esp_driver_sdm/libesp_driver_sdm.a
xiaozhi.elf: esp-idf/esp_driver_i2c/libesp_driver_i2c.a
xiaozhi.elf: esp-idf/esp_driver_ledc/libesp_driver_ledc.a
xiaozhi.elf: esp-idf/esp_driver_usb_serial_jtag/libesp_driver_usb_serial_jtag.a
xiaozhi.elf: esp-idf/driver/libdriver.a
xiaozhi.elf: esp-idf/esp_phy/libesp_phy.a
xiaozhi.elf: esp-idf/esp_vfs_console/libesp_vfs_console.a
xiaozhi.elf: esp-idf/vfs/libvfs.a
xiaozhi.elf: esp-idf/lwip/liblwip.a
xiaozhi.elf: esp-idf/esp_netif/libesp_netif.a
xiaozhi.elf: esp-idf/wpa_supplicant/libwpa_supplicant.a
xiaozhi.elf: esp-idf/esp_coex/libesp_coex.a
xiaozhi.elf: esp-idf/esp_wifi/libesp_wifi.a
xiaozhi.elf: esp-idf/http_parser/libhttp_parser.a
xiaozhi.elf: esp-idf/esp-tls/libesp-tls.a
xiaozhi.elf: esp-idf/esp_adc/libesp_adc.a
xiaozhi.elf: esp-idf/esp_eth/libesp_eth.a
xiaozhi.elf: esp-idf/esp_gdbstub/libesp_gdbstub.a
xiaozhi.elf: esp-idf/tcp_transport/libtcp_transport.a
xiaozhi.elf: esp-idf/esp_http_client/libesp_http_client.a
xiaozhi.elf: esp-idf/esp_http_server/libesp_http_server.a
xiaozhi.elf: esp-idf/esp_https_ota/libesp_https_ota.a
xiaozhi.elf: esp-idf/esp_psram/libesp_psram.a
xiaozhi.elf: esp-idf/mbedtls/mbedtls/library/libmbedtls.a
xiaozhi.elf: esp-idf/mbedtls/mbedtls/library/libmbedcrypto.a
xiaozhi.elf: esp-idf/mbedtls/mbedtls/library/libmbedx509.a
xiaozhi.elf: esp-idf/mbedtls/mbedtls/3rdparty/everest/libeverest.a
xiaozhi.elf: esp-idf/mbedtls/mbedtls/3rdparty/p256-m/libp256m.a
xiaozhi.elf: /home/<USER>/esp/esp-idf-v5.3/components/esp_wifi/lib/esp32s3/libcore.a
xiaozhi.elf: /home/<USER>/esp/esp-idf-v5.3/components/esp_wifi/lib/esp32s3/libespnow.a
xiaozhi.elf: /home/<USER>/esp/esp-idf-v5.3/components/esp_wifi/lib/esp32s3/libmesh.a
xiaozhi.elf: /home/<USER>/esp/esp-idf-v5.3/components/esp_wifi/lib/esp32s3/libnet80211.a
xiaozhi.elf: /home/<USER>/esp/esp-idf-v5.3/components/esp_wifi/lib/esp32s3/libpp.a
xiaozhi.elf: /home/<USER>/esp/esp-idf-v5.3/components/esp_wifi/lib/esp32s3/libsmartconfig.a
xiaozhi.elf: /home/<USER>/esp/esp-idf-v5.3/components/esp_wifi/lib/esp32s3/libwapi.a
xiaozhi.elf: /home/<USER>/esp/esp-idf-v5.3/components/xtensa/esp32s3/libxt_hal.a
xiaozhi.elf: esp-idf/pthread/libpthread.a
xiaozhi.elf: esp-idf/newlib/libnewlib.a
xiaozhi.elf: esp-idf/cxx/libcxx.a
xiaozhi.elf: esp-idf/esp_phy/libesp_phy.a
xiaozhi.elf: esp-idf/esp_phy/libesp_phy.a
xiaozhi.elf: esp-idf/esp_system/ld/memory.ld
xiaozhi.elf: esp-idf/esp_system/ld/sections.ld
xiaozhi.elf: /home/<USER>/esp/esp-idf-v5.3/components/esp_rom/esp32s3/ld/esp32s3.rom.ld
xiaozhi.elf: /home/<USER>/esp/esp-idf-v5.3/components/esp_rom/esp32s3/ld/esp32s3.rom.api.ld
xiaozhi.elf: /home/<USER>/esp/esp-idf-v5.3/components/esp_rom/esp32s3/ld/esp32s3.rom.libgcc.ld
xiaozhi.elf: /home/<USER>/esp/esp-idf-v5.3/components/esp_rom/esp32s3/ld/esp32s3.rom.wdt.ld
xiaozhi.elf: /home/<USER>/esp/esp-idf-v5.3/components/esp_rom/esp32s3/ld/esp32s3.rom.version.ld
xiaozhi.elf: /home/<USER>/esp/esp-idf-v5.3/components/esp_rom/esp32s3/ld/esp32s3.rom.newlib.ld
xiaozhi.elf: /home/<USER>/esp/esp-idf-v5.3/components/soc/esp32s3/ld/esp32s3.peripherals.ld
xiaozhi.elf: CMakeFiles/xiaozhi.elf.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --bold --progress-dir=/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_3) "Linking CXX executable xiaozhi.elf"
	$(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/xiaozhi.elf.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
CMakeFiles/xiaozhi.elf.dir/build: xiaozhi.elf
.PHONY : CMakeFiles/xiaozhi.elf.dir/build

CMakeFiles/xiaozhi.elf.dir/clean:
	$(CMAKE_COMMAND) -P CMakeFiles/xiaozhi.elf.dir/cmake_clean.cmake
.PHONY : CMakeFiles/xiaozhi.elf.dir/clean

CMakeFiles/xiaozhi.elf.dir/depend: project_elf_src_esp32s3.c
	cd /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles/xiaozhi.elf.dir/DependInfo.cmake --color=$(COLOR)
.PHONY : CMakeFiles/xiaozhi.elf.dir/depend

