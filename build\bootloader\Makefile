# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.22

# Default target executed when no arguments are given to make.
default_target: all
.PHONY : default_target

# Allow only one "make -f Makefile2" at a time, but pass parallelism.
.NOTPARALLEL:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/esp/esp-idf-v5.3/components/bootloader/subproject

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/bootloader

#=============================================================================
# Targets provided globally by CMake.

# Special rule for the target edit_cache
edit_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "No interactive CMake dialog available..."
	/usr/bin/cmake -E echo No\ interactive\ CMake\ dialog\ available.
.PHONY : edit_cache

# Special rule for the target edit_cache
edit_cache/fast: edit_cache
.PHONY : edit_cache/fast

# Special rule for the target rebuild_cache
rebuild_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Running CMake to regenerate build system..."
	/usr/bin/cmake --regenerate-during-build -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : rebuild_cache

# Special rule for the target rebuild_cache
rebuild_cache/fast: rebuild_cache
.PHONY : rebuild_cache/fast

# The main all target
all: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/bootloader/CMakeFiles /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/bootloader//CMakeFiles/progress.marks
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/bootloader/CMakeFiles 0
.PHONY : all

# The main clean target
clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 clean
.PHONY : clean

# The main clean target
clean/fast: clean
.PHONY : clean/fast

# Prepare targets for installation.
preinstall: all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 preinstall
.PHONY : preinstall

# Prepare targets for installation.
preinstall/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 preinstall
.PHONY : preinstall/fast

# clear depends
depend:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 1
.PHONY : depend

#=============================================================================
# Target rules for targets named menuconfig

# Build rule for target.
menuconfig: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 menuconfig
.PHONY : menuconfig

# fast build rule for target.
menuconfig/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/menuconfig.dir/build.make CMakeFiles/menuconfig.dir/build
.PHONY : menuconfig/fast

#=============================================================================
# Target rules for targets named confserver

# Build rule for target.
confserver: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 confserver
.PHONY : confserver

# fast build rule for target.
confserver/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/confserver.dir/build.make CMakeFiles/confserver.dir/build
.PHONY : confserver/fast

#=============================================================================
# Target rules for targets named save-defconfig

# Build rule for target.
save-defconfig: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 save-defconfig
.PHONY : save-defconfig

# fast build rule for target.
save-defconfig/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/save-defconfig.dir/build.make CMakeFiles/save-defconfig.dir/build
.PHONY : save-defconfig/fast

#=============================================================================
# Target rules for targets named gen_project_binary

# Build rule for target.
gen_project_binary: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 gen_project_binary
.PHONY : gen_project_binary

# fast build rule for target.
gen_project_binary/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/gen_project_binary.dir/build.make CMakeFiles/gen_project_binary.dir/build
.PHONY : gen_project_binary/fast

#=============================================================================
# Target rules for targets named app

# Build rule for target.
app: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 app
.PHONY : app

# fast build rule for target.
app/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/app.dir/build.make CMakeFiles/app.dir/build
.PHONY : app/fast

#=============================================================================
# Target rules for targets named erase_flash

# Build rule for target.
erase_flash: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 erase_flash
.PHONY : erase_flash

# fast build rule for target.
erase_flash/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/erase_flash.dir/build.make CMakeFiles/erase_flash.dir/build
.PHONY : erase_flash/fast

#=============================================================================
# Target rules for targets named uf2

# Build rule for target.
uf2: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 uf2
.PHONY : uf2

# fast build rule for target.
uf2/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/uf2.dir/build.make CMakeFiles/uf2.dir/build
.PHONY : uf2/fast

#=============================================================================
# Target rules for targets named uf2-app

# Build rule for target.
uf2-app: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 uf2-app
.PHONY : uf2-app

# fast build rule for target.
uf2-app/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/uf2-app.dir/build.make CMakeFiles/uf2-app.dir/build
.PHONY : uf2-app/fast

#=============================================================================
# Target rules for targets named merge-bin

# Build rule for target.
merge-bin: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 merge-bin
.PHONY : merge-bin

# fast build rule for target.
merge-bin/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/merge-bin.dir/build.make CMakeFiles/merge-bin.dir/build
.PHONY : merge-bin/fast

#=============================================================================
# Target rules for targets named monitor

# Build rule for target.
monitor: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 monitor
.PHONY : monitor

# fast build rule for target.
monitor/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/monitor.dir/build.make CMakeFiles/monitor.dir/build
.PHONY : monitor/fast

#=============================================================================
# Target rules for targets named _project_elf_src

# Build rule for target.
_project_elf_src: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 _project_elf_src
.PHONY : _project_elf_src

# fast build rule for target.
_project_elf_src/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/_project_elf_src.dir/build.make CMakeFiles/_project_elf_src.dir/build
.PHONY : _project_elf_src/fast

#=============================================================================
# Target rules for targets named bootloader.elf

# Build rule for target.
bootloader.elf: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 bootloader.elf
.PHONY : bootloader.elf

# fast build rule for target.
bootloader.elf/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/bootloader.elf.dir/build.make CMakeFiles/bootloader.elf.dir/build
.PHONY : bootloader.elf/fast

#=============================================================================
# Target rules for targets named size

# Build rule for target.
size: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 size
.PHONY : size

# fast build rule for target.
size/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/size.dir/build.make CMakeFiles/size.dir/build
.PHONY : size/fast

#=============================================================================
# Target rules for targets named size-files

# Build rule for target.
size-files: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 size-files
.PHONY : size-files

# fast build rule for target.
size-files/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/size-files.dir/build.make CMakeFiles/size-files.dir/build
.PHONY : size-files/fast

#=============================================================================
# Target rules for targets named size-components

# Build rule for target.
size-components: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 size-components
.PHONY : size-components

# fast build rule for target.
size-components/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/size-components.dir/build.make CMakeFiles/size-components.dir/build
.PHONY : size-components/fast

#=============================================================================
# Target rules for targets named dfu

# Build rule for target.
dfu: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 dfu
.PHONY : dfu

# fast build rule for target.
dfu/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/dfu.dir/build.make CMakeFiles/dfu.dir/build
.PHONY : dfu/fast

#=============================================================================
# Target rules for targets named dfu-list

# Build rule for target.
dfu-list: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 dfu-list
.PHONY : dfu-list

# fast build rule for target.
dfu-list/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/dfu-list.dir/build.make CMakeFiles/dfu-list.dir/build
.PHONY : dfu-list/fast

#=============================================================================
# Target rules for targets named dfu-flash

# Build rule for target.
dfu-flash: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 dfu-flash
.PHONY : dfu-flash

# fast build rule for target.
dfu-flash/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/dfu-flash.dir/build.make CMakeFiles/dfu-flash.dir/build
.PHONY : dfu-flash/fast

#=============================================================================
# Target rules for targets named __idf_xtensa

# Build rule for target.
__idf_xtensa: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 __idf_xtensa
.PHONY : __idf_xtensa

# fast build rule for target.
__idf_xtensa/fast:
	$(MAKE) $(MAKESILENT) -f esp-idf/xtensa/CMakeFiles/__idf_xtensa.dir/build.make esp-idf/xtensa/CMakeFiles/__idf_xtensa.dir/build
.PHONY : __idf_xtensa/fast

#=============================================================================
# Target rules for targets named __idf_soc

# Build rule for target.
__idf_soc: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 __idf_soc
.PHONY : __idf_soc

# fast build rule for target.
__idf_soc/fast:
	$(MAKE) $(MAKESILENT) -f esp-idf/soc/CMakeFiles/__idf_soc.dir/build.make esp-idf/soc/CMakeFiles/__idf_soc.dir/build
.PHONY : __idf_soc/fast

#=============================================================================
# Target rules for targets named __idf_micro-ecc

# Build rule for target.
__idf_micro-ecc: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 __idf_micro-ecc
.PHONY : __idf_micro-ecc

# fast build rule for target.
__idf_micro-ecc/fast:
	$(MAKE) $(MAKESILENT) -f esp-idf/micro-ecc/CMakeFiles/__idf_micro-ecc.dir/build.make esp-idf/micro-ecc/CMakeFiles/__idf_micro-ecc.dir/build
.PHONY : __idf_micro-ecc/fast

#=============================================================================
# Target rules for targets named __idf_hal

# Build rule for target.
__idf_hal: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 __idf_hal
.PHONY : __idf_hal

# fast build rule for target.
__idf_hal/fast:
	$(MAKE) $(MAKESILENT) -f esp-idf/hal/CMakeFiles/__idf_hal.dir/build.make esp-idf/hal/CMakeFiles/__idf_hal.dir/build
.PHONY : __idf_hal/fast

#=============================================================================
# Target rules for targets named __idf_spi_flash

# Build rule for target.
__idf_spi_flash: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 __idf_spi_flash
.PHONY : __idf_spi_flash

# fast build rule for target.
__idf_spi_flash/fast:
	$(MAKE) $(MAKESILENT) -f esp-idf/spi_flash/CMakeFiles/__idf_spi_flash.dir/build.make esp-idf/spi_flash/CMakeFiles/__idf_spi_flash.dir/build
.PHONY : __idf_spi_flash/fast

#=============================================================================
# Target rules for targets named __idf_esp_bootloader_format

# Build rule for target.
__idf_esp_bootloader_format: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 __idf_esp_bootloader_format
.PHONY : __idf_esp_bootloader_format

# fast build rule for target.
__idf_esp_bootloader_format/fast:
	$(MAKE) $(MAKESILENT) -f esp-idf/esp_bootloader_format/CMakeFiles/__idf_esp_bootloader_format.dir/build.make esp-idf/esp_bootloader_format/CMakeFiles/__idf_esp_bootloader_format.dir/build
.PHONY : __idf_esp_bootloader_format/fast

#=============================================================================
# Target rules for targets named __idf_bootloader_support

# Build rule for target.
__idf_bootloader_support: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 __idf_bootloader_support
.PHONY : __idf_bootloader_support

# fast build rule for target.
__idf_bootloader_support/fast:
	$(MAKE) $(MAKESILENT) -f esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/build.make esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/build
.PHONY : __idf_bootloader_support/fast

#=============================================================================
# Target rules for targets named __idf_efuse

# Build rule for target.
__idf_efuse: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 __idf_efuse
.PHONY : __idf_efuse

# fast build rule for target.
__idf_efuse/fast:
	$(MAKE) $(MAKESILENT) -f esp-idf/efuse/CMakeFiles/__idf_efuse.dir/build.make esp-idf/efuse/CMakeFiles/__idf_efuse.dir/build
.PHONY : __idf_efuse/fast

#=============================================================================
# Target rules for targets named efuse-common-table

# Build rule for target.
efuse-common-table: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 efuse-common-table
.PHONY : efuse-common-table

# fast build rule for target.
efuse-common-table/fast:
	$(MAKE) $(MAKESILENT) -f esp-idf/efuse/CMakeFiles/efuse-common-table.dir/build.make esp-idf/efuse/CMakeFiles/efuse-common-table.dir/build
.PHONY : efuse-common-table/fast

#=============================================================================
# Target rules for targets named efuse_common_table

# Build rule for target.
efuse_common_table: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 efuse_common_table
.PHONY : efuse_common_table

# fast build rule for target.
efuse_common_table/fast:
	$(MAKE) $(MAKESILENT) -f esp-idf/efuse/CMakeFiles/efuse_common_table.dir/build.make esp-idf/efuse/CMakeFiles/efuse_common_table.dir/build
.PHONY : efuse_common_table/fast

#=============================================================================
# Target rules for targets named efuse-custom-table

# Build rule for target.
efuse-custom-table: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 efuse-custom-table
.PHONY : efuse-custom-table

# fast build rule for target.
efuse-custom-table/fast:
	$(MAKE) $(MAKESILENT) -f esp-idf/efuse/CMakeFiles/efuse-custom-table.dir/build.make esp-idf/efuse/CMakeFiles/efuse-custom-table.dir/build
.PHONY : efuse-custom-table/fast

#=============================================================================
# Target rules for targets named efuse_custom_table

# Build rule for target.
efuse_custom_table: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 efuse_custom_table
.PHONY : efuse_custom_table

# fast build rule for target.
efuse_custom_table/fast:
	$(MAKE) $(MAKESILENT) -f esp-idf/efuse/CMakeFiles/efuse_custom_table.dir/build.make esp-idf/efuse/CMakeFiles/efuse_custom_table.dir/build
.PHONY : efuse_custom_table/fast

#=============================================================================
# Target rules for targets named show-efuse-table

# Build rule for target.
show-efuse-table: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 show-efuse-table
.PHONY : show-efuse-table

# fast build rule for target.
show-efuse-table/fast:
	$(MAKE) $(MAKESILENT) -f esp-idf/efuse/CMakeFiles/show-efuse-table.dir/build.make esp-idf/efuse/CMakeFiles/show-efuse-table.dir/build
.PHONY : show-efuse-table/fast

#=============================================================================
# Target rules for targets named show_efuse_table

# Build rule for target.
show_efuse_table: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 show_efuse_table
.PHONY : show_efuse_table

# fast build rule for target.
show_efuse_table/fast:
	$(MAKE) $(MAKESILENT) -f esp-idf/efuse/CMakeFiles/show_efuse_table.dir/build.make esp-idf/efuse/CMakeFiles/show_efuse_table.dir/build
.PHONY : show_efuse_table/fast

#=============================================================================
# Target rules for targets named efuse_test_table

# Build rule for target.
efuse_test_table: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 efuse_test_table
.PHONY : efuse_test_table

# fast build rule for target.
efuse_test_table/fast:
	$(MAKE) $(MAKESILENT) -f esp-idf/efuse/CMakeFiles/efuse_test_table.dir/build.make esp-idf/efuse/CMakeFiles/efuse_test_table.dir/build
.PHONY : efuse_test_table/fast

#=============================================================================
# Target rules for targets named __idf_esp_system

# Build rule for target.
__idf_esp_system: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 __idf_esp_system
.PHONY : __idf_esp_system

# fast build rule for target.
__idf_esp_system/fast:
	$(MAKE) $(MAKESILENT) -f esp-idf/esp_system/CMakeFiles/__idf_esp_system.dir/build.make esp-idf/esp_system/CMakeFiles/__idf_esp_system.dir/build
.PHONY : __idf_esp_system/fast

#=============================================================================
# Target rules for targets named __idf_esp_hw_support

# Build rule for target.
__idf_esp_hw_support: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 __idf_esp_hw_support
.PHONY : __idf_esp_hw_support

# fast build rule for target.
__idf_esp_hw_support/fast:
	$(MAKE) $(MAKESILENT) -f esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/build.make esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/build
.PHONY : __idf_esp_hw_support/fast

#=============================================================================
# Target rules for targets named __idf_esp_common

# Build rule for target.
__idf_esp_common: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 __idf_esp_common
.PHONY : __idf_esp_common

# fast build rule for target.
__idf_esp_common/fast:
	$(MAKE) $(MAKESILENT) -f esp-idf/esp_common/CMakeFiles/__idf_esp_common.dir/build.make esp-idf/esp_common/CMakeFiles/__idf_esp_common.dir/build
.PHONY : __idf_esp_common/fast

#=============================================================================
# Target rules for targets named __idf_esp_rom

# Build rule for target.
__idf_esp_rom: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 __idf_esp_rom
.PHONY : __idf_esp_rom

# fast build rule for target.
__idf_esp_rom/fast:
	$(MAKE) $(MAKESILENT) -f esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/build.make esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/build
.PHONY : __idf_esp_rom/fast

#=============================================================================
# Target rules for targets named __idf_log

# Build rule for target.
__idf_log: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 __idf_log
.PHONY : __idf_log

# fast build rule for target.
__idf_log/fast:
	$(MAKE) $(MAKESILENT) -f esp-idf/log/CMakeFiles/__idf_log.dir/build.make esp-idf/log/CMakeFiles/__idf_log.dir/build
.PHONY : __idf_log/fast

#=============================================================================
# Target rules for targets named bootloader_check_size

# Build rule for target.
bootloader_check_size: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 bootloader_check_size
.PHONY : bootloader_check_size

# fast build rule for target.
bootloader_check_size/fast:
	$(MAKE) $(MAKESILENT) -f esp-idf/esptool_py/CMakeFiles/bootloader_check_size.dir/build.make esp-idf/esptool_py/CMakeFiles/bootloader_check_size.dir/build
.PHONY : bootloader_check_size/fast

#=============================================================================
# Target rules for targets named __idf_main

# Build rule for target.
__idf_main: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 __idf_main
.PHONY : __idf_main

# fast build rule for target.
__idf_main/fast:
	$(MAKE) $(MAKESILENT) -f esp-idf/main/CMakeFiles/__idf_main.dir/build.make esp-idf/main/CMakeFiles/__idf_main.dir/build
.PHONY : __idf_main/fast

project_elf_src_esp32s3.obj: project_elf_src_esp32s3.c.obj
.PHONY : project_elf_src_esp32s3.obj

# target to build an object file
project_elf_src_esp32s3.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/bootloader.elf.dir/build.make CMakeFiles/bootloader.elf.dir/project_elf_src_esp32s3.c.obj
.PHONY : project_elf_src_esp32s3.c.obj

project_elf_src_esp32s3.i: project_elf_src_esp32s3.c.i
.PHONY : project_elf_src_esp32s3.i

# target to preprocess a source file
project_elf_src_esp32s3.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/bootloader.elf.dir/build.make CMakeFiles/bootloader.elf.dir/project_elf_src_esp32s3.c.i
.PHONY : project_elf_src_esp32s3.c.i

project_elf_src_esp32s3.s: project_elf_src_esp32s3.c.s
.PHONY : project_elf_src_esp32s3.s

# target to generate assembly for a file
project_elf_src_esp32s3.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/bootloader.elf.dir/build.make CMakeFiles/bootloader.elf.dir/project_elf_src_esp32s3.c.s
.PHONY : project_elf_src_esp32s3.c.s

# Help Target
help:
	@echo "The following are some of the valid targets for this Makefile:"
	@echo "... all (the default if no target is provided)"
	@echo "... clean"
	@echo "... depend"
	@echo "... edit_cache"
	@echo "... rebuild_cache"
	@echo "... _project_elf_src"
	@echo "... app"
	@echo "... bootloader_check_size"
	@echo "... confserver"
	@echo "... dfu"
	@echo "... dfu-flash"
	@echo "... dfu-list"
	@echo "... efuse-common-table"
	@echo "... efuse-custom-table"
	@echo "... efuse_common_table"
	@echo "... efuse_custom_table"
	@echo "... efuse_test_table"
	@echo "... erase_flash"
	@echo "... gen_project_binary"
	@echo "... menuconfig"
	@echo "... merge-bin"
	@echo "... monitor"
	@echo "... save-defconfig"
	@echo "... show-efuse-table"
	@echo "... show_efuse_table"
	@echo "... size"
	@echo "... size-components"
	@echo "... size-files"
	@echo "... uf2"
	@echo "... uf2-app"
	@echo "... __idf_bootloader_support"
	@echo "... __idf_efuse"
	@echo "... __idf_esp_bootloader_format"
	@echo "... __idf_esp_common"
	@echo "... __idf_esp_hw_support"
	@echo "... __idf_esp_rom"
	@echo "... __idf_esp_system"
	@echo "... __idf_hal"
	@echo "... __idf_log"
	@echo "... __idf_main"
	@echo "... __idf_micro-ecc"
	@echo "... __idf_soc"
	@echo "... __idf_spi_flash"
	@echo "... __idf_xtensa"
	@echo "... bootloader.elf"
	@echo "... project_elf_src_esp32s3.obj"
	@echo "... project_elf_src_esp32s3.i"
	@echo "... project_elf_src_esp32s3.s"
.PHONY : help



#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system

