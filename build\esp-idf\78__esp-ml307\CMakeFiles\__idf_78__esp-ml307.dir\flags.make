# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.22

# compile CXX with /home/<USER>/.espressif/tools/xtensa-esp-elf/esp-13.2.0_20240530/xtensa-esp-elf/bin/xtensa-esp32s3-elf-g++
CXX_DEFINES = -DESP_PLATFORM -DIDF_VER=\"v5.3\" -DMBEDTLS_CONFIG_FILE=\"mbedtls/esp_config.h\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -D_POSIX_READER_WRITER_LOCKS

CXX_INCLUDES = -I/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/config -I/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/managed_components/78__esp-ml307/include -I/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/managed_components/78__esp-ml307 -I/home/<USER>/esp/esp-idf-v5.3/components/newlib/platform_include -I/home/<USER>/esp/esp-idf-v5.3/components/freertos/config/include -I/home/<USER>/esp/esp-idf-v5.3/components/freertos/config/include/freertos -I/home/<USER>/esp/esp-idf-v5.3/components/freertos/config/xtensa/include -I/home/<USER>/esp/esp-idf-v5.3/components/freertos/FreeRTOS-Kernel/include -I/home/<USER>/esp/esp-idf-v5.3/components/freertos/FreeRTOS-Kernel/portable/xtensa/include -I/home/<USER>/esp/esp-idf-v5.3/components/freertos/FreeRTOS-Kernel/portable/xtensa/include/freertos -I/home/<USER>/esp/esp-idf-v5.3/components/freertos/esp_additions/include -I/home/<USER>/esp/esp-idf-v5.3/components/esp_hw_support/include -I/home/<USER>/esp/esp-idf-v5.3/components/esp_hw_support/include/soc -I/home/<USER>/esp/esp-idf-v5.3/components/esp_hw_support/include/soc/esp32s3 -I/home/<USER>/esp/esp-idf-v5.3/components/esp_hw_support/dma/include -I/home/<USER>/esp/esp-idf-v5.3/components/esp_hw_support/ldo/include -I/home/<USER>/esp/esp-idf-v5.3/components/esp_hw_support/port/esp32s3/. -I/home/<USER>/esp/esp-idf-v5.3/components/esp_hw_support/port/esp32s3/include -I/home/<USER>/esp/esp-idf-v5.3/components/heap/include -I/home/<USER>/esp/esp-idf-v5.3/components/log/include -I/home/<USER>/esp/esp-idf-v5.3/components/soc/include -I/home/<USER>/esp/esp-idf-v5.3/components/soc/esp32s3 -I/home/<USER>/esp/esp-idf-v5.3/components/soc/esp32s3/include -I/home/<USER>/esp/esp-idf-v5.3/components/hal/platform_port/include -I/home/<USER>/esp/esp-idf-v5.3/components/hal/esp32s3/include -I/home/<USER>/esp/esp-idf-v5.3/components/hal/include -I/home/<USER>/esp/esp-idf-v5.3/components/esp_rom/include -I/home/<USER>/esp/esp-idf-v5.3/components/esp_rom/include/esp32s3 -I/home/<USER>/esp/esp-idf-v5.3/components/esp_rom/esp32s3 -I/home/<USER>/esp/esp-idf-v5.3/components/esp_common/include -I/home/<USER>/esp/esp-idf-v5.3/components/esp_system/include -I/home/<USER>/esp/esp-idf-v5.3/components/esp_system/port/soc -I/home/<USER>/esp/esp-idf-v5.3/components/esp_system/port/include/private -I/home/<USER>/esp/esp-idf-v5.3/components/xtensa/esp32s3/include -I/home/<USER>/esp/esp-idf-v5.3/components/xtensa/include -I/home/<USER>/esp/esp-idf-v5.3/components/xtensa/deprecated_include -I/home/<USER>/esp/esp-idf-v5.3/components/esp_timer/include -I/home/<USER>/esp/esp-idf-v5.3/components/lwip/include -I/home/<USER>/esp/esp-idf-v5.3/components/lwip/include/apps -I/home/<USER>/esp/esp-idf-v5.3/components/lwip/include/apps/sntp -I/home/<USER>/esp/esp-idf-v5.3/components/lwip/lwip/src/include -I/home/<USER>/esp/esp-idf-v5.3/components/lwip/port/include -I/home/<USER>/esp/esp-idf-v5.3/components/lwip/port/freertos/include -I/home/<USER>/esp/esp-idf-v5.3/components/lwip/port/esp32xx/include -I/home/<USER>/esp/esp-idf-v5.3/components/lwip/port/esp32xx/include/arch -I/home/<USER>/esp/esp-idf-v5.3/components/lwip/port/esp32xx/include/sys -I/home/<USER>/esp/esp-idf-v5.3/components/esp_driver_gpio/include -I/home/<USER>/esp/esp-idf-v5.3/components/esp_driver_uart/include -I/home/<USER>/esp/esp-idf-v5.3/components/vfs/include -I/home/<USER>/esp/esp-idf-v5.3/components/esp-tls -I/home/<USER>/esp/esp-idf-v5.3/components/esp-tls/esp-tls-crypto -I/home/<USER>/esp/esp-idf-v5.3/components/mbedtls/port/include -I/home/<USER>/esp/esp-idf-v5.3/components/mbedtls/mbedtls/include -I/home/<USER>/esp/esp-idf-v5.3/components/mbedtls/mbedtls/library -I/home/<USER>/esp/esp-idf-v5.3/components/mbedtls/esp_crt_bundle/include -I/home/<USER>/esp/esp-idf-v5.3/components/mbedtls/mbedtls/3rdparty/everest/include -I/home/<USER>/esp/esp-idf-v5.3/components/mbedtls/mbedtls/3rdparty/p256-m -I/home/<USER>/esp/esp-idf-v5.3/components/mbedtls/mbedtls/3rdparty/p256-m/p256-m -I/home/<USER>/esp/esp-idf-v5.3/components/esp_http_client/include -I/home/<USER>/esp/esp-idf-v5.3/components/esp_event/include -I/home/<USER>/esp/esp-idf-v5.3/components/mqtt/esp-mqtt/include -I/home/<USER>/esp/esp-idf-v5.3/components/tcp_transport/include

CXX_FLAGS = -mlongcalls  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -mdisable-hardware-atomics -Og -fno-shrink-wrap -fmacro-prefix-map=/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project=. -fmacro-prefix-map=/home/<USER>/esp/esp-idf-v5.3=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -std=gnu++2b -fexceptions -fno-rtti

