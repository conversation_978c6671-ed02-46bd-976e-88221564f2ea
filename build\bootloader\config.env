{"COMPONENT_KCONFIGS": "/home/<USER>/esp/esp-idf-v5.3/components/efuse/Kconfig;/home/<USER>/esp/esp-idf-v5.3/components/esp_common/Kconfig;/home/<USER>/esp/esp-idf-v5.3/components/esp_hw_support/Kconfig;/home/<USER>/esp/esp-idf-v5.3/components/esp_system/Kconfig;/home/<USER>/esp/esp-idf-v5.3/components/freertos/Kconfig;/home/<USER>/esp/esp-idf-v5.3/components/hal/Kconfig;/home/<USER>/esp/esp-idf-v5.3/components/log/Kconfig;/home/<USER>/esp/esp-idf-v5.3/components/newlib/Kconfig;/home/<USER>/esp/esp-idf-v5.3/components/soc/Kconfig;/home/<USER>/esp/esp-idf-v5.3/components/spi_flash/Kconfig", "COMPONENT_KCONFIGS_PROJBUILD": "/home/<USER>/esp/esp-idf-v5.3/components/bootloader/Kconfig.projbuild;/home/<USER>/esp/esp-idf-v5.3/components/esp_app_format/Kconfig.projbuild;/home/<USER>/esp/esp-idf-v5.3/components/esp_rom/Kconfig.projbuild;/home/<USER>/esp/esp-idf-v5.3/components/esptool_py/Kconfig.projbuild;/home/<USER>/esp/esp-idf-v5.3/components/partition_table/Kconfig.projbuild", "COMPONENT_SDKCONFIG_RENAMES": "/home/<USER>/esp/esp-idf-v5.3/components/bootloader/sdkconfig.rename;/home/<USER>/esp/esp-idf-v5.3/components/esp_hw_support/sdkconfig.rename;/home/<USER>/esp/esp-idf-v5.3/components/esp_hw_support/sdkconfig.rename.esp32s3;/home/<USER>/esp/esp-idf-v5.3/components/esp_system/sdkconfig.rename;/home/<USER>/esp/esp-idf-v5.3/components/esp_system/sdkconfig.rename.esp32s3;/home/<USER>/esp/esp-idf-v5.3/components/esptool_py/sdkconfig.rename;/home/<USER>/esp/esp-idf-v5.3/components/freertos/sdkconfig.rename;/home/<USER>/esp/esp-idf-v5.3/components/hal/sdkconfig.rename;/home/<USER>/esp/esp-idf-v5.3/components/newlib/sdkconfig.rename.esp32s3;/home/<USER>/esp/esp-idf-v5.3/components/spi_flash/sdkconfig.rename", "IDF_TARGET": "esp32s3", "IDF_TOOLCHAIN": "gcc", "IDF_VERSION": "5.3.0", "IDF_ENV_FPGA": "", "IDF_PATH": "/home/<USER>/esp/esp-idf-v5.3", "COMPONENT_KCONFIGS_SOURCE_FILE": "/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/bootloader/kconfigs.in", "COMPONENT_KCONFIGS_PROJBUILD_SOURCE_FILE": "/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/bootloader/kconfigs_projbuild.in"}