# Target labels
 bootloader
# Source files and their labels
/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles/bootloader
/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles/bootloader.rule
/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles/bootloader-complete.rule
/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/bootloader-prefix/src/bootloader-stamp/bootloader-build.rule
/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/bootloader-prefix/src/bootloader-stamp/bootloader-configure.rule
/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/bootloader-prefix/src/bootloader-stamp/bootloader-download.rule
/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/bootloader-prefix/src/bootloader-stamp/bootloader-install.rule
/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/bootloader-prefix/src/bootloader-stamp/bootloader-mkdir.rule
/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/bootloader-prefix/src/bootloader-stamp/bootloader-patch.rule
/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/bootloader-prefix/src/bootloader-stamp/bootloader-update.rule
