#include "iot/thing.h"
#include "iot/thing_manager.h"
#include "board.h"
#include <esp_log.h>
#include <driver/ledc.h>
#include <freertos/FreeRTOS.h>
#include <freertos/task.h>
#include <freertos/timers.h>
#include <string>
#include <ctime>

// 包含Temperature类声明
#include "iot/things/temperature.h"

#define TAG "Fan"

// L9110风扇控制引脚定义
#define FAN_PWM_GPIO GPIO_NUM_12  // 使用GPIO12作为PWM输出
#define LEDC_TIMER LEDC_TIMER_0
#define LEDC_CHANNEL LEDC_CHANNEL_0
#define LEDC_DUTY_RES LEDC_TIMER_10_BIT  // 10位分辨率，范围0-1023
#define LEDC_FREQUENCY 1000              // PWM频率1KHz

namespace iot {

// 前向声明，用于获取温度数据
class Temperature;

class Fan : public Thing {
private:
    bool power_;                // 风扇开关状态
    int speed_;                 // 风扇速度百分比(0-100)
    bool auto_mode_;           // 自动模式状态
    bool timer_enabled_;       // 定时器状态
    int timer_minutes_;        // 定时时间（分钟）
    time_t timer_end_time_;    // 定时结束时间

    // 定时器句柄
    TimerHandle_t shutdown_timer_;

    // 初始化PWM
    void InitializePwm() {
        ESP_LOGI(TAG, "开始初始化风扇PWM控制，GPIO: %d", FAN_PWM_GPIO);

        // 首先重置GPIO引脚
        esp_err_t ret = gpio_reset_pin(FAN_PWM_GPIO);
        if (ret != ESP_OK) {
            ESP_LOGE(TAG, "GPIO重置失败: %s", esp_err_to_name(ret));
        } else {
            ESP_LOGI(TAG, "GPIO %d 重置成功", FAN_PWM_GPIO);
        }

        // 配置LEDC定时器
        ledc_timer_config_t timer_conf = {
            .speed_mode = LEDC_LOW_SPEED_MODE,
            .duty_resolution = LEDC_DUTY_RES,
            .timer_num = LEDC_TIMER,
            .freq_hz = LEDC_FREQUENCY,
            .clk_cfg = LEDC_AUTO_CLK
        };

        ret = ledc_timer_config(&timer_conf);
        if (ret != ESP_OK) {
            ESP_LOGE(TAG, "LEDC定时器配置失败: %s", esp_err_to_name(ret));
            return;
        } else {
            ESP_LOGI(TAG, "LEDC定时器配置成功");
        }

        // 配置LEDC通道
        ledc_channel_config_t channel_conf = {
            .gpio_num = FAN_PWM_GPIO,
            .speed_mode = LEDC_LOW_SPEED_MODE,
            .channel = LEDC_CHANNEL,
            .timer_sel = LEDC_TIMER,
            .duty = 0,                           // 初始占空比为0
            .hpoint = 0
        };

        ret = ledc_channel_config(&channel_conf);
        if (ret != ESP_OK) {
            ESP_LOGE(TAG, "LEDC通道配置失败: %s", esp_err_to_name(ret));
            return;
        } else {
            ESP_LOGI(TAG, "LEDC通道配置成功");
        }

        // 测试PWM输出
        ESP_LOGI(TAG, "测试PWM输出...");
        SetPwmDuty(50);  // 设置50%占空比测试
        vTaskDelay(pdMS_TO_TICKS(1000));  // 等待1秒
        SetPwmDuty(0);   // 关闭输出

        ESP_LOGI(TAG, "PWM初始化完成，GPIO: %d, 频率: %dHz", FAN_PWM_GPIO, LEDC_FREQUENCY);
    }

    // 设置PWM占空比
    void SetPwmDuty(int duty_percent) {
        // 将百分比转换为LEDC占空比值（10位分辨率，范围0-1023）
        uint32_t duty = (duty_percent * 1023) / 100;
        ESP_ERROR_CHECK(ledc_set_duty(LEDC_LOW_SPEED_MODE, LEDC_CHANNEL, duty));
        ESP_ERROR_CHECK(ledc_update_duty(LEDC_LOW_SPEED_MODE, LEDC_CHANNEL));
        ESP_LOGI(TAG, "设置PWM占空比: %d%%", duty_percent);
    }

    // 定时器回调函数
    static void TimerCallback(TimerHandle_t timer) {
        Fan* fan = static_cast<Fan*>(pvTimerGetTimerID(timer));
        if (fan) {
            fan->timer_enabled_ = false;
            fan->TurnOff();
            ESP_LOGI(TAG, "定时器到期，风扇已关闭");
        }
    }

    // 自动模式下更新风速
    void UpdateAutoSpeed(float temperature) {
        if (!auto_mode_ || !power_) {
            return;
        }

        int new_speed;
        if (temperature < 25.0f) {
            new_speed = 30;  // 低于25度，风速30%
        } else if (temperature > 35.0f) {
            new_speed = 100; // 高于35度，风速100%
        } else {
            // 25-35度之间，线性调整风速从30%到100%
            float ratio = (temperature - 25.0f) / 10.0f;  // 0.0-1.0
            new_speed = 30 + (int)(ratio * 70);  // 30% + (0-70%)
        }

        if (new_speed != speed_) {
            ESP_LOGI(TAG, "自动模式：温度 %.1f°C，调整风速为 %d%%", temperature, new_speed);
            SetSpeed(new_speed);
        }
    }

public:
    Fan() : Thing("Fan", "风扇控制器"),
           power_(false),
           speed_(0),
           auto_mode_(false),
           timer_enabled_(false),
           timer_minutes_(0),
           timer_end_time_(0),
           shutdown_timer_(NULL) {

        InitializePwm();

        // 定义设备的属性
        properties_.AddBooleanProperty("power", "风扇是否开启", [this]() -> bool {
            return power_;
        });

        properties_.AddNumberProperty("speed", "风扇速度(0-100%)", [this]() -> int {
            return speed_;
        });

        properties_.AddBooleanProperty("auto_mode", "是否为自动模式", [this]() -> bool {
            return auto_mode_;
        });

        properties_.AddNumberProperty("timer", "定时剩余时间(秒)", [this]() -> int {
            return GetRemainingTime();
        });

        // 定义设备可以被远程执行的指令
        methods_.AddMethod("TurnOn", "打开风扇", ParameterList(), [this](const ParameterList& parameters) {
            TurnOn();
        });

        methods_.AddMethod("TurnOff", "关闭风扇", ParameterList(), [this](const ParameterList& parameters) {
            TurnOff();
        });

        ParameterList setSpeedParams;
        setSpeedParams.AddParameter(Parameter("speed", "风速百分比(0-100)", kValueTypeNumber));
        methods_.AddMethod("SetSpeed", "设置风速", setSpeedParams,
            [this](const ParameterList& parameters) {
                try {
                    int speed = parameters["speed"].number();
                    SetSpeed(speed);
                } catch (const std::exception& e) {
                    ESP_LOGE(TAG, "设置风速失败: %s", e.what());
                }
            });

        ParameterList setTimerParams;
        setTimerParams.AddParameter(Parameter("minutes", "定时分钟数", kValueTypeNumber));
        methods_.AddMethod("SetTimer", "设置定时关闭", setTimerParams,
            [this](const ParameterList& parameters) {
                try {
                    int minutes = parameters["minutes"].number();
                    SetTimer(minutes);
                } catch (const std::exception& e) {
                    ESP_LOGE(TAG, "设置定时失败: %s", e.what());
                }
            });

        methods_.AddMethod("CancelTimer", "取消定时", ParameterList(),
            [this](const ParameterList& parameters) {
                CancelTimer();
            });

        ParameterList setAutoModeParams;
        setAutoModeParams.AddParameter(Parameter("enabled", "是否启用", kValueTypeBoolean));
        methods_.AddMethod("SetAutoMode", "设置自动模式", setAutoModeParams,
            [this](const ParameterList& parameters) {
                try {
                    bool enabled = parameters["enabled"].boolean();
                    SetAutoMode(enabled);
                } catch (const std::exception& e) {
                    ESP_LOGE(TAG, "设置自动模式失败: %s", e.what());
                }
            });

        ESP_LOGI(TAG, "风扇控制模块初始化完成");
    }

    ~Fan() {
        if (shutdown_timer_) {
            xTimerDelete(shutdown_timer_, portMAX_DELAY);
        }
        TurnOff();
        ESP_LOGI(TAG, "风扇控制模块已释放");
    }

    // 开启风扇
    void TurnOn(int speed = 70) {
        if (!power_) {
            power_ = true;
            SetSpeed(speed);
            ESP_LOGI(TAG, "风扇已开启，风速: %d%%", speed);
        } else if (speed != speed_) {
            SetSpeed(speed);
        }
    }

    // 关闭风扇
    void TurnOff() {
        if (power_) {
            power_ = false;
            SetPwmDuty(0);
            ESP_LOGI(TAG, "风扇已关闭");
        }

        // 关闭风扇时取消定时器
        CancelTimer();
    }

    // 设置风速
    void SetSpeed(int speed) {
        // 限制风速范围在0-100%
        if (speed < 0) speed = 0;
        if (speed > 100) speed = 100;

        speed_ = speed;

        // 如果风扇开启，则设置PWM占空比
        if (power_) {
            SetPwmDuty(speed);
        }

        // 手动设置风速时，如果处于自动模式，则退出自动模式
        if (auto_mode_) {
            auto_mode_ = false;
            ESP_LOGI(TAG, "手动设置风速，已退出自动模式");
        }
    }

    // 设置定时关闭
    void SetTimer(int minutes) {
        if (minutes <= 0) {
            ESP_LOGW(TAG, "定时时间必须大于0分钟");
            return;
        }

        timer_minutes_ = minutes;
        timer_enabled_ = true;
        timer_end_time_ = time(NULL) + minutes * 60;

        ESP_LOGI(TAG, "设置定时关闭: %d分钟后", minutes);

        // 创建或重置定时器
        if (shutdown_timer_ == NULL) {
            shutdown_timer_ = xTimerCreate(
                "fan_shutdown",
                pdMS_TO_TICKS(minutes * 60 * 1000),
                pdFALSE,  // 单次触发
                this,
                TimerCallback
            );
        } else {
            xTimerChangePeriod(shutdown_timer_, pdMS_TO_TICKS(minutes * 60 * 1000), portMAX_DELAY);
        }

        xTimerStart(shutdown_timer_, portMAX_DELAY);

        // 如果风扇未开启，则自动开启
        if (!power_) {
            TurnOn();
        }
    }

    // 取消定时
    void CancelTimer() {
        if (timer_enabled_ && shutdown_timer_ != NULL) {
            xTimerStop(shutdown_timer_, portMAX_DELAY);
            timer_enabled_ = false;
            ESP_LOGI(TAG, "已取消定时关闭");
        }
    }

    // 获取剩余定时时间（秒）
    int GetRemainingTime() const {
        if (!timer_enabled_) {
            return 0;
        }

        time_t now = time(NULL);
        if (now >= timer_end_time_) {
            return 0;
        }

        return (int)(timer_end_time_ - now);
    }

    // 设置自动模式
    void SetAutoMode(bool enabled) {
        auto_mode_ = enabled;

        if (enabled) {
            ESP_LOGI(TAG, "已开启自动模式，将根据温度自动调节风速");

            // 获取当前温度并立即应用
            auto& thing_manager = iot::ThingManager::GetInstance();
            auto thing = thing_manager.GetThingByName("Temperature");
            auto temperature = static_cast<iot::Temperature*>(thing);

            if (temperature && temperature->IsValid()) {
                float current_temp = temperature->GetCurrentTemperature();
                UpdateAutoSpeed(current_temp);
            } else {
                ESP_LOGW(TAG, "无法获取当前温度，自动模式可能无法正常工作");
            }

            // 如果风扇未开启，则自动开启
            if (!power_) {
                power_ = true;
                // 风速将由UpdateAutoSpeed设置
            }
        } else {
            ESP_LOGI(TAG, "已关闭自动模式");
        }
    }

    // 温度变化回调
    void OnTemperatureChanged(float temperature) {
        if (auto_mode_) {
            UpdateAutoSpeed(temperature);
        }
    }
};

} // namespace iot

DECLARE_THING(Fan);
