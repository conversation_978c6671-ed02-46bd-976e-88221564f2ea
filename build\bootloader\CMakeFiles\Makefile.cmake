# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.22

# The generator used is:
set(CMAKE_DEPENDS_GENERATOR "Unix Makefiles")

# The top level Makefile was generated from the following files:
set(CMAKE_MAKEFILE_DEPENDS
  "CMakeCache.txt"
  "/home/<USER>/esp/esp-idf-v5.3/.git/HEAD"
  "/home/<USER>/esp/esp-idf-v5.3/.git/modules/components/bootloader/subproject/components/micro-ecc/micro-ecc/HEAD"
  "/home/<USER>/esp/esp-idf-v5.3/.git/modules/components/bt/controller/lib_esp32/HEAD"
  "/home/<USER>/esp/esp-idf-v5.3/.git/modules/components/bt/controller/lib_esp32c2/esp32c2-bt-lib/HEAD"
  "/home/<USER>/esp/esp-idf-v5.3/.git/modules/components/bt/controller/lib_esp32c3_family/HEAD"
  "/home/<USER>/esp/esp-idf-v5.3/.git/modules/components/bt/controller/lib_esp32c5/esp32c5-bt-lib/HEAD"
  "/home/<USER>/esp/esp-idf-v5.3/.git/modules/components/bt/controller/lib_esp32c6/esp32c6-bt-lib/HEAD"
  "/home/<USER>/esp/esp-idf-v5.3/.git/modules/components/bt/controller/lib_esp32h2/esp32h2-bt-lib/HEAD"
  "/home/<USER>/esp/esp-idf-v5.3/.git/modules/components/bt/esp_ble_mesh/lib/lib/HEAD"
  "/home/<USER>/esp/esp-idf-v5.3/.git/modules/components/bt/host/nimble/nimble/HEAD"
  "/home/<USER>/esp/esp-idf-v5.3/.git/modules/components/cmock/CMock/HEAD"
  "/home/<USER>/esp/esp-idf-v5.3/.git/modules/components/esp_coex/lib/HEAD"
  "/home/<USER>/esp/esp-idf-v5.3/.git/modules/components/esp_phy/lib/HEAD"
  "/home/<USER>/esp/esp-idf-v5.3/.git/modules/components/esp_wifi/lib/HEAD"
  "/home/<USER>/esp/esp-idf-v5.3/.git/modules/components/heap/tlsf/HEAD"
  "/home/<USER>/esp/esp-idf-v5.3/.git/modules/components/json/cJSON/HEAD"
  "/home/<USER>/esp/esp-idf-v5.3/.git/modules/components/lwip/lwip/HEAD"
  "/home/<USER>/esp/esp-idf-v5.3/.git/modules/components/mbedtls/mbedtls/HEAD"
  "/home/<USER>/esp/esp-idf-v5.3/.git/modules/components/mqtt/esp-mqtt/HEAD"
  "/home/<USER>/esp/esp-idf-v5.3/.git/modules/components/openthread/lib/HEAD"
  "/home/<USER>/esp/esp-idf-v5.3/.git/modules/components/openthread/openthread/HEAD"
  "/home/<USER>/esp/esp-idf-v5.3/.git/modules/components/protobuf-c/protobuf-c/HEAD"
  "/home/<USER>/esp/esp-idf-v5.3/.git/modules/components/spiffs/spiffs/HEAD"
  "/home/<USER>/esp/esp-idf-v5.3/.git/modules/components/unity/unity/HEAD"
  "/home/<USER>/esp/esp-idf-v5.3/CMakeLists.txt"
  "/home/<USER>/esp/esp-idf-v5.3/components/bootloader/CMakeLists.txt"
  "/home/<USER>/esp/esp-idf-v5.3/components/bootloader/project_include.cmake"
  "/home/<USER>/esp/esp-idf-v5.3/components/bootloader/subproject/CMakeLists.txt"
  "/home/<USER>/esp/esp-idf-v5.3/components/bootloader/subproject/components/micro-ecc/CMakeLists.txt"
  "/home/<USER>/esp/esp-idf-v5.3/components/bootloader/subproject/components/micro-ecc/micro-ecc/.git"
  "/home/<USER>/esp/esp-idf-v5.3/components/bootloader/subproject/main/CMakeLists.txt"
  "/home/<USER>/esp/esp-idf-v5.3/components/bootloader_support/CMakeLists.txt"
  "/home/<USER>/esp/esp-idf-v5.3/components/bt/controller/lib_esp32/.git"
  "/home/<USER>/esp/esp-idf-v5.3/components/bt/controller/lib_esp32c2/esp32c2-bt-lib/.git"
  "/home/<USER>/esp/esp-idf-v5.3/components/bt/controller/lib_esp32c3_family/.git"
  "/home/<USER>/esp/esp-idf-v5.3/components/bt/controller/lib_esp32c5/esp32c5-bt-lib/.git"
  "/home/<USER>/esp/esp-idf-v5.3/components/bt/controller/lib_esp32c6/esp32c6-bt-lib/.git"
  "/home/<USER>/esp/esp-idf-v5.3/components/bt/controller/lib_esp32h2/esp32h2-bt-lib/.git"
  "/home/<USER>/esp/esp-idf-v5.3/components/bt/esp_ble_mesh/lib/lib/.git"
  "/home/<USER>/esp/esp-idf-v5.3/components/bt/host/nimble/nimble/.git"
  "/home/<USER>/esp/esp-idf-v5.3/components/cmock/CMock/.git"
  "/home/<USER>/esp/esp-idf-v5.3/components/efuse/CMakeLists.txt"
  "/home/<USER>/esp/esp-idf-v5.3/components/efuse/esp32s3/sources.cmake"
  "/home/<USER>/esp/esp-idf-v5.3/components/esp_app_format/CMakeLists.txt"
  "/home/<USER>/esp/esp-idf-v5.3/components/esp_bootloader_format/CMakeLists.txt"
  "/home/<USER>/esp/esp-idf-v5.3/components/esp_coex/lib/.git"
  "/home/<USER>/esp/esp-idf-v5.3/components/esp_common/CMakeLists.txt"
  "/home/<USER>/esp/esp-idf-v5.3/components/esp_common/project_include.cmake"
  "/home/<USER>/esp/esp-idf-v5.3/components/esp_hw_support/CMakeLists.txt"
  "/home/<USER>/esp/esp-idf-v5.3/components/esp_hw_support/lowpower/CMakeLists.txt"
  "/home/<USER>/esp/esp-idf-v5.3/components/esp_hw_support/port/esp32s3/CMakeLists.txt"
  "/home/<USER>/esp/esp-idf-v5.3/components/esp_phy/lib/.git"
  "/home/<USER>/esp/esp-idf-v5.3/components/esp_rom/CMakeLists.txt"
  "/home/<USER>/esp/esp-idf-v5.3/components/esp_system/CMakeLists.txt"
  "/home/<USER>/esp/esp-idf-v5.3/components/esp_wifi/lib/.git"
  "/home/<USER>/esp/esp-idf-v5.3/components/esptool_py/CMakeLists.txt"
  "/home/<USER>/esp/esp-idf-v5.3/components/esptool_py/espefuse.cmake"
  "/home/<USER>/esp/esp-idf-v5.3/components/esptool_py/project_include.cmake"
  "/home/<USER>/esp/esp-idf-v5.3/components/freertos/CMakeLists.txt"
  "/home/<USER>/esp/esp-idf-v5.3/components/hal/CMakeLists.txt"
  "/home/<USER>/esp/esp-idf-v5.3/components/heap/tlsf/.git"
  "/home/<USER>/esp/esp-idf-v5.3/components/json/cJSON/.git"
  "/home/<USER>/esp/esp-idf-v5.3/components/log/CMakeLists.txt"
  "/home/<USER>/esp/esp-idf-v5.3/components/lwip/lwip/.git"
  "/home/<USER>/esp/esp-idf-v5.3/components/mbedtls/mbedtls/.git"
  "/home/<USER>/esp/esp-idf-v5.3/components/mqtt/esp-mqtt/.git"
  "/home/<USER>/esp/esp-idf-v5.3/components/newlib/CMakeLists.txt"
  "/home/<USER>/esp/esp-idf-v5.3/components/newlib/project_include.cmake"
  "/home/<USER>/esp/esp-idf-v5.3/components/openthread/lib/.git"
  "/home/<USER>/esp/esp-idf-v5.3/components/openthread/openthread/.git"
  "/home/<USER>/esp/esp-idf-v5.3/components/partition_table/CMakeLists.txt"
  "/home/<USER>/esp/esp-idf-v5.3/components/partition_table/project_include.cmake"
  "/home/<USER>/esp/esp-idf-v5.3/components/protobuf-c/protobuf-c/.git"
  "/home/<USER>/esp/esp-idf-v5.3/components/soc/CMakeLists.txt"
  "/home/<USER>/esp/esp-idf-v5.3/components/spi_flash/CMakeLists.txt"
  "/home/<USER>/esp/esp-idf-v5.3/components/spiffs/spiffs/.git"
  "/home/<USER>/esp/esp-idf-v5.3/components/unity/unity/.git"
  "/home/<USER>/esp/esp-idf-v5.3/components/xtensa/CMakeLists.txt"
  "/home/<USER>/esp/esp-idf-v5.3/components/xtensa/project_include.cmake"
  "/home/<USER>/esp/esp-idf-v5.3/tools/cmake/build.cmake"
  "/home/<USER>/esp/esp-idf-v5.3/tools/cmake/component.cmake"
  "/home/<USER>/esp/esp-idf-v5.3/tools/cmake/depgraph.cmake"
  "/home/<USER>/esp/esp-idf-v5.3/tools/cmake/dfu.cmake"
  "/home/<USER>/esp/esp-idf-v5.3/tools/cmake/git_submodules.cmake"
  "/home/<USER>/esp/esp-idf-v5.3/tools/cmake/idf.cmake"
  "/home/<USER>/esp/esp-idf-v5.3/tools/cmake/kconfig.cmake"
  "/home/<USER>/esp/esp-idf-v5.3/tools/cmake/ldgen.cmake"
  "/home/<USER>/esp/esp-idf-v5.3/tools/cmake/project.cmake"
  "/home/<USER>/esp/esp-idf-v5.3/tools/cmake/project_description.json.in"
  "/home/<USER>/esp/esp-idf-v5.3/tools/cmake/targets.cmake"
  "/home/<USER>/esp/esp-idf-v5.3/tools/cmake/third_party/GetGitRevisionDescription.cmake"
  "/home/<USER>/esp/esp-idf-v5.3/tools/cmake/third_party/GetGitRevisionDescription.cmake.in"
  "/home/<USER>/esp/esp-idf-v5.3/tools/cmake/tool_version_check.cmake"
  "/home/<USER>/esp/esp-idf-v5.3/tools/cmake/toolchain-esp32s3.cmake"
  "/home/<USER>/esp/esp-idf-v5.3/tools/cmake/utilities.cmake"
  "/home/<USER>/esp/esp-idf-v5.3/tools/cmake/version.cmake"
  "/home/<USER>/esp/esp-idf-v5.3/tools/kconfig_new/confgen.py"
  "/home/<USER>/esp/esp-idf-v5.3/tools/kconfig_new/config.env.in"
  "CMakeFiles/3.22.1/CMakeASMCompiler.cmake"
  "CMakeFiles/3.22.1/CMakeCCompiler.cmake"
  "CMakeFiles/3.22.1/CMakeCXXCompiler.cmake"
  "CMakeFiles/3.22.1/CMakeSystem.cmake"
  "CMakeFiles/git-data/grabRef.cmake"
  "config/sdkconfig.cmake"
  "config/sdkconfig.h"
  "/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/sdkconfig"
  "/usr/share/cmake-3.22/Modules/CMakeASMCompiler.cmake.in"
  "/usr/share/cmake-3.22/Modules/CMakeASMInformation.cmake"
  "/usr/share/cmake-3.22/Modules/CMakeCCompiler.cmake.in"
  "/usr/share/cmake-3.22/Modules/CMakeCCompilerABI.c"
  "/usr/share/cmake-3.22/Modules/CMakeCInformation.cmake"
  "/usr/share/cmake-3.22/Modules/CMakeCXXCompiler.cmake.in"
  "/usr/share/cmake-3.22/Modules/CMakeCXXCompilerABI.cpp"
  "/usr/share/cmake-3.22/Modules/CMakeCXXInformation.cmake"
  "/usr/share/cmake-3.22/Modules/CMakeCheckCompilerFlagCommonPatterns.cmake"
  "/usr/share/cmake-3.22/Modules/CMakeCommonLanguageInclude.cmake"
  "/usr/share/cmake-3.22/Modules/CMakeCompilerIdDetection.cmake"
  "/usr/share/cmake-3.22/Modules/CMakeDetermineASMCompiler.cmake"
  "/usr/share/cmake-3.22/Modules/CMakeDetermineCCompiler.cmake"
  "/usr/share/cmake-3.22/Modules/CMakeDetermineCXXCompiler.cmake"
  "/usr/share/cmake-3.22/Modules/CMakeDetermineCompileFeatures.cmake"
  "/usr/share/cmake-3.22/Modules/CMakeDetermineCompiler.cmake"
  "/usr/share/cmake-3.22/Modules/CMakeDetermineCompilerABI.cmake"
  "/usr/share/cmake-3.22/Modules/CMakeDetermineCompilerId.cmake"
  "/usr/share/cmake-3.22/Modules/CMakeDetermineSystem.cmake"
  "/usr/share/cmake-3.22/Modules/CMakeFindBinUtils.cmake"
  "/usr/share/cmake-3.22/Modules/CMakeGenericSystem.cmake"
  "/usr/share/cmake-3.22/Modules/CMakeInitializeConfigs.cmake"
  "/usr/share/cmake-3.22/Modules/CMakeLanguageInformation.cmake"
  "/usr/share/cmake-3.22/Modules/CMakeParseImplicitIncludeInfo.cmake"
  "/usr/share/cmake-3.22/Modules/CMakeParseImplicitLinkInfo.cmake"
  "/usr/share/cmake-3.22/Modules/CMakeParseLibraryArchitecture.cmake"
  "/usr/share/cmake-3.22/Modules/CMakeSystem.cmake.in"
  "/usr/share/cmake-3.22/Modules/CMakeSystemSpecificInformation.cmake"
  "/usr/share/cmake-3.22/Modules/CMakeSystemSpecificInitialize.cmake"
  "/usr/share/cmake-3.22/Modules/CMakeTestASMCompiler.cmake"
  "/usr/share/cmake-3.22/Modules/CMakeTestCCompiler.cmake"
  "/usr/share/cmake-3.22/Modules/CMakeTestCXXCompiler.cmake"
  "/usr/share/cmake-3.22/Modules/CMakeTestCompilerCommon.cmake"
  "/usr/share/cmake-3.22/Modules/CMakeUnixFindMake.cmake"
  "/usr/share/cmake-3.22/Modules/CheckCCompilerFlag.cmake"
  "/usr/share/cmake-3.22/Modules/CheckCSourceCompiles.cmake"
  "/usr/share/cmake-3.22/Modules/CheckCXXCompilerFlag.cmake"
  "/usr/share/cmake-3.22/Modules/CheckCXXSourceCompiles.cmake"
  "/usr/share/cmake-3.22/Modules/Compiler/ADSP-DetermineCompiler.cmake"
  "/usr/share/cmake-3.22/Modules/Compiler/ARMCC-DetermineCompiler.cmake"
  "/usr/share/cmake-3.22/Modules/Compiler/ARMClang-DetermineCompiler.cmake"
  "/usr/share/cmake-3.22/Modules/Compiler/AppleClang-DetermineCompiler.cmake"
  "/usr/share/cmake-3.22/Modules/Compiler/Borland-DetermineCompiler.cmake"
  "/usr/share/cmake-3.22/Modules/Compiler/Bruce-C-DetermineCompiler.cmake"
  "/usr/share/cmake-3.22/Modules/Compiler/CMakeCommonCompilerMacros.cmake"
  "/usr/share/cmake-3.22/Modules/Compiler/Clang-DetermineCompiler.cmake"
  "/usr/share/cmake-3.22/Modules/Compiler/Clang-DetermineCompilerInternal.cmake"
  "/usr/share/cmake-3.22/Modules/Compiler/Comeau-CXX-DetermineCompiler.cmake"
  "/usr/share/cmake-3.22/Modules/Compiler/Compaq-C-DetermineCompiler.cmake"
  "/usr/share/cmake-3.22/Modules/Compiler/Compaq-CXX-DetermineCompiler.cmake"
  "/usr/share/cmake-3.22/Modules/Compiler/Cray-DetermineCompiler.cmake"
  "/usr/share/cmake-3.22/Modules/Compiler/Embarcadero-DetermineCompiler.cmake"
  "/usr/share/cmake-3.22/Modules/Compiler/Fujitsu-DetermineCompiler.cmake"
  "/usr/share/cmake-3.22/Modules/Compiler/FujitsuClang-DetermineCompiler.cmake"
  "/usr/share/cmake-3.22/Modules/Compiler/GHS-DetermineCompiler.cmake"
  "/usr/share/cmake-3.22/Modules/Compiler/GNU-ASM.cmake"
  "/usr/share/cmake-3.22/Modules/Compiler/GNU-C-DetermineCompiler.cmake"
  "/usr/share/cmake-3.22/Modules/Compiler/GNU-C.cmake"
  "/usr/share/cmake-3.22/Modules/Compiler/GNU-CXX-DetermineCompiler.cmake"
  "/usr/share/cmake-3.22/Modules/Compiler/GNU-CXX.cmake"
  "/usr/share/cmake-3.22/Modules/Compiler/GNU-FindBinUtils.cmake"
  "/usr/share/cmake-3.22/Modules/Compiler/GNU.cmake"
  "/usr/share/cmake-3.22/Modules/Compiler/HP-C-DetermineCompiler.cmake"
  "/usr/share/cmake-3.22/Modules/Compiler/HP-CXX-DetermineCompiler.cmake"
  "/usr/share/cmake-3.22/Modules/Compiler/IAR-DetermineCompiler.cmake"
  "/usr/share/cmake-3.22/Modules/Compiler/IBMCPP-C-DetermineVersionInternal.cmake"
  "/usr/share/cmake-3.22/Modules/Compiler/IBMCPP-CXX-DetermineVersionInternal.cmake"
  "/usr/share/cmake-3.22/Modules/Compiler/Intel-DetermineCompiler.cmake"
  "/usr/share/cmake-3.22/Modules/Compiler/IntelLLVM-DetermineCompiler.cmake"
  "/usr/share/cmake-3.22/Modules/Compiler/MSVC-DetermineCompiler.cmake"
  "/usr/share/cmake-3.22/Modules/Compiler/NVHPC-DetermineCompiler.cmake"
  "/usr/share/cmake-3.22/Modules/Compiler/NVIDIA-DetermineCompiler.cmake"
  "/usr/share/cmake-3.22/Modules/Compiler/OpenWatcom-DetermineCompiler.cmake"
  "/usr/share/cmake-3.22/Modules/Compiler/PGI-DetermineCompiler.cmake"
  "/usr/share/cmake-3.22/Modules/Compiler/PathScale-DetermineCompiler.cmake"
  "/usr/share/cmake-3.22/Modules/Compiler/SCO-DetermineCompiler.cmake"
  "/usr/share/cmake-3.22/Modules/Compiler/SDCC-C-DetermineCompiler.cmake"
  "/usr/share/cmake-3.22/Modules/Compiler/SunPro-C-DetermineCompiler.cmake"
  "/usr/share/cmake-3.22/Modules/Compiler/SunPro-CXX-DetermineCompiler.cmake"
  "/usr/share/cmake-3.22/Modules/Compiler/TI-DetermineCompiler.cmake"
  "/usr/share/cmake-3.22/Modules/Compiler/TinyCC-C-DetermineCompiler.cmake"
  "/usr/share/cmake-3.22/Modules/Compiler/VisualAge-C-DetermineCompiler.cmake"
  "/usr/share/cmake-3.22/Modules/Compiler/VisualAge-CXX-DetermineCompiler.cmake"
  "/usr/share/cmake-3.22/Modules/Compiler/Watcom-DetermineCompiler.cmake"
  "/usr/share/cmake-3.22/Modules/Compiler/XL-C-DetermineCompiler.cmake"
  "/usr/share/cmake-3.22/Modules/Compiler/XL-CXX-DetermineCompiler.cmake"
  "/usr/share/cmake-3.22/Modules/Compiler/XLClang-C-DetermineCompiler.cmake"
  "/usr/share/cmake-3.22/Modules/Compiler/XLClang-CXX-DetermineCompiler.cmake"
  "/usr/share/cmake-3.22/Modules/Compiler/zOS-C-DetermineCompiler.cmake"
  "/usr/share/cmake-3.22/Modules/Compiler/zOS-CXX-DetermineCompiler.cmake"
  "/usr/share/cmake-3.22/Modules/ExternalProject.cmake"
  "/usr/share/cmake-3.22/Modules/FindGit.cmake"
  "/usr/share/cmake-3.22/Modules/FindPackageHandleStandardArgs.cmake"
  "/usr/share/cmake-3.22/Modules/FindPackageMessage.cmake"
  "/usr/share/cmake-3.22/Modules/Internal/CheckCompilerFlag.cmake"
  "/usr/share/cmake-3.22/Modules/Internal/CheckSourceCompiles.cmake"
  "/usr/share/cmake-3.22/Modules/Internal/FeatureTesting.cmake"
  "/usr/share/cmake-3.22/Modules/Platform/Generic.cmake"
  )

# The corresponding makefile is:
set(CMAKE_MAKEFILE_OUTPUTS
  "Makefile"
  "CMakeFiles/cmake.check_cache"
  )

# Byproducts of CMake generate step:
set(CMAKE_MAKEFILE_PRODUCTS
  "CMakeFiles/git-data/HEAD"
  "CMakeFiles/git-data/grabRef.cmake"
  "CMakeFiles/git-data/head-ref"
  "CMakeFiles/3.22.1/CMakeSystem.cmake"
  "CMakeFiles/3.22.1/CMakeCCompiler.cmake"
  "CMakeFiles/3.22.1/CMakeCXXCompiler.cmake"
  "CMakeFiles/3.22.1/CMakeASMCompiler.cmake"
  "CMakeFiles/3.22.1/CMakeCCompiler.cmake"
  "CMakeFiles/3.22.1/CMakeCXXCompiler.cmake"
  "CMakeFiles/git-data/HEAD"
  "CMakeFiles/git-data/grabRef.cmake"
  "CMakeFiles/git-data/head-ref"
  "config.env"
  "project_description.json"
  "CMakeFiles/CMakeDirectoryInformation.cmake"
  "esp-idf/CMakeFiles/CMakeDirectoryInformation.cmake"
  "esp-idf/xtensa/CMakeFiles/CMakeDirectoryInformation.cmake"
  "esp-idf/newlib/CMakeFiles/CMakeDirectoryInformation.cmake"
  "esp-idf/soc/CMakeFiles/CMakeDirectoryInformation.cmake"
  "esp-idf/micro-ecc/CMakeFiles/CMakeDirectoryInformation.cmake"
  "esp-idf/hal/CMakeFiles/CMakeDirectoryInformation.cmake"
  "esp-idf/spi_flash/CMakeFiles/CMakeDirectoryInformation.cmake"
  "esp-idf/esp_bootloader_format/CMakeFiles/CMakeDirectoryInformation.cmake"
  "esp-idf/esp_app_format/CMakeFiles/CMakeDirectoryInformation.cmake"
  "esp-idf/bootloader_support/CMakeFiles/CMakeDirectoryInformation.cmake"
  "esp-idf/efuse/CMakeFiles/CMakeDirectoryInformation.cmake"
  "esp-idf/esp_system/CMakeFiles/CMakeDirectoryInformation.cmake"
  "esp-idf/esp_hw_support/CMakeFiles/CMakeDirectoryInformation.cmake"
  "esp-idf/esp_hw_support/port/esp32s3/CMakeFiles/CMakeDirectoryInformation.cmake"
  "esp-idf/esp_hw_support/lowpower/CMakeFiles/CMakeDirectoryInformation.cmake"
  "esp-idf/esp_common/CMakeFiles/CMakeDirectoryInformation.cmake"
  "esp-idf/esp_rom/CMakeFiles/CMakeDirectoryInformation.cmake"
  "esp-idf/log/CMakeFiles/CMakeDirectoryInformation.cmake"
  "esp-idf/esptool_py/CMakeFiles/CMakeDirectoryInformation.cmake"
  "esp-idf/partition_table/CMakeFiles/CMakeDirectoryInformation.cmake"
  "esp-idf/bootloader/CMakeFiles/CMakeDirectoryInformation.cmake"
  "esp-idf/freertos/CMakeFiles/CMakeDirectoryInformation.cmake"
  "esp-idf/main/CMakeFiles/CMakeDirectoryInformation.cmake"
  )

# Dependency information for all targets:
set(CMAKE_DEPEND_INFO_FILES
  "CMakeFiles/menuconfig.dir/DependInfo.cmake"
  "CMakeFiles/confserver.dir/DependInfo.cmake"
  "CMakeFiles/save-defconfig.dir/DependInfo.cmake"
  "CMakeFiles/gen_project_binary.dir/DependInfo.cmake"
  "CMakeFiles/app.dir/DependInfo.cmake"
  "CMakeFiles/erase_flash.dir/DependInfo.cmake"
  "CMakeFiles/uf2.dir/DependInfo.cmake"
  "CMakeFiles/uf2-app.dir/DependInfo.cmake"
  "CMakeFiles/merge-bin.dir/DependInfo.cmake"
  "CMakeFiles/monitor.dir/DependInfo.cmake"
  "CMakeFiles/_project_elf_src.dir/DependInfo.cmake"
  "CMakeFiles/bootloader.elf.dir/DependInfo.cmake"
  "CMakeFiles/size.dir/DependInfo.cmake"
  "CMakeFiles/size-files.dir/DependInfo.cmake"
  "CMakeFiles/size-components.dir/DependInfo.cmake"
  "CMakeFiles/dfu.dir/DependInfo.cmake"
  "CMakeFiles/dfu-list.dir/DependInfo.cmake"
  "CMakeFiles/dfu-flash.dir/DependInfo.cmake"
  "esp-idf/xtensa/CMakeFiles/__idf_xtensa.dir/DependInfo.cmake"
  "esp-idf/soc/CMakeFiles/__idf_soc.dir/DependInfo.cmake"
  "esp-idf/micro-ecc/CMakeFiles/__idf_micro-ecc.dir/DependInfo.cmake"
  "esp-idf/hal/CMakeFiles/__idf_hal.dir/DependInfo.cmake"
  "esp-idf/spi_flash/CMakeFiles/__idf_spi_flash.dir/DependInfo.cmake"
  "esp-idf/esp_bootloader_format/CMakeFiles/__idf_esp_bootloader_format.dir/DependInfo.cmake"
  "esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/DependInfo.cmake"
  "esp-idf/efuse/CMakeFiles/__idf_efuse.dir/DependInfo.cmake"
  "esp-idf/efuse/CMakeFiles/efuse-common-table.dir/DependInfo.cmake"
  "esp-idf/efuse/CMakeFiles/efuse_common_table.dir/DependInfo.cmake"
  "esp-idf/efuse/CMakeFiles/efuse-custom-table.dir/DependInfo.cmake"
  "esp-idf/efuse/CMakeFiles/efuse_custom_table.dir/DependInfo.cmake"
  "esp-idf/efuse/CMakeFiles/show-efuse-table.dir/DependInfo.cmake"
  "esp-idf/efuse/CMakeFiles/show_efuse_table.dir/DependInfo.cmake"
  "esp-idf/efuse/CMakeFiles/efuse_test_table.dir/DependInfo.cmake"
  "esp-idf/esp_system/CMakeFiles/__idf_esp_system.dir/DependInfo.cmake"
  "esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/DependInfo.cmake"
  "esp-idf/esp_common/CMakeFiles/__idf_esp_common.dir/DependInfo.cmake"
  "esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/DependInfo.cmake"
  "esp-idf/log/CMakeFiles/__idf_log.dir/DependInfo.cmake"
  "esp-idf/esptool_py/CMakeFiles/bootloader_check_size.dir/DependInfo.cmake"
  "esp-idf/main/CMakeFiles/__idf_main.dir/DependInfo.cmake"
  )
