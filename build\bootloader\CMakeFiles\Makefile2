# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.22

# Default target executed when no arguments are given to make.
default_target: all
.PHONY : default_target

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/esp/esp-idf-v5.3/components/bootloader/subproject

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/bootloader

#=============================================================================
# Directory level rules for the build root directory

# The main recursive "all" target.
all: CMakeFiles/app.dir/all
all: CMakeFiles/bootloader.elf.dir/all
all: esp-idf/all
.PHONY : all

# The main recursive "preinstall" target.
preinstall: esp-idf/preinstall
.PHONY : preinstall

# The main recursive "clean" target.
clean: CMakeFiles/menuconfig.dir/clean
clean: CMakeFiles/confserver.dir/clean
clean: CMakeFiles/save-defconfig.dir/clean
clean: CMakeFiles/gen_project_binary.dir/clean
clean: CMakeFiles/app.dir/clean
clean: CMakeFiles/erase_flash.dir/clean
clean: CMakeFiles/uf2.dir/clean
clean: CMakeFiles/uf2-app.dir/clean
clean: CMakeFiles/merge-bin.dir/clean
clean: CMakeFiles/monitor.dir/clean
clean: CMakeFiles/_project_elf_src.dir/clean
clean: CMakeFiles/bootloader.elf.dir/clean
clean: CMakeFiles/size.dir/clean
clean: CMakeFiles/size-files.dir/clean
clean: CMakeFiles/size-components.dir/clean
clean: CMakeFiles/dfu.dir/clean
clean: CMakeFiles/dfu-list.dir/clean
clean: CMakeFiles/dfu-flash.dir/clean
clean: esp-idf/clean
	$(CMAKE_COMMAND) -P CMakeFiles/cmake_directory_clean.cmake
.PHONY : clean

#=============================================================================
# Directory level rules for directory esp-idf

# Recursive "all" directory target.
esp-idf/all: esp-idf/xtensa/all
esp-idf/all: esp-idf/newlib/all
esp-idf/all: esp-idf/soc/all
esp-idf/all: esp-idf/micro-ecc/all
esp-idf/all: esp-idf/hal/all
esp-idf/all: esp-idf/spi_flash/all
esp-idf/all: esp-idf/esp_bootloader_format/all
esp-idf/all: esp-idf/esp_app_format/all
esp-idf/all: esp-idf/bootloader_support/all
esp-idf/all: esp-idf/efuse/all
esp-idf/all: esp-idf/esp_system/all
esp-idf/all: esp-idf/esp_hw_support/all
esp-idf/all: esp-idf/esp_common/all
esp-idf/all: esp-idf/esp_rom/all
esp-idf/all: esp-idf/log/all
esp-idf/all: esp-idf/esptool_py/all
esp-idf/all: esp-idf/partition_table/all
esp-idf/all: esp-idf/bootloader/all
esp-idf/all: esp-idf/freertos/all
esp-idf/all: esp-idf/main/all
.PHONY : esp-idf/all

# Recursive "preinstall" directory target.
esp-idf/preinstall: esp-idf/xtensa/preinstall
esp-idf/preinstall: esp-idf/newlib/preinstall
esp-idf/preinstall: esp-idf/soc/preinstall
esp-idf/preinstall: esp-idf/micro-ecc/preinstall
esp-idf/preinstall: esp-idf/hal/preinstall
esp-idf/preinstall: esp-idf/spi_flash/preinstall
esp-idf/preinstall: esp-idf/esp_bootloader_format/preinstall
esp-idf/preinstall: esp-idf/esp_app_format/preinstall
esp-idf/preinstall: esp-idf/bootloader_support/preinstall
esp-idf/preinstall: esp-idf/efuse/preinstall
esp-idf/preinstall: esp-idf/esp_system/preinstall
esp-idf/preinstall: esp-idf/esp_hw_support/preinstall
esp-idf/preinstall: esp-idf/esp_common/preinstall
esp-idf/preinstall: esp-idf/esp_rom/preinstall
esp-idf/preinstall: esp-idf/log/preinstall
esp-idf/preinstall: esp-idf/esptool_py/preinstall
esp-idf/preinstall: esp-idf/partition_table/preinstall
esp-idf/preinstall: esp-idf/bootloader/preinstall
esp-idf/preinstall: esp-idf/freertos/preinstall
esp-idf/preinstall: esp-idf/main/preinstall
.PHONY : esp-idf/preinstall

# Recursive "clean" directory target.
esp-idf/clean: esp-idf/xtensa/clean
esp-idf/clean: esp-idf/newlib/clean
esp-idf/clean: esp-idf/soc/clean
esp-idf/clean: esp-idf/micro-ecc/clean
esp-idf/clean: esp-idf/hal/clean
esp-idf/clean: esp-idf/spi_flash/clean
esp-idf/clean: esp-idf/esp_bootloader_format/clean
esp-idf/clean: esp-idf/esp_app_format/clean
esp-idf/clean: esp-idf/bootloader_support/clean
esp-idf/clean: esp-idf/efuse/clean
esp-idf/clean: esp-idf/esp_system/clean
esp-idf/clean: esp-idf/esp_hw_support/clean
esp-idf/clean: esp-idf/esp_common/clean
esp-idf/clean: esp-idf/esp_rom/clean
esp-idf/clean: esp-idf/log/clean
esp-idf/clean: esp-idf/esptool_py/clean
esp-idf/clean: esp-idf/partition_table/clean
esp-idf/clean: esp-idf/bootloader/clean
esp-idf/clean: esp-idf/freertos/clean
esp-idf/clean: esp-idf/main/clean
.PHONY : esp-idf/clean

#=============================================================================
# Directory level rules for directory esp-idf/bootloader

# Recursive "all" directory target.
esp-idf/bootloader/all:
.PHONY : esp-idf/bootloader/all

# Recursive "preinstall" directory target.
esp-idf/bootloader/preinstall:
.PHONY : esp-idf/bootloader/preinstall

# Recursive "clean" directory target.
esp-idf/bootloader/clean:
.PHONY : esp-idf/bootloader/clean

#=============================================================================
# Directory level rules for directory esp-idf/bootloader_support

# Recursive "all" directory target.
esp-idf/bootloader_support/all: esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/all
.PHONY : esp-idf/bootloader_support/all

# Recursive "preinstall" directory target.
esp-idf/bootloader_support/preinstall:
.PHONY : esp-idf/bootloader_support/preinstall

# Recursive "clean" directory target.
esp-idf/bootloader_support/clean: esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/clean
.PHONY : esp-idf/bootloader_support/clean

#=============================================================================
# Directory level rules for directory esp-idf/efuse

# Recursive "all" directory target.
esp-idf/efuse/all: esp-idf/efuse/CMakeFiles/__idf_efuse.dir/all
.PHONY : esp-idf/efuse/all

# Recursive "preinstall" directory target.
esp-idf/efuse/preinstall:
.PHONY : esp-idf/efuse/preinstall

# Recursive "clean" directory target.
esp-idf/efuse/clean: esp-idf/efuse/CMakeFiles/__idf_efuse.dir/clean
esp-idf/efuse/clean: esp-idf/efuse/CMakeFiles/efuse-common-table.dir/clean
esp-idf/efuse/clean: esp-idf/efuse/CMakeFiles/efuse_common_table.dir/clean
esp-idf/efuse/clean: esp-idf/efuse/CMakeFiles/efuse-custom-table.dir/clean
esp-idf/efuse/clean: esp-idf/efuse/CMakeFiles/efuse_custom_table.dir/clean
esp-idf/efuse/clean: esp-idf/efuse/CMakeFiles/show-efuse-table.dir/clean
esp-idf/efuse/clean: esp-idf/efuse/CMakeFiles/show_efuse_table.dir/clean
esp-idf/efuse/clean: esp-idf/efuse/CMakeFiles/efuse_test_table.dir/clean
.PHONY : esp-idf/efuse/clean

#=============================================================================
# Directory level rules for directory esp-idf/esp_app_format

# Recursive "all" directory target.
esp-idf/esp_app_format/all:
.PHONY : esp-idf/esp_app_format/all

# Recursive "preinstall" directory target.
esp-idf/esp_app_format/preinstall:
.PHONY : esp-idf/esp_app_format/preinstall

# Recursive "clean" directory target.
esp-idf/esp_app_format/clean:
.PHONY : esp-idf/esp_app_format/clean

#=============================================================================
# Directory level rules for directory esp-idf/esp_bootloader_format

# Recursive "all" directory target.
esp-idf/esp_bootloader_format/all: esp-idf/esp_bootloader_format/CMakeFiles/__idf_esp_bootloader_format.dir/all
.PHONY : esp-idf/esp_bootloader_format/all

# Recursive "preinstall" directory target.
esp-idf/esp_bootloader_format/preinstall:
.PHONY : esp-idf/esp_bootloader_format/preinstall

# Recursive "clean" directory target.
esp-idf/esp_bootloader_format/clean: esp-idf/esp_bootloader_format/CMakeFiles/__idf_esp_bootloader_format.dir/clean
.PHONY : esp-idf/esp_bootloader_format/clean

#=============================================================================
# Directory level rules for directory esp-idf/esp_common

# Recursive "all" directory target.
esp-idf/esp_common/all: esp-idf/esp_common/CMakeFiles/__idf_esp_common.dir/all
.PHONY : esp-idf/esp_common/all

# Recursive "preinstall" directory target.
esp-idf/esp_common/preinstall:
.PHONY : esp-idf/esp_common/preinstall

# Recursive "clean" directory target.
esp-idf/esp_common/clean: esp-idf/esp_common/CMakeFiles/__idf_esp_common.dir/clean
.PHONY : esp-idf/esp_common/clean

#=============================================================================
# Directory level rules for directory esp-idf/esp_hw_support

# Recursive "all" directory target.
esp-idf/esp_hw_support/all: esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/all
esp-idf/esp_hw_support/all: esp-idf/esp_hw_support/port/esp32s3/all
esp-idf/esp_hw_support/all: esp-idf/esp_hw_support/lowpower/all
.PHONY : esp-idf/esp_hw_support/all

# Recursive "preinstall" directory target.
esp-idf/esp_hw_support/preinstall: esp-idf/esp_hw_support/port/esp32s3/preinstall
esp-idf/esp_hw_support/preinstall: esp-idf/esp_hw_support/lowpower/preinstall
.PHONY : esp-idf/esp_hw_support/preinstall

# Recursive "clean" directory target.
esp-idf/esp_hw_support/clean: esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/clean
esp-idf/esp_hw_support/clean: esp-idf/esp_hw_support/port/esp32s3/clean
esp-idf/esp_hw_support/clean: esp-idf/esp_hw_support/lowpower/clean
.PHONY : esp-idf/esp_hw_support/clean

#=============================================================================
# Directory level rules for directory esp-idf/esp_hw_support/lowpower

# Recursive "all" directory target.
esp-idf/esp_hw_support/lowpower/all:
.PHONY : esp-idf/esp_hw_support/lowpower/all

# Recursive "preinstall" directory target.
esp-idf/esp_hw_support/lowpower/preinstall:
.PHONY : esp-idf/esp_hw_support/lowpower/preinstall

# Recursive "clean" directory target.
esp-idf/esp_hw_support/lowpower/clean:
.PHONY : esp-idf/esp_hw_support/lowpower/clean

#=============================================================================
# Directory level rules for directory esp-idf/esp_hw_support/port/esp32s3

# Recursive "all" directory target.
esp-idf/esp_hw_support/port/esp32s3/all:
.PHONY : esp-idf/esp_hw_support/port/esp32s3/all

# Recursive "preinstall" directory target.
esp-idf/esp_hw_support/port/esp32s3/preinstall:
.PHONY : esp-idf/esp_hw_support/port/esp32s3/preinstall

# Recursive "clean" directory target.
esp-idf/esp_hw_support/port/esp32s3/clean:
.PHONY : esp-idf/esp_hw_support/port/esp32s3/clean

#=============================================================================
# Directory level rules for directory esp-idf/esp_rom

# Recursive "all" directory target.
esp-idf/esp_rom/all: esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/all
.PHONY : esp-idf/esp_rom/all

# Recursive "preinstall" directory target.
esp-idf/esp_rom/preinstall:
.PHONY : esp-idf/esp_rom/preinstall

# Recursive "clean" directory target.
esp-idf/esp_rom/clean: esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/clean
.PHONY : esp-idf/esp_rom/clean

#=============================================================================
# Directory level rules for directory esp-idf/esp_system

# Recursive "all" directory target.
esp-idf/esp_system/all: esp-idf/esp_system/CMakeFiles/__idf_esp_system.dir/all
.PHONY : esp-idf/esp_system/all

# Recursive "preinstall" directory target.
esp-idf/esp_system/preinstall:
.PHONY : esp-idf/esp_system/preinstall

# Recursive "clean" directory target.
esp-idf/esp_system/clean: esp-idf/esp_system/CMakeFiles/__idf_esp_system.dir/clean
.PHONY : esp-idf/esp_system/clean

#=============================================================================
# Directory level rules for directory esp-idf/esptool_py

# Recursive "all" directory target.
esp-idf/esptool_py/all:
.PHONY : esp-idf/esptool_py/all

# Recursive "preinstall" directory target.
esp-idf/esptool_py/preinstall:
.PHONY : esp-idf/esptool_py/preinstall

# Recursive "clean" directory target.
esp-idf/esptool_py/clean: esp-idf/esptool_py/CMakeFiles/bootloader_check_size.dir/clean
.PHONY : esp-idf/esptool_py/clean

#=============================================================================
# Directory level rules for directory esp-idf/freertos

# Recursive "all" directory target.
esp-idf/freertos/all:
.PHONY : esp-idf/freertos/all

# Recursive "preinstall" directory target.
esp-idf/freertos/preinstall:
.PHONY : esp-idf/freertos/preinstall

# Recursive "clean" directory target.
esp-idf/freertos/clean:
.PHONY : esp-idf/freertos/clean

#=============================================================================
# Directory level rules for directory esp-idf/hal

# Recursive "all" directory target.
esp-idf/hal/all: esp-idf/hal/CMakeFiles/__idf_hal.dir/all
.PHONY : esp-idf/hal/all

# Recursive "preinstall" directory target.
esp-idf/hal/preinstall:
.PHONY : esp-idf/hal/preinstall

# Recursive "clean" directory target.
esp-idf/hal/clean: esp-idf/hal/CMakeFiles/__idf_hal.dir/clean
.PHONY : esp-idf/hal/clean

#=============================================================================
# Directory level rules for directory esp-idf/log

# Recursive "all" directory target.
esp-idf/log/all: esp-idf/log/CMakeFiles/__idf_log.dir/all
.PHONY : esp-idf/log/all

# Recursive "preinstall" directory target.
esp-idf/log/preinstall:
.PHONY : esp-idf/log/preinstall

# Recursive "clean" directory target.
esp-idf/log/clean: esp-idf/log/CMakeFiles/__idf_log.dir/clean
.PHONY : esp-idf/log/clean

#=============================================================================
# Directory level rules for directory esp-idf/main

# Recursive "all" directory target.
esp-idf/main/all: esp-idf/main/CMakeFiles/__idf_main.dir/all
.PHONY : esp-idf/main/all

# Recursive "preinstall" directory target.
esp-idf/main/preinstall:
.PHONY : esp-idf/main/preinstall

# Recursive "clean" directory target.
esp-idf/main/clean: esp-idf/main/CMakeFiles/__idf_main.dir/clean
.PHONY : esp-idf/main/clean

#=============================================================================
# Directory level rules for directory esp-idf/micro-ecc

# Recursive "all" directory target.
esp-idf/micro-ecc/all: esp-idf/micro-ecc/CMakeFiles/__idf_micro-ecc.dir/all
.PHONY : esp-idf/micro-ecc/all

# Recursive "preinstall" directory target.
esp-idf/micro-ecc/preinstall:
.PHONY : esp-idf/micro-ecc/preinstall

# Recursive "clean" directory target.
esp-idf/micro-ecc/clean: esp-idf/micro-ecc/CMakeFiles/__idf_micro-ecc.dir/clean
.PHONY : esp-idf/micro-ecc/clean

#=============================================================================
# Directory level rules for directory esp-idf/newlib

# Recursive "all" directory target.
esp-idf/newlib/all:
.PHONY : esp-idf/newlib/all

# Recursive "preinstall" directory target.
esp-idf/newlib/preinstall:
.PHONY : esp-idf/newlib/preinstall

# Recursive "clean" directory target.
esp-idf/newlib/clean:
.PHONY : esp-idf/newlib/clean

#=============================================================================
# Directory level rules for directory esp-idf/partition_table

# Recursive "all" directory target.
esp-idf/partition_table/all:
.PHONY : esp-idf/partition_table/all

# Recursive "preinstall" directory target.
esp-idf/partition_table/preinstall:
.PHONY : esp-idf/partition_table/preinstall

# Recursive "clean" directory target.
esp-idf/partition_table/clean:
.PHONY : esp-idf/partition_table/clean

#=============================================================================
# Directory level rules for directory esp-idf/soc

# Recursive "all" directory target.
esp-idf/soc/all: esp-idf/soc/CMakeFiles/__idf_soc.dir/all
.PHONY : esp-idf/soc/all

# Recursive "preinstall" directory target.
esp-idf/soc/preinstall:
.PHONY : esp-idf/soc/preinstall

# Recursive "clean" directory target.
esp-idf/soc/clean: esp-idf/soc/CMakeFiles/__idf_soc.dir/clean
.PHONY : esp-idf/soc/clean

#=============================================================================
# Directory level rules for directory esp-idf/spi_flash

# Recursive "all" directory target.
esp-idf/spi_flash/all: esp-idf/spi_flash/CMakeFiles/__idf_spi_flash.dir/all
.PHONY : esp-idf/spi_flash/all

# Recursive "preinstall" directory target.
esp-idf/spi_flash/preinstall:
.PHONY : esp-idf/spi_flash/preinstall

# Recursive "clean" directory target.
esp-idf/spi_flash/clean: esp-idf/spi_flash/CMakeFiles/__idf_spi_flash.dir/clean
.PHONY : esp-idf/spi_flash/clean

#=============================================================================
# Directory level rules for directory esp-idf/xtensa

# Recursive "all" directory target.
esp-idf/xtensa/all: esp-idf/xtensa/CMakeFiles/__idf_xtensa.dir/all
.PHONY : esp-idf/xtensa/all

# Recursive "preinstall" directory target.
esp-idf/xtensa/preinstall:
.PHONY : esp-idf/xtensa/preinstall

# Recursive "clean" directory target.
esp-idf/xtensa/clean: esp-idf/xtensa/CMakeFiles/__idf_xtensa.dir/clean
.PHONY : esp-idf/xtensa/clean

#=============================================================================
# Target rules for target CMakeFiles/menuconfig.dir

# All Build rule for target.
CMakeFiles/menuconfig.dir/all:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/menuconfig.dir/build.make CMakeFiles/menuconfig.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/menuconfig.dir/build.make CMakeFiles/menuconfig.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/bootloader/CMakeFiles --progress-num= "Built target menuconfig"
.PHONY : CMakeFiles/menuconfig.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/menuconfig.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/bootloader/CMakeFiles 0
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/menuconfig.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/bootloader/CMakeFiles 0
.PHONY : CMakeFiles/menuconfig.dir/rule

# Convenience name for target.
menuconfig: CMakeFiles/menuconfig.dir/rule
.PHONY : menuconfig

# clean rule for target.
CMakeFiles/menuconfig.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/menuconfig.dir/build.make CMakeFiles/menuconfig.dir/clean
.PHONY : CMakeFiles/menuconfig.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/confserver.dir

# All Build rule for target.
CMakeFiles/confserver.dir/all:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/confserver.dir/build.make CMakeFiles/confserver.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/confserver.dir/build.make CMakeFiles/confserver.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/bootloader/CMakeFiles --progress-num= "Built target confserver"
.PHONY : CMakeFiles/confserver.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/confserver.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/bootloader/CMakeFiles 0
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/confserver.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/bootloader/CMakeFiles 0
.PHONY : CMakeFiles/confserver.dir/rule

# Convenience name for target.
confserver: CMakeFiles/confserver.dir/rule
.PHONY : confserver

# clean rule for target.
CMakeFiles/confserver.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/confserver.dir/build.make CMakeFiles/confserver.dir/clean
.PHONY : CMakeFiles/confserver.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/save-defconfig.dir

# All Build rule for target.
CMakeFiles/save-defconfig.dir/all:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/save-defconfig.dir/build.make CMakeFiles/save-defconfig.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/save-defconfig.dir/build.make CMakeFiles/save-defconfig.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/bootloader/CMakeFiles --progress-num= "Built target save-defconfig"
.PHONY : CMakeFiles/save-defconfig.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/save-defconfig.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/bootloader/CMakeFiles 0
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/save-defconfig.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/bootloader/CMakeFiles 0
.PHONY : CMakeFiles/save-defconfig.dir/rule

# Convenience name for target.
save-defconfig: CMakeFiles/save-defconfig.dir/rule
.PHONY : save-defconfig

# clean rule for target.
CMakeFiles/save-defconfig.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/save-defconfig.dir/build.make CMakeFiles/save-defconfig.dir/clean
.PHONY : CMakeFiles/save-defconfig.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/gen_project_binary.dir

# All Build rule for target.
CMakeFiles/gen_project_binary.dir/all: CMakeFiles/bootloader.elf.dir/all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/gen_project_binary.dir/build.make CMakeFiles/gen_project_binary.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/gen_project_binary.dir/build.make CMakeFiles/gen_project_binary.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/bootloader/CMakeFiles --progress-num=99 "Built target gen_project_binary"
.PHONY : CMakeFiles/gen_project_binary.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/gen_project_binary.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/bootloader/CMakeFiles 97
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/gen_project_binary.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/bootloader/CMakeFiles 0
.PHONY : CMakeFiles/gen_project_binary.dir/rule

# Convenience name for target.
gen_project_binary: CMakeFiles/gen_project_binary.dir/rule
.PHONY : gen_project_binary

# clean rule for target.
CMakeFiles/gen_project_binary.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/gen_project_binary.dir/build.make CMakeFiles/gen_project_binary.dir/clean
.PHONY : CMakeFiles/gen_project_binary.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/app.dir

# All Build rule for target.
CMakeFiles/app.dir/all: CMakeFiles/gen_project_binary.dir/all
CMakeFiles/app.dir/all: esp-idf/esptool_py/CMakeFiles/bootloader_check_size.dir/all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/app.dir/build.make CMakeFiles/app.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/app.dir/build.make CMakeFiles/app.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/bootloader/CMakeFiles --progress-num= "Built target app"
.PHONY : CMakeFiles/app.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/app.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/bootloader/CMakeFiles 97
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/app.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/bootloader/CMakeFiles 0
.PHONY : CMakeFiles/app.dir/rule

# Convenience name for target.
app: CMakeFiles/app.dir/rule
.PHONY : app

# clean rule for target.
CMakeFiles/app.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/app.dir/build.make CMakeFiles/app.dir/clean
.PHONY : CMakeFiles/app.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/erase_flash.dir

# All Build rule for target.
CMakeFiles/erase_flash.dir/all:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/erase_flash.dir/build.make CMakeFiles/erase_flash.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/erase_flash.dir/build.make CMakeFiles/erase_flash.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/bootloader/CMakeFiles --progress-num= "Built target erase_flash"
.PHONY : CMakeFiles/erase_flash.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/erase_flash.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/bootloader/CMakeFiles 0
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/erase_flash.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/bootloader/CMakeFiles 0
.PHONY : CMakeFiles/erase_flash.dir/rule

# Convenience name for target.
erase_flash: CMakeFiles/erase_flash.dir/rule
.PHONY : erase_flash

# clean rule for target.
CMakeFiles/erase_flash.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/erase_flash.dir/build.make CMakeFiles/erase_flash.dir/clean
.PHONY : CMakeFiles/erase_flash.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/uf2.dir

# All Build rule for target.
CMakeFiles/uf2.dir/all:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/uf2.dir/build.make CMakeFiles/uf2.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/uf2.dir/build.make CMakeFiles/uf2.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/bootloader/CMakeFiles --progress-num= "Built target uf2"
.PHONY : CMakeFiles/uf2.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/uf2.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/bootloader/CMakeFiles 0
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/uf2.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/bootloader/CMakeFiles 0
.PHONY : CMakeFiles/uf2.dir/rule

# Convenience name for target.
uf2: CMakeFiles/uf2.dir/rule
.PHONY : uf2

# clean rule for target.
CMakeFiles/uf2.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/uf2.dir/build.make CMakeFiles/uf2.dir/clean
.PHONY : CMakeFiles/uf2.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/uf2-app.dir

# All Build rule for target.
CMakeFiles/uf2-app.dir/all:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/uf2-app.dir/build.make CMakeFiles/uf2-app.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/uf2-app.dir/build.make CMakeFiles/uf2-app.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/bootloader/CMakeFiles --progress-num= "Built target uf2-app"
.PHONY : CMakeFiles/uf2-app.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/uf2-app.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/bootloader/CMakeFiles 0
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/uf2-app.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/bootloader/CMakeFiles 0
.PHONY : CMakeFiles/uf2-app.dir/rule

# Convenience name for target.
uf2-app: CMakeFiles/uf2-app.dir/rule
.PHONY : uf2-app

# clean rule for target.
CMakeFiles/uf2-app.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/uf2-app.dir/build.make CMakeFiles/uf2-app.dir/clean
.PHONY : CMakeFiles/uf2-app.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/merge-bin.dir

# All Build rule for target.
CMakeFiles/merge-bin.dir/all: CMakeFiles/gen_project_binary.dir/all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/merge-bin.dir/build.make CMakeFiles/merge-bin.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/merge-bin.dir/build.make CMakeFiles/merge-bin.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/bootloader/CMakeFiles --progress-num= "Built target merge-bin"
.PHONY : CMakeFiles/merge-bin.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/merge-bin.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/bootloader/CMakeFiles 97
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/merge-bin.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/bootloader/CMakeFiles 0
.PHONY : CMakeFiles/merge-bin.dir/rule

# Convenience name for target.
merge-bin: CMakeFiles/merge-bin.dir/rule
.PHONY : merge-bin

# clean rule for target.
CMakeFiles/merge-bin.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/merge-bin.dir/build.make CMakeFiles/merge-bin.dir/clean
.PHONY : CMakeFiles/merge-bin.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/monitor.dir

# All Build rule for target.
CMakeFiles/monitor.dir/all: CMakeFiles/bootloader.elf.dir/all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/monitor.dir/build.make CMakeFiles/monitor.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/monitor.dir/build.make CMakeFiles/monitor.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/bootloader/CMakeFiles --progress-num= "Built target monitor"
.PHONY : CMakeFiles/monitor.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/monitor.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/bootloader/CMakeFiles 96
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/monitor.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/bootloader/CMakeFiles 0
.PHONY : CMakeFiles/monitor.dir/rule

# Convenience name for target.
monitor: CMakeFiles/monitor.dir/rule
.PHONY : monitor

# clean rule for target.
CMakeFiles/monitor.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/monitor.dir/build.make CMakeFiles/monitor.dir/clean
.PHONY : CMakeFiles/monitor.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/_project_elf_src.dir

# All Build rule for target.
CMakeFiles/_project_elf_src.dir/all:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/_project_elf_src.dir/build.make CMakeFiles/_project_elf_src.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/_project_elf_src.dir/build.make CMakeFiles/_project_elf_src.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/bootloader/CMakeFiles --progress-num= "Built target _project_elf_src"
.PHONY : CMakeFiles/_project_elf_src.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/_project_elf_src.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/bootloader/CMakeFiles 0
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/_project_elf_src.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/bootloader/CMakeFiles 0
.PHONY : CMakeFiles/_project_elf_src.dir/rule

# Convenience name for target.
_project_elf_src: CMakeFiles/_project_elf_src.dir/rule
.PHONY : _project_elf_src

# clean rule for target.
CMakeFiles/_project_elf_src.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/_project_elf_src.dir/build.make CMakeFiles/_project_elf_src.dir/clean
.PHONY : CMakeFiles/_project_elf_src.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/bootloader.elf.dir

# All Build rule for target.
CMakeFiles/bootloader.elf.dir/all: CMakeFiles/_project_elf_src.dir/all
CMakeFiles/bootloader.elf.dir/all: esp-idf/xtensa/CMakeFiles/__idf_xtensa.dir/all
CMakeFiles/bootloader.elf.dir/all: esp-idf/main/CMakeFiles/__idf_main.dir/all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/bootloader.elf.dir/build.make CMakeFiles/bootloader.elf.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/bootloader.elf.dir/build.make CMakeFiles/bootloader.elf.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/bootloader/CMakeFiles --progress-num=94,95,96 "Built target bootloader.elf"
.PHONY : CMakeFiles/bootloader.elf.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/bootloader.elf.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/bootloader/CMakeFiles 96
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/bootloader.elf.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/bootloader/CMakeFiles 0
.PHONY : CMakeFiles/bootloader.elf.dir/rule

# Convenience name for target.
bootloader.elf: CMakeFiles/bootloader.elf.dir/rule
.PHONY : bootloader.elf

# clean rule for target.
CMakeFiles/bootloader.elf.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/bootloader.elf.dir/build.make CMakeFiles/bootloader.elf.dir/clean
.PHONY : CMakeFiles/bootloader.elf.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/size.dir

# All Build rule for target.
CMakeFiles/size.dir/all:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/size.dir/build.make CMakeFiles/size.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/size.dir/build.make CMakeFiles/size.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/bootloader/CMakeFiles --progress-num= "Built target size"
.PHONY : CMakeFiles/size.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/size.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/bootloader/CMakeFiles 0
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/size.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/bootloader/CMakeFiles 0
.PHONY : CMakeFiles/size.dir/rule

# Convenience name for target.
size: CMakeFiles/size.dir/rule
.PHONY : size

# clean rule for target.
CMakeFiles/size.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/size.dir/build.make CMakeFiles/size.dir/clean
.PHONY : CMakeFiles/size.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/size-files.dir

# All Build rule for target.
CMakeFiles/size-files.dir/all:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/size-files.dir/build.make CMakeFiles/size-files.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/size-files.dir/build.make CMakeFiles/size-files.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/bootloader/CMakeFiles --progress-num= "Built target size-files"
.PHONY : CMakeFiles/size-files.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/size-files.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/bootloader/CMakeFiles 0
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/size-files.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/bootloader/CMakeFiles 0
.PHONY : CMakeFiles/size-files.dir/rule

# Convenience name for target.
size-files: CMakeFiles/size-files.dir/rule
.PHONY : size-files

# clean rule for target.
CMakeFiles/size-files.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/size-files.dir/build.make CMakeFiles/size-files.dir/clean
.PHONY : CMakeFiles/size-files.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/size-components.dir

# All Build rule for target.
CMakeFiles/size-components.dir/all:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/size-components.dir/build.make CMakeFiles/size-components.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/size-components.dir/build.make CMakeFiles/size-components.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/bootloader/CMakeFiles --progress-num= "Built target size-components"
.PHONY : CMakeFiles/size-components.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/size-components.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/bootloader/CMakeFiles 0
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/size-components.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/bootloader/CMakeFiles 0
.PHONY : CMakeFiles/size-components.dir/rule

# Convenience name for target.
size-components: CMakeFiles/size-components.dir/rule
.PHONY : size-components

# clean rule for target.
CMakeFiles/size-components.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/size-components.dir/build.make CMakeFiles/size-components.dir/clean
.PHONY : CMakeFiles/size-components.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/dfu.dir

# All Build rule for target.
CMakeFiles/dfu.dir/all: CMakeFiles/gen_project_binary.dir/all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/dfu.dir/build.make CMakeFiles/dfu.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/dfu.dir/build.make CMakeFiles/dfu.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/bootloader/CMakeFiles --progress-num= "Built target dfu"
.PHONY : CMakeFiles/dfu.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/dfu.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/bootloader/CMakeFiles 97
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/dfu.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/bootloader/CMakeFiles 0
.PHONY : CMakeFiles/dfu.dir/rule

# Convenience name for target.
dfu: CMakeFiles/dfu.dir/rule
.PHONY : dfu

# clean rule for target.
CMakeFiles/dfu.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/dfu.dir/build.make CMakeFiles/dfu.dir/clean
.PHONY : CMakeFiles/dfu.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/dfu-list.dir

# All Build rule for target.
CMakeFiles/dfu-list.dir/all:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/dfu-list.dir/build.make CMakeFiles/dfu-list.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/dfu-list.dir/build.make CMakeFiles/dfu-list.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/bootloader/CMakeFiles --progress-num= "Built target dfu-list"
.PHONY : CMakeFiles/dfu-list.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/dfu-list.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/bootloader/CMakeFiles 0
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/dfu-list.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/bootloader/CMakeFiles 0
.PHONY : CMakeFiles/dfu-list.dir/rule

# Convenience name for target.
dfu-list: CMakeFiles/dfu-list.dir/rule
.PHONY : dfu-list

# clean rule for target.
CMakeFiles/dfu-list.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/dfu-list.dir/build.make CMakeFiles/dfu-list.dir/clean
.PHONY : CMakeFiles/dfu-list.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/dfu-flash.dir

# All Build rule for target.
CMakeFiles/dfu-flash.dir/all:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/dfu-flash.dir/build.make CMakeFiles/dfu-flash.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/dfu-flash.dir/build.make CMakeFiles/dfu-flash.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/bootloader/CMakeFiles --progress-num= "Built target dfu-flash"
.PHONY : CMakeFiles/dfu-flash.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/dfu-flash.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/bootloader/CMakeFiles 0
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/dfu-flash.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/bootloader/CMakeFiles 0
.PHONY : CMakeFiles/dfu-flash.dir/rule

# Convenience name for target.
dfu-flash: CMakeFiles/dfu-flash.dir/rule
.PHONY : dfu-flash

# clean rule for target.
CMakeFiles/dfu-flash.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/dfu-flash.dir/build.make CMakeFiles/dfu-flash.dir/clean
.PHONY : CMakeFiles/dfu-flash.dir/clean

#=============================================================================
# Target rules for target esp-idf/xtensa/CMakeFiles/__idf_xtensa.dir

# All Build rule for target.
esp-idf/xtensa/CMakeFiles/__idf_xtensa.dir/all: esp-idf/soc/CMakeFiles/__idf_soc.dir/all
	$(MAKE) $(MAKESILENT) -f esp-idf/xtensa/CMakeFiles/__idf_xtensa.dir/build.make esp-idf/xtensa/CMakeFiles/__idf_xtensa.dir/depend
	$(MAKE) $(MAKESILENT) -f esp-idf/xtensa/CMakeFiles/__idf_xtensa.dir/build.make esp-idf/xtensa/CMakeFiles/__idf_xtensa.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/bootloader/CMakeFiles --progress-num=91,92,93 "Built target __idf_xtensa"
.PHONY : esp-idf/xtensa/CMakeFiles/__idf_xtensa.dir/all

# Build rule for subdir invocation for target.
esp-idf/xtensa/CMakeFiles/__idf_xtensa.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/bootloader/CMakeFiles 92
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 esp-idf/xtensa/CMakeFiles/__idf_xtensa.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/bootloader/CMakeFiles 0
.PHONY : esp-idf/xtensa/CMakeFiles/__idf_xtensa.dir/rule

# Convenience name for target.
__idf_xtensa: esp-idf/xtensa/CMakeFiles/__idf_xtensa.dir/rule
.PHONY : __idf_xtensa

# clean rule for target.
esp-idf/xtensa/CMakeFiles/__idf_xtensa.dir/clean:
	$(MAKE) $(MAKESILENT) -f esp-idf/xtensa/CMakeFiles/__idf_xtensa.dir/build.make esp-idf/xtensa/CMakeFiles/__idf_xtensa.dir/clean
.PHONY : esp-idf/xtensa/CMakeFiles/__idf_xtensa.dir/clean

#=============================================================================
# Target rules for target esp-idf/soc/CMakeFiles/__idf_soc.dir

# All Build rule for target.
esp-idf/soc/CMakeFiles/__idf_soc.dir/all: esp-idf/micro-ecc/CMakeFiles/__idf_micro-ecc.dir/all
	$(MAKE) $(MAKESILENT) -f esp-idf/soc/CMakeFiles/__idf_soc.dir/build.make esp-idf/soc/CMakeFiles/__idf_soc.dir/depend
	$(MAKE) $(MAKESILENT) -f esp-idf/soc/CMakeFiles/__idf_soc.dir/build.make esp-idf/soc/CMakeFiles/__idf_soc.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/bootloader/CMakeFiles --progress-num=66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88 "Built target __idf_soc"
.PHONY : esp-idf/soc/CMakeFiles/__idf_soc.dir/all

# Build rule for subdir invocation for target.
esp-idf/soc/CMakeFiles/__idf_soc.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/bootloader/CMakeFiles 89
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 esp-idf/soc/CMakeFiles/__idf_soc.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/bootloader/CMakeFiles 0
.PHONY : esp-idf/soc/CMakeFiles/__idf_soc.dir/rule

# Convenience name for target.
__idf_soc: esp-idf/soc/CMakeFiles/__idf_soc.dir/rule
.PHONY : __idf_soc

# clean rule for target.
esp-idf/soc/CMakeFiles/__idf_soc.dir/clean:
	$(MAKE) $(MAKESILENT) -f esp-idf/soc/CMakeFiles/__idf_soc.dir/build.make esp-idf/soc/CMakeFiles/__idf_soc.dir/clean
.PHONY : esp-idf/soc/CMakeFiles/__idf_soc.dir/clean

#=============================================================================
# Target rules for target esp-idf/micro-ecc/CMakeFiles/__idf_micro-ecc.dir

# All Build rule for target.
esp-idf/micro-ecc/CMakeFiles/__idf_micro-ecc.dir/all: esp-idf/hal/CMakeFiles/__idf_hal.dir/all
	$(MAKE) $(MAKESILENT) -f esp-idf/micro-ecc/CMakeFiles/__idf_micro-ecc.dir/build.make esp-idf/micro-ecc/CMakeFiles/__idf_micro-ecc.dir/depend
	$(MAKE) $(MAKESILENT) -f esp-idf/micro-ecc/CMakeFiles/__idf_micro-ecc.dir/build.make esp-idf/micro-ecc/CMakeFiles/__idf_micro-ecc.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/bootloader/CMakeFiles --progress-num=64,65 "Built target __idf_micro-ecc"
.PHONY : esp-idf/micro-ecc/CMakeFiles/__idf_micro-ecc.dir/all

# Build rule for subdir invocation for target.
esp-idf/micro-ecc/CMakeFiles/__idf_micro-ecc.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/bootloader/CMakeFiles 66
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 esp-idf/micro-ecc/CMakeFiles/__idf_micro-ecc.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/bootloader/CMakeFiles 0
.PHONY : esp-idf/micro-ecc/CMakeFiles/__idf_micro-ecc.dir/rule

# Convenience name for target.
__idf_micro-ecc: esp-idf/micro-ecc/CMakeFiles/__idf_micro-ecc.dir/rule
.PHONY : __idf_micro-ecc

# clean rule for target.
esp-idf/micro-ecc/CMakeFiles/__idf_micro-ecc.dir/clean:
	$(MAKE) $(MAKESILENT) -f esp-idf/micro-ecc/CMakeFiles/__idf_micro-ecc.dir/build.make esp-idf/micro-ecc/CMakeFiles/__idf_micro-ecc.dir/clean
.PHONY : esp-idf/micro-ecc/CMakeFiles/__idf_micro-ecc.dir/clean

#=============================================================================
# Target rules for target esp-idf/hal/CMakeFiles/__idf_hal.dir

# All Build rule for target.
esp-idf/hal/CMakeFiles/__idf_hal.dir/all: esp-idf/spi_flash/CMakeFiles/__idf_spi_flash.dir/all
	$(MAKE) $(MAKESILENT) -f esp-idf/hal/CMakeFiles/__idf_hal.dir/build.make esp-idf/hal/CMakeFiles/__idf_hal.dir/depend
	$(MAKE) $(MAKESILENT) -f esp-idf/hal/CMakeFiles/__idf_hal.dir/build.make esp-idf/hal/CMakeFiles/__idf_hal.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/bootloader/CMakeFiles --progress-num=53,54,55,56,57,58 "Built target __idf_hal"
.PHONY : esp-idf/hal/CMakeFiles/__idf_hal.dir/all

# Build rule for subdir invocation for target.
esp-idf/hal/CMakeFiles/__idf_hal.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/bootloader/CMakeFiles 64
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 esp-idf/hal/CMakeFiles/__idf_hal.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/bootloader/CMakeFiles 0
.PHONY : esp-idf/hal/CMakeFiles/__idf_hal.dir/rule

# Convenience name for target.
__idf_hal: esp-idf/hal/CMakeFiles/__idf_hal.dir/rule
.PHONY : __idf_hal

# clean rule for target.
esp-idf/hal/CMakeFiles/__idf_hal.dir/clean:
	$(MAKE) $(MAKESILENT) -f esp-idf/hal/CMakeFiles/__idf_hal.dir/build.make esp-idf/hal/CMakeFiles/__idf_hal.dir/clean
.PHONY : esp-idf/hal/CMakeFiles/__idf_hal.dir/clean

#=============================================================================
# Target rules for target esp-idf/spi_flash/CMakeFiles/__idf_spi_flash.dir

# All Build rule for target.
esp-idf/spi_flash/CMakeFiles/__idf_spi_flash.dir/all: esp-idf/esp_bootloader_format/CMakeFiles/__idf_esp_bootloader_format.dir/all
	$(MAKE) $(MAKESILENT) -f esp-idf/spi_flash/CMakeFiles/__idf_spi_flash.dir/build.make esp-idf/spi_flash/CMakeFiles/__idf_spi_flash.dir/depend
	$(MAKE) $(MAKESILENT) -f esp-idf/spi_flash/CMakeFiles/__idf_spi_flash.dir/build.make esp-idf/spi_flash/CMakeFiles/__idf_spi_flash.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/bootloader/CMakeFiles --progress-num=89,90 "Built target __idf_spi_flash"
.PHONY : esp-idf/spi_flash/CMakeFiles/__idf_spi_flash.dir/all

# Build rule for subdir invocation for target.
esp-idf/spi_flash/CMakeFiles/__idf_spi_flash.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/bootloader/CMakeFiles 58
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 esp-idf/spi_flash/CMakeFiles/__idf_spi_flash.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/bootloader/CMakeFiles 0
.PHONY : esp-idf/spi_flash/CMakeFiles/__idf_spi_flash.dir/rule

# Convenience name for target.
__idf_spi_flash: esp-idf/spi_flash/CMakeFiles/__idf_spi_flash.dir/rule
.PHONY : __idf_spi_flash

# clean rule for target.
esp-idf/spi_flash/CMakeFiles/__idf_spi_flash.dir/clean:
	$(MAKE) $(MAKESILENT) -f esp-idf/spi_flash/CMakeFiles/__idf_spi_flash.dir/build.make esp-idf/spi_flash/CMakeFiles/__idf_spi_flash.dir/clean
.PHONY : esp-idf/spi_flash/CMakeFiles/__idf_spi_flash.dir/clean

#=============================================================================
# Target rules for target esp-idf/esp_bootloader_format/CMakeFiles/__idf_esp_bootloader_format.dir

# All Build rule for target.
esp-idf/esp_bootloader_format/CMakeFiles/__idf_esp_bootloader_format.dir/all: esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/all
	$(MAKE) $(MAKESILENT) -f esp-idf/esp_bootloader_format/CMakeFiles/__idf_esp_bootloader_format.dir/build.make esp-idf/esp_bootloader_format/CMakeFiles/__idf_esp_bootloader_format.dir/depend
	$(MAKE) $(MAKESILENT) -f esp-idf/esp_bootloader_format/CMakeFiles/__idf_esp_bootloader_format.dir/build.make esp-idf/esp_bootloader_format/CMakeFiles/__idf_esp_bootloader_format.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/bootloader/CMakeFiles --progress-num=29,30 "Built target __idf_esp_bootloader_format"
.PHONY : esp-idf/esp_bootloader_format/CMakeFiles/__idf_esp_bootloader_format.dir/all

# Build rule for subdir invocation for target.
esp-idf/esp_bootloader_format/CMakeFiles/__idf_esp_bootloader_format.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/bootloader/CMakeFiles 56
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 esp-idf/esp_bootloader_format/CMakeFiles/__idf_esp_bootloader_format.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/bootloader/CMakeFiles 0
.PHONY : esp-idf/esp_bootloader_format/CMakeFiles/__idf_esp_bootloader_format.dir/rule

# Convenience name for target.
__idf_esp_bootloader_format: esp-idf/esp_bootloader_format/CMakeFiles/__idf_esp_bootloader_format.dir/rule
.PHONY : __idf_esp_bootloader_format

# clean rule for target.
esp-idf/esp_bootloader_format/CMakeFiles/__idf_esp_bootloader_format.dir/clean:
	$(MAKE) $(MAKESILENT) -f esp-idf/esp_bootloader_format/CMakeFiles/__idf_esp_bootloader_format.dir/build.make esp-idf/esp_bootloader_format/CMakeFiles/__idf_esp_bootloader_format.dir/clean
.PHONY : esp-idf/esp_bootloader_format/CMakeFiles/__idf_esp_bootloader_format.dir/clean

#=============================================================================
# Target rules for target esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir

# All Build rule for target.
esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/all: esp-idf/efuse/CMakeFiles/__idf_efuse.dir/all
	$(MAKE) $(MAKESILENT) -f esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/build.make esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/depend
	$(MAKE) $(MAKESILENT) -f esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/build.make esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/bootloader/CMakeFiles --progress-num=1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20 "Built target __idf_bootloader_support"
.PHONY : esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/all

# Build rule for subdir invocation for target.
esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/bootloader/CMakeFiles 54
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/bootloader/CMakeFiles 0
.PHONY : esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/rule

# Convenience name for target.
__idf_bootloader_support: esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/rule
.PHONY : __idf_bootloader_support

# clean rule for target.
esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/clean:
	$(MAKE) $(MAKESILENT) -f esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/build.make esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/clean
.PHONY : esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/clean

#=============================================================================
# Target rules for target esp-idf/efuse/CMakeFiles/__idf_efuse.dir

# All Build rule for target.
esp-idf/efuse/CMakeFiles/__idf_efuse.dir/all: esp-idf/esp_system/CMakeFiles/__idf_esp_system.dir/all
	$(MAKE) $(MAKESILENT) -f esp-idf/efuse/CMakeFiles/__idf_efuse.dir/build.make esp-idf/efuse/CMakeFiles/__idf_efuse.dir/depend
	$(MAKE) $(MAKESILENT) -f esp-idf/efuse/CMakeFiles/__idf_efuse.dir/build.make esp-idf/efuse/CMakeFiles/__idf_efuse.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/bootloader/CMakeFiles --progress-num=21,22,23,24,25,26,27,28 "Built target __idf_efuse"
.PHONY : esp-idf/efuse/CMakeFiles/__idf_efuse.dir/all

# Build rule for subdir invocation for target.
esp-idf/efuse/CMakeFiles/__idf_efuse.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/bootloader/CMakeFiles 34
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 esp-idf/efuse/CMakeFiles/__idf_efuse.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/bootloader/CMakeFiles 0
.PHONY : esp-idf/efuse/CMakeFiles/__idf_efuse.dir/rule

# Convenience name for target.
__idf_efuse: esp-idf/efuse/CMakeFiles/__idf_efuse.dir/rule
.PHONY : __idf_efuse

# clean rule for target.
esp-idf/efuse/CMakeFiles/__idf_efuse.dir/clean:
	$(MAKE) $(MAKESILENT) -f esp-idf/efuse/CMakeFiles/__idf_efuse.dir/build.make esp-idf/efuse/CMakeFiles/__idf_efuse.dir/clean
.PHONY : esp-idf/efuse/CMakeFiles/__idf_efuse.dir/clean

#=============================================================================
# Target rules for target esp-idf/efuse/CMakeFiles/efuse-common-table.dir

# All Build rule for target.
esp-idf/efuse/CMakeFiles/efuse-common-table.dir/all:
	$(MAKE) $(MAKESILENT) -f esp-idf/efuse/CMakeFiles/efuse-common-table.dir/build.make esp-idf/efuse/CMakeFiles/efuse-common-table.dir/depend
	$(MAKE) $(MAKESILENT) -f esp-idf/efuse/CMakeFiles/efuse-common-table.dir/build.make esp-idf/efuse/CMakeFiles/efuse-common-table.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/bootloader/CMakeFiles --progress-num= "Built target efuse-common-table"
.PHONY : esp-idf/efuse/CMakeFiles/efuse-common-table.dir/all

# Build rule for subdir invocation for target.
esp-idf/efuse/CMakeFiles/efuse-common-table.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/bootloader/CMakeFiles 0
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 esp-idf/efuse/CMakeFiles/efuse-common-table.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/bootloader/CMakeFiles 0
.PHONY : esp-idf/efuse/CMakeFiles/efuse-common-table.dir/rule

# Convenience name for target.
efuse-common-table: esp-idf/efuse/CMakeFiles/efuse-common-table.dir/rule
.PHONY : efuse-common-table

# clean rule for target.
esp-idf/efuse/CMakeFiles/efuse-common-table.dir/clean:
	$(MAKE) $(MAKESILENT) -f esp-idf/efuse/CMakeFiles/efuse-common-table.dir/build.make esp-idf/efuse/CMakeFiles/efuse-common-table.dir/clean
.PHONY : esp-idf/efuse/CMakeFiles/efuse-common-table.dir/clean

#=============================================================================
# Target rules for target esp-idf/efuse/CMakeFiles/efuse_common_table.dir

# All Build rule for target.
esp-idf/efuse/CMakeFiles/efuse_common_table.dir/all: esp-idf/efuse/CMakeFiles/efuse-common-table.dir/all
	$(MAKE) $(MAKESILENT) -f esp-idf/efuse/CMakeFiles/efuse_common_table.dir/build.make esp-idf/efuse/CMakeFiles/efuse_common_table.dir/depend
	$(MAKE) $(MAKESILENT) -f esp-idf/efuse/CMakeFiles/efuse_common_table.dir/build.make esp-idf/efuse/CMakeFiles/efuse_common_table.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/bootloader/CMakeFiles --progress-num=97 "Built target efuse_common_table"
.PHONY : esp-idf/efuse/CMakeFiles/efuse_common_table.dir/all

# Build rule for subdir invocation for target.
esp-idf/efuse/CMakeFiles/efuse_common_table.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/bootloader/CMakeFiles 1
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 esp-idf/efuse/CMakeFiles/efuse_common_table.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/bootloader/CMakeFiles 0
.PHONY : esp-idf/efuse/CMakeFiles/efuse_common_table.dir/rule

# Convenience name for target.
efuse_common_table: esp-idf/efuse/CMakeFiles/efuse_common_table.dir/rule
.PHONY : efuse_common_table

# clean rule for target.
esp-idf/efuse/CMakeFiles/efuse_common_table.dir/clean:
	$(MAKE) $(MAKESILENT) -f esp-idf/efuse/CMakeFiles/efuse_common_table.dir/build.make esp-idf/efuse/CMakeFiles/efuse_common_table.dir/clean
.PHONY : esp-idf/efuse/CMakeFiles/efuse_common_table.dir/clean

#=============================================================================
# Target rules for target esp-idf/efuse/CMakeFiles/efuse-custom-table.dir

# All Build rule for target.
esp-idf/efuse/CMakeFiles/efuse-custom-table.dir/all:
	$(MAKE) $(MAKESILENT) -f esp-idf/efuse/CMakeFiles/efuse-custom-table.dir/build.make esp-idf/efuse/CMakeFiles/efuse-custom-table.dir/depend
	$(MAKE) $(MAKESILENT) -f esp-idf/efuse/CMakeFiles/efuse-custom-table.dir/build.make esp-idf/efuse/CMakeFiles/efuse-custom-table.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/bootloader/CMakeFiles --progress-num= "Built target efuse-custom-table"
.PHONY : esp-idf/efuse/CMakeFiles/efuse-custom-table.dir/all

# Build rule for subdir invocation for target.
esp-idf/efuse/CMakeFiles/efuse-custom-table.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/bootloader/CMakeFiles 0
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 esp-idf/efuse/CMakeFiles/efuse-custom-table.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/bootloader/CMakeFiles 0
.PHONY : esp-idf/efuse/CMakeFiles/efuse-custom-table.dir/rule

# Convenience name for target.
efuse-custom-table: esp-idf/efuse/CMakeFiles/efuse-custom-table.dir/rule
.PHONY : efuse-custom-table

# clean rule for target.
esp-idf/efuse/CMakeFiles/efuse-custom-table.dir/clean:
	$(MAKE) $(MAKESILENT) -f esp-idf/efuse/CMakeFiles/efuse-custom-table.dir/build.make esp-idf/efuse/CMakeFiles/efuse-custom-table.dir/clean
.PHONY : esp-idf/efuse/CMakeFiles/efuse-custom-table.dir/clean

#=============================================================================
# Target rules for target esp-idf/efuse/CMakeFiles/efuse_custom_table.dir

# All Build rule for target.
esp-idf/efuse/CMakeFiles/efuse_custom_table.dir/all: esp-idf/efuse/CMakeFiles/efuse-custom-table.dir/all
	$(MAKE) $(MAKESILENT) -f esp-idf/efuse/CMakeFiles/efuse_custom_table.dir/build.make esp-idf/efuse/CMakeFiles/efuse_custom_table.dir/depend
	$(MAKE) $(MAKESILENT) -f esp-idf/efuse/CMakeFiles/efuse_custom_table.dir/build.make esp-idf/efuse/CMakeFiles/efuse_custom_table.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/bootloader/CMakeFiles --progress-num=98 "Built target efuse_custom_table"
.PHONY : esp-idf/efuse/CMakeFiles/efuse_custom_table.dir/all

# Build rule for subdir invocation for target.
esp-idf/efuse/CMakeFiles/efuse_custom_table.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/bootloader/CMakeFiles 1
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 esp-idf/efuse/CMakeFiles/efuse_custom_table.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/bootloader/CMakeFiles 0
.PHONY : esp-idf/efuse/CMakeFiles/efuse_custom_table.dir/rule

# Convenience name for target.
efuse_custom_table: esp-idf/efuse/CMakeFiles/efuse_custom_table.dir/rule
.PHONY : efuse_custom_table

# clean rule for target.
esp-idf/efuse/CMakeFiles/efuse_custom_table.dir/clean:
	$(MAKE) $(MAKESILENT) -f esp-idf/efuse/CMakeFiles/efuse_custom_table.dir/build.make esp-idf/efuse/CMakeFiles/efuse_custom_table.dir/clean
.PHONY : esp-idf/efuse/CMakeFiles/efuse_custom_table.dir/clean

#=============================================================================
# Target rules for target esp-idf/efuse/CMakeFiles/show-efuse-table.dir

# All Build rule for target.
esp-idf/efuse/CMakeFiles/show-efuse-table.dir/all:
	$(MAKE) $(MAKESILENT) -f esp-idf/efuse/CMakeFiles/show-efuse-table.dir/build.make esp-idf/efuse/CMakeFiles/show-efuse-table.dir/depend
	$(MAKE) $(MAKESILENT) -f esp-idf/efuse/CMakeFiles/show-efuse-table.dir/build.make esp-idf/efuse/CMakeFiles/show-efuse-table.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/bootloader/CMakeFiles --progress-num= "Built target show-efuse-table"
.PHONY : esp-idf/efuse/CMakeFiles/show-efuse-table.dir/all

# Build rule for subdir invocation for target.
esp-idf/efuse/CMakeFiles/show-efuse-table.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/bootloader/CMakeFiles 0
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 esp-idf/efuse/CMakeFiles/show-efuse-table.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/bootloader/CMakeFiles 0
.PHONY : esp-idf/efuse/CMakeFiles/show-efuse-table.dir/rule

# Convenience name for target.
show-efuse-table: esp-idf/efuse/CMakeFiles/show-efuse-table.dir/rule
.PHONY : show-efuse-table

# clean rule for target.
esp-idf/efuse/CMakeFiles/show-efuse-table.dir/clean:
	$(MAKE) $(MAKESILENT) -f esp-idf/efuse/CMakeFiles/show-efuse-table.dir/build.make esp-idf/efuse/CMakeFiles/show-efuse-table.dir/clean
.PHONY : esp-idf/efuse/CMakeFiles/show-efuse-table.dir/clean

#=============================================================================
# Target rules for target esp-idf/efuse/CMakeFiles/show_efuse_table.dir

# All Build rule for target.
esp-idf/efuse/CMakeFiles/show_efuse_table.dir/all: esp-idf/efuse/CMakeFiles/show-efuse-table.dir/all
	$(MAKE) $(MAKESILENT) -f esp-idf/efuse/CMakeFiles/show_efuse_table.dir/build.make esp-idf/efuse/CMakeFiles/show_efuse_table.dir/depend
	$(MAKE) $(MAKESILENT) -f esp-idf/efuse/CMakeFiles/show_efuse_table.dir/build.make esp-idf/efuse/CMakeFiles/show_efuse_table.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/bootloader/CMakeFiles --progress-num=100 "Built target show_efuse_table"
.PHONY : esp-idf/efuse/CMakeFiles/show_efuse_table.dir/all

# Build rule for subdir invocation for target.
esp-idf/efuse/CMakeFiles/show_efuse_table.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/bootloader/CMakeFiles 1
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 esp-idf/efuse/CMakeFiles/show_efuse_table.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/bootloader/CMakeFiles 0
.PHONY : esp-idf/efuse/CMakeFiles/show_efuse_table.dir/rule

# Convenience name for target.
show_efuse_table: esp-idf/efuse/CMakeFiles/show_efuse_table.dir/rule
.PHONY : show_efuse_table

# clean rule for target.
esp-idf/efuse/CMakeFiles/show_efuse_table.dir/clean:
	$(MAKE) $(MAKESILENT) -f esp-idf/efuse/CMakeFiles/show_efuse_table.dir/build.make esp-idf/efuse/CMakeFiles/show_efuse_table.dir/clean
.PHONY : esp-idf/efuse/CMakeFiles/show_efuse_table.dir/clean

#=============================================================================
# Target rules for target esp-idf/efuse/CMakeFiles/efuse_test_table.dir

# All Build rule for target.
esp-idf/efuse/CMakeFiles/efuse_test_table.dir/all:
	$(MAKE) $(MAKESILENT) -f esp-idf/efuse/CMakeFiles/efuse_test_table.dir/build.make esp-idf/efuse/CMakeFiles/efuse_test_table.dir/depend
	$(MAKE) $(MAKESILENT) -f esp-idf/efuse/CMakeFiles/efuse_test_table.dir/build.make esp-idf/efuse/CMakeFiles/efuse_test_table.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/bootloader/CMakeFiles --progress-num= "Built target efuse_test_table"
.PHONY : esp-idf/efuse/CMakeFiles/efuse_test_table.dir/all

# Build rule for subdir invocation for target.
esp-idf/efuse/CMakeFiles/efuse_test_table.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/bootloader/CMakeFiles 0
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 esp-idf/efuse/CMakeFiles/efuse_test_table.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/bootloader/CMakeFiles 0
.PHONY : esp-idf/efuse/CMakeFiles/efuse_test_table.dir/rule

# Convenience name for target.
efuse_test_table: esp-idf/efuse/CMakeFiles/efuse_test_table.dir/rule
.PHONY : efuse_test_table

# clean rule for target.
esp-idf/efuse/CMakeFiles/efuse_test_table.dir/clean:
	$(MAKE) $(MAKESILENT) -f esp-idf/efuse/CMakeFiles/efuse_test_table.dir/build.make esp-idf/efuse/CMakeFiles/efuse_test_table.dir/clean
.PHONY : esp-idf/efuse/CMakeFiles/efuse_test_table.dir/clean

#=============================================================================
# Target rules for target esp-idf/esp_system/CMakeFiles/__idf_esp_system.dir

# All Build rule for target.
esp-idf/esp_system/CMakeFiles/__idf_esp_system.dir/all: esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/all
	$(MAKE) $(MAKESILENT) -f esp-idf/esp_system/CMakeFiles/__idf_esp_system.dir/build.make esp-idf/esp_system/CMakeFiles/__idf_esp_system.dir/depend
	$(MAKE) $(MAKESILENT) -f esp-idf/esp_system/CMakeFiles/__idf_esp_system.dir/build.make esp-idf/esp_system/CMakeFiles/__idf_esp_system.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/bootloader/CMakeFiles --progress-num=51,52 "Built target __idf_esp_system"
.PHONY : esp-idf/esp_system/CMakeFiles/__idf_esp_system.dir/all

# Build rule for subdir invocation for target.
esp-idf/esp_system/CMakeFiles/__idf_esp_system.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/bootloader/CMakeFiles 26
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 esp-idf/esp_system/CMakeFiles/__idf_esp_system.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/bootloader/CMakeFiles 0
.PHONY : esp-idf/esp_system/CMakeFiles/__idf_esp_system.dir/rule

# Convenience name for target.
__idf_esp_system: esp-idf/esp_system/CMakeFiles/__idf_esp_system.dir/rule
.PHONY : __idf_esp_system

# clean rule for target.
esp-idf/esp_system/CMakeFiles/__idf_esp_system.dir/clean:
	$(MAKE) $(MAKESILENT) -f esp-idf/esp_system/CMakeFiles/__idf_esp_system.dir/build.make esp-idf/esp_system/CMakeFiles/__idf_esp_system.dir/clean
.PHONY : esp-idf/esp_system/CMakeFiles/__idf_esp_system.dir/clean

#=============================================================================
# Target rules for target esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir

# All Build rule for target.
esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/all: esp-idf/esp_common/CMakeFiles/__idf_esp_common.dir/all
	$(MAKE) $(MAKESILENT) -f esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/build.make esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/depend
	$(MAKE) $(MAKESILENT) -f esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/build.make esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/bootloader/CMakeFiles --progress-num=32,33,34,35,36,37,38,39,40,41 "Built target __idf_esp_hw_support"
.PHONY : esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/all

# Build rule for subdir invocation for target.
esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/bootloader/CMakeFiles 24
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/bootloader/CMakeFiles 0
.PHONY : esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/rule

# Convenience name for target.
__idf_esp_hw_support: esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/rule
.PHONY : __idf_esp_hw_support

# clean rule for target.
esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/clean:
	$(MAKE) $(MAKESILENT) -f esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/build.make esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/clean
.PHONY : esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/clean

#=============================================================================
# Target rules for target esp-idf/esp_common/CMakeFiles/__idf_esp_common.dir

# All Build rule for target.
esp-idf/esp_common/CMakeFiles/__idf_esp_common.dir/all: esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/all
	$(MAKE) $(MAKESILENT) -f esp-idf/esp_common/CMakeFiles/__idf_esp_common.dir/build.make esp-idf/esp_common/CMakeFiles/__idf_esp_common.dir/depend
	$(MAKE) $(MAKESILENT) -f esp-idf/esp_common/CMakeFiles/__idf_esp_common.dir/build.make esp-idf/esp_common/CMakeFiles/__idf_esp_common.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/bootloader/CMakeFiles --progress-num=31 "Built target __idf_esp_common"
.PHONY : esp-idf/esp_common/CMakeFiles/__idf_esp_common.dir/all

# Build rule for subdir invocation for target.
esp-idf/esp_common/CMakeFiles/__idf_esp_common.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/bootloader/CMakeFiles 14
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 esp-idf/esp_common/CMakeFiles/__idf_esp_common.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/bootloader/CMakeFiles 0
.PHONY : esp-idf/esp_common/CMakeFiles/__idf_esp_common.dir/rule

# Convenience name for target.
__idf_esp_common: esp-idf/esp_common/CMakeFiles/__idf_esp_common.dir/rule
.PHONY : __idf_esp_common

# clean rule for target.
esp-idf/esp_common/CMakeFiles/__idf_esp_common.dir/clean:
	$(MAKE) $(MAKESILENT) -f esp-idf/esp_common/CMakeFiles/__idf_esp_common.dir/build.make esp-idf/esp_common/CMakeFiles/__idf_esp_common.dir/clean
.PHONY : esp-idf/esp_common/CMakeFiles/__idf_esp_common.dir/clean

#=============================================================================
# Target rules for target esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir

# All Build rule for target.
esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/all: esp-idf/log/CMakeFiles/__idf_log.dir/all
	$(MAKE) $(MAKESILENT) -f esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/build.make esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/depend
	$(MAKE) $(MAKESILENT) -f esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/build.make esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/bootloader/CMakeFiles --progress-num=42,43,44,45,46,47,48,49,50 "Built target __idf_esp_rom"
.PHONY : esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/all

# Build rule for subdir invocation for target.
esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/bootloader/CMakeFiles 13
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/bootloader/CMakeFiles 0
.PHONY : esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/rule

# Convenience name for target.
__idf_esp_rom: esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/rule
.PHONY : __idf_esp_rom

# clean rule for target.
esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/clean:
	$(MAKE) $(MAKESILENT) -f esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/build.make esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/clean
.PHONY : esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/clean

#=============================================================================
# Target rules for target esp-idf/log/CMakeFiles/__idf_log.dir

# All Build rule for target.
esp-idf/log/CMakeFiles/__idf_log.dir/all:
	$(MAKE) $(MAKESILENT) -f esp-idf/log/CMakeFiles/__idf_log.dir/build.make esp-idf/log/CMakeFiles/__idf_log.dir/depend
	$(MAKE) $(MAKESILENT) -f esp-idf/log/CMakeFiles/__idf_log.dir/build.make esp-idf/log/CMakeFiles/__idf_log.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/bootloader/CMakeFiles --progress-num=59,60,61,62 "Built target __idf_log"
.PHONY : esp-idf/log/CMakeFiles/__idf_log.dir/all

# Build rule for subdir invocation for target.
esp-idf/log/CMakeFiles/__idf_log.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/bootloader/CMakeFiles 4
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 esp-idf/log/CMakeFiles/__idf_log.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/bootloader/CMakeFiles 0
.PHONY : esp-idf/log/CMakeFiles/__idf_log.dir/rule

# Convenience name for target.
__idf_log: esp-idf/log/CMakeFiles/__idf_log.dir/rule
.PHONY : __idf_log

# clean rule for target.
esp-idf/log/CMakeFiles/__idf_log.dir/clean:
	$(MAKE) $(MAKESILENT) -f esp-idf/log/CMakeFiles/__idf_log.dir/build.make esp-idf/log/CMakeFiles/__idf_log.dir/clean
.PHONY : esp-idf/log/CMakeFiles/__idf_log.dir/clean

#=============================================================================
# Target rules for target esp-idf/esptool_py/CMakeFiles/bootloader_check_size.dir

# All Build rule for target.
esp-idf/esptool_py/CMakeFiles/bootloader_check_size.dir/all: CMakeFiles/gen_project_binary.dir/all
	$(MAKE) $(MAKESILENT) -f esp-idf/esptool_py/CMakeFiles/bootloader_check_size.dir/build.make esp-idf/esptool_py/CMakeFiles/bootloader_check_size.dir/depend
	$(MAKE) $(MAKESILENT) -f esp-idf/esptool_py/CMakeFiles/bootloader_check_size.dir/build.make esp-idf/esptool_py/CMakeFiles/bootloader_check_size.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/bootloader/CMakeFiles --progress-num= "Built target bootloader_check_size"
.PHONY : esp-idf/esptool_py/CMakeFiles/bootloader_check_size.dir/all

# Build rule for subdir invocation for target.
esp-idf/esptool_py/CMakeFiles/bootloader_check_size.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/bootloader/CMakeFiles 97
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 esp-idf/esptool_py/CMakeFiles/bootloader_check_size.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/bootloader/CMakeFiles 0
.PHONY : esp-idf/esptool_py/CMakeFiles/bootloader_check_size.dir/rule

# Convenience name for target.
bootloader_check_size: esp-idf/esptool_py/CMakeFiles/bootloader_check_size.dir/rule
.PHONY : bootloader_check_size

# clean rule for target.
esp-idf/esptool_py/CMakeFiles/bootloader_check_size.dir/clean:
	$(MAKE) $(MAKESILENT) -f esp-idf/esptool_py/CMakeFiles/bootloader_check_size.dir/build.make esp-idf/esptool_py/CMakeFiles/bootloader_check_size.dir/clean
.PHONY : esp-idf/esptool_py/CMakeFiles/bootloader_check_size.dir/clean

#=============================================================================
# Target rules for target esp-idf/main/CMakeFiles/__idf_main.dir

# All Build rule for target.
esp-idf/main/CMakeFiles/__idf_main.dir/all: esp-idf/xtensa/CMakeFiles/__idf_xtensa.dir/all
	$(MAKE) $(MAKESILENT) -f esp-idf/main/CMakeFiles/__idf_main.dir/build.make esp-idf/main/CMakeFiles/__idf_main.dir/depend
	$(MAKE) $(MAKESILENT) -f esp-idf/main/CMakeFiles/__idf_main.dir/build.make esp-idf/main/CMakeFiles/__idf_main.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/bootloader/CMakeFiles --progress-num=63 "Built target __idf_main"
.PHONY : esp-idf/main/CMakeFiles/__idf_main.dir/all

# Build rule for subdir invocation for target.
esp-idf/main/CMakeFiles/__idf_main.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/bootloader/CMakeFiles 93
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 esp-idf/main/CMakeFiles/__idf_main.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/bootloader/CMakeFiles 0
.PHONY : esp-idf/main/CMakeFiles/__idf_main.dir/rule

# Convenience name for target.
__idf_main: esp-idf/main/CMakeFiles/__idf_main.dir/rule
.PHONY : __idf_main

# clean rule for target.
esp-idf/main/CMakeFiles/__idf_main.dir/clean:
	$(MAKE) $(MAKESILENT) -f esp-idf/main/CMakeFiles/__idf_main.dir/build.make esp-idf/main/CMakeFiles/__idf_main.dir/clean
.PHONY : esp-idf/main/CMakeFiles/__idf_main.dir/clean

#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system

