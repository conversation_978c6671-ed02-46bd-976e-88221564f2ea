#ifndef REGISTER_THINGS_H
#define REGISTER_THINGS_H

#include "iot/thing_manager.h"
#include "iot/things/lamp.h"
#include "iot/things/fan.h"
#include "iot/things/temperature.h"
#include <esp_log.h>

#define TAG_REGISTER "RegisterThings"

namespace iot {

/**
 * 注册所有物联网设备到系统
 * 确保所有设备都能被语音命令系统识别
 */
inline void RegisterAllThings() {
    ESP_LOGI(TAG_REGISTER, "开始注册所有物联网设备...");
    
    // 获取ThingManager单例
    auto& thing_manager = ThingManager::GetInstance();
    
    // 确保灯控制模块已注册
    auto lamp = thing_manager.GetThingByName("Lamp");
    if (lamp) {
        ESP_LOGI(TAG_REGISTER, "灯控制模块已注册");
    } else {
        ESP_LOGW(TAG_REGISTER, "灯控制模块未注册，请检查DECLARE_THING宏是否正确使用");
    }
    
    // 确保风扇控制模块已注册
    auto fan = thing_manager.GetThingByName("Fan");
    if (fan) {
        ESP_LOGI(TAG_REGISTER, "风扇控制模块已注册");
    } else {
        ESP_LOGW(TAG_REGISTER, "风扇控制模块未注册，请检查DECLARE_THING宏是否正确使用");
    }
    
    // 确保温度传感器模块已注册
    auto temperature = thing_manager.GetThingByName("Temperature");
    if (temperature) {
        ESP_LOGI(TAG_REGISTER, "温度传感器模块已注册");
    } else {
        ESP_LOGW(TAG_REGISTER, "温度传感器模块未注册，请检查DECLARE_THING宏是否正确使用");
    }
    
    ESP_LOGI(TAG_REGISTER, "所有物联网设备注册完成");
}

} // namespace iot

#endif // REGISTER_THINGS_H
