# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.22

# Default target executed when no arguments are given to make.
default_target: all
.PHONY : default_target

# Allow only one "make -f Makefile2" at a time, but pass parallelism.
.NOTPARALLEL:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build

#=============================================================================
# Targets provided globally by CMake.

# Special rule for the target edit_cache
edit_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "No interactive CMake dialog available..."
	/usr/bin/cmake -E echo No\ interactive\ CMake\ dialog\ available.
.PHONY : edit_cache

# Special rule for the target edit_cache
edit_cache/fast: edit_cache
.PHONY : edit_cache/fast

# Special rule for the target rebuild_cache
rebuild_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Running CMake to regenerate build system..."
	/usr/bin/cmake --regenerate-during-build -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : rebuild_cache

# Special rule for the target rebuild_cache
rebuild_cache/fast: rebuild_cache
.PHONY : rebuild_cache/fast

# Special rule for the target list_install_components
list_install_components:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Available install components are: \"Unspecified\""
.PHONY : list_install_components

# Special rule for the target list_install_components
list_install_components/fast: list_install_components
.PHONY : list_install_components/fast

# Special rule for the target install
install: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Install the project..."
	/usr/bin/cmake -P cmake_install.cmake
.PHONY : install

# Special rule for the target install
install/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Install the project..."
	/usr/bin/cmake -P cmake_install.cmake
.PHONY : install/fast

# Special rule for the target install/local
install/local: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing only the local directory..."
	/usr/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local

# Special rule for the target install/local
install/local/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing only the local directory..."
	/usr/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local/fast

# Special rule for the target install/strip
install/strip: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing the project stripped..."
	/usr/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip

# Special rule for the target install/strip
install/strip/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing the project stripped..."
	/usr/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip/fast

# The main all target
all: cmake_check_build_system
	cd /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build && $(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/esp-idf/78__esp-ml307//CMakeFiles/progress.marks
	cd /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 esp-idf/78__esp-ml307/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles 0
.PHONY : all

# The main clean target
clean:
	cd /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 esp-idf/78__esp-ml307/clean
.PHONY : clean

# The main clean target
clean/fast: clean
.PHONY : clean/fast

# Prepare targets for installation.
preinstall: all
	cd /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 esp-idf/78__esp-ml307/preinstall
.PHONY : preinstall

# Prepare targets for installation.
preinstall/fast:
	cd /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 esp-idf/78__esp-ml307/preinstall
.PHONY : preinstall/fast

# clear depends
depend:
	cd /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build && $(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 1
.PHONY : depend

# Convenience name for target.
esp-idf/78__esp-ml307/CMakeFiles/__idf_78__esp-ml307.dir/rule:
	cd /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 esp-idf/78__esp-ml307/CMakeFiles/__idf_78__esp-ml307.dir/rule
.PHONY : esp-idf/78__esp-ml307/CMakeFiles/__idf_78__esp-ml307.dir/rule

# Convenience name for target.
__idf_78__esp-ml307: esp-idf/78__esp-ml307/CMakeFiles/__idf_78__esp-ml307.dir/rule
.PHONY : __idf_78__esp-ml307

# fast build rule for target.
__idf_78__esp-ml307/fast:
	cd /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build && $(MAKE) $(MAKESILENT) -f esp-idf/78__esp-ml307/CMakeFiles/__idf_78__esp-ml307.dir/build.make esp-idf/78__esp-ml307/CMakeFiles/__idf_78__esp-ml307.dir/build
.PHONY : __idf_78__esp-ml307/fast

esp_http.obj: esp_http.cc.obj
.PHONY : esp_http.obj

# target to build an object file
esp_http.cc.obj:
	cd /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build && $(MAKE) $(MAKESILENT) -f esp-idf/78__esp-ml307/CMakeFiles/__idf_78__esp-ml307.dir/build.make esp-idf/78__esp-ml307/CMakeFiles/__idf_78__esp-ml307.dir/esp_http.cc.obj
.PHONY : esp_http.cc.obj

esp_http.i: esp_http.cc.i
.PHONY : esp_http.i

# target to preprocess a source file
esp_http.cc.i:
	cd /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build && $(MAKE) $(MAKESILENT) -f esp-idf/78__esp-ml307/CMakeFiles/__idf_78__esp-ml307.dir/build.make esp-idf/78__esp-ml307/CMakeFiles/__idf_78__esp-ml307.dir/esp_http.cc.i
.PHONY : esp_http.cc.i

esp_http.s: esp_http.cc.s
.PHONY : esp_http.s

# target to generate assembly for a file
esp_http.cc.s:
	cd /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build && $(MAKE) $(MAKESILENT) -f esp-idf/78__esp-ml307/CMakeFiles/__idf_78__esp-ml307.dir/build.make esp-idf/78__esp-ml307/CMakeFiles/__idf_78__esp-ml307.dir/esp_http.cc.s
.PHONY : esp_http.cc.s

esp_mqtt.obj: esp_mqtt.cc.obj
.PHONY : esp_mqtt.obj

# target to build an object file
esp_mqtt.cc.obj:
	cd /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build && $(MAKE) $(MAKESILENT) -f esp-idf/78__esp-ml307/CMakeFiles/__idf_78__esp-ml307.dir/build.make esp-idf/78__esp-ml307/CMakeFiles/__idf_78__esp-ml307.dir/esp_mqtt.cc.obj
.PHONY : esp_mqtt.cc.obj

esp_mqtt.i: esp_mqtt.cc.i
.PHONY : esp_mqtt.i

# target to preprocess a source file
esp_mqtt.cc.i:
	cd /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build && $(MAKE) $(MAKESILENT) -f esp-idf/78__esp-ml307/CMakeFiles/__idf_78__esp-ml307.dir/build.make esp-idf/78__esp-ml307/CMakeFiles/__idf_78__esp-ml307.dir/esp_mqtt.cc.i
.PHONY : esp_mqtt.cc.i

esp_mqtt.s: esp_mqtt.cc.s
.PHONY : esp_mqtt.s

# target to generate assembly for a file
esp_mqtt.cc.s:
	cd /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build && $(MAKE) $(MAKESILENT) -f esp-idf/78__esp-ml307/CMakeFiles/__idf_78__esp-ml307.dir/build.make esp-idf/78__esp-ml307/CMakeFiles/__idf_78__esp-ml307.dir/esp_mqtt.cc.s
.PHONY : esp_mqtt.cc.s

esp_udp.obj: esp_udp.cc.obj
.PHONY : esp_udp.obj

# target to build an object file
esp_udp.cc.obj:
	cd /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build && $(MAKE) $(MAKESILENT) -f esp-idf/78__esp-ml307/CMakeFiles/__idf_78__esp-ml307.dir/build.make esp-idf/78__esp-ml307/CMakeFiles/__idf_78__esp-ml307.dir/esp_udp.cc.obj
.PHONY : esp_udp.cc.obj

esp_udp.i: esp_udp.cc.i
.PHONY : esp_udp.i

# target to preprocess a source file
esp_udp.cc.i:
	cd /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build && $(MAKE) $(MAKESILENT) -f esp-idf/78__esp-ml307/CMakeFiles/__idf_78__esp-ml307.dir/build.make esp-idf/78__esp-ml307/CMakeFiles/__idf_78__esp-ml307.dir/esp_udp.cc.i
.PHONY : esp_udp.cc.i

esp_udp.s: esp_udp.cc.s
.PHONY : esp_udp.s

# target to generate assembly for a file
esp_udp.cc.s:
	cd /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build && $(MAKE) $(MAKESILENT) -f esp-idf/78__esp-ml307/CMakeFiles/__idf_78__esp-ml307.dir/build.make esp-idf/78__esp-ml307/CMakeFiles/__idf_78__esp-ml307.dir/esp_udp.cc.s
.PHONY : esp_udp.cc.s

ml307_at_modem.obj: ml307_at_modem.cc.obj
.PHONY : ml307_at_modem.obj

# target to build an object file
ml307_at_modem.cc.obj:
	cd /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build && $(MAKE) $(MAKESILENT) -f esp-idf/78__esp-ml307/CMakeFiles/__idf_78__esp-ml307.dir/build.make esp-idf/78__esp-ml307/CMakeFiles/__idf_78__esp-ml307.dir/ml307_at_modem.cc.obj
.PHONY : ml307_at_modem.cc.obj

ml307_at_modem.i: ml307_at_modem.cc.i
.PHONY : ml307_at_modem.i

# target to preprocess a source file
ml307_at_modem.cc.i:
	cd /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build && $(MAKE) $(MAKESILENT) -f esp-idf/78__esp-ml307/CMakeFiles/__idf_78__esp-ml307.dir/build.make esp-idf/78__esp-ml307/CMakeFiles/__idf_78__esp-ml307.dir/ml307_at_modem.cc.i
.PHONY : ml307_at_modem.cc.i

ml307_at_modem.s: ml307_at_modem.cc.s
.PHONY : ml307_at_modem.s

# target to generate assembly for a file
ml307_at_modem.cc.s:
	cd /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build && $(MAKE) $(MAKESILENT) -f esp-idf/78__esp-ml307/CMakeFiles/__idf_78__esp-ml307.dir/build.make esp-idf/78__esp-ml307/CMakeFiles/__idf_78__esp-ml307.dir/ml307_at_modem.cc.s
.PHONY : ml307_at_modem.cc.s

ml307_http.obj: ml307_http.cc.obj
.PHONY : ml307_http.obj

# target to build an object file
ml307_http.cc.obj:
	cd /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build && $(MAKE) $(MAKESILENT) -f esp-idf/78__esp-ml307/CMakeFiles/__idf_78__esp-ml307.dir/build.make esp-idf/78__esp-ml307/CMakeFiles/__idf_78__esp-ml307.dir/ml307_http.cc.obj
.PHONY : ml307_http.cc.obj

ml307_http.i: ml307_http.cc.i
.PHONY : ml307_http.i

# target to preprocess a source file
ml307_http.cc.i:
	cd /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build && $(MAKE) $(MAKESILENT) -f esp-idf/78__esp-ml307/CMakeFiles/__idf_78__esp-ml307.dir/build.make esp-idf/78__esp-ml307/CMakeFiles/__idf_78__esp-ml307.dir/ml307_http.cc.i
.PHONY : ml307_http.cc.i

ml307_http.s: ml307_http.cc.s
.PHONY : ml307_http.s

# target to generate assembly for a file
ml307_http.cc.s:
	cd /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build && $(MAKE) $(MAKESILENT) -f esp-idf/78__esp-ml307/CMakeFiles/__idf_78__esp-ml307.dir/build.make esp-idf/78__esp-ml307/CMakeFiles/__idf_78__esp-ml307.dir/ml307_http.cc.s
.PHONY : ml307_http.cc.s

ml307_mqtt.obj: ml307_mqtt.cc.obj
.PHONY : ml307_mqtt.obj

# target to build an object file
ml307_mqtt.cc.obj:
	cd /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build && $(MAKE) $(MAKESILENT) -f esp-idf/78__esp-ml307/CMakeFiles/__idf_78__esp-ml307.dir/build.make esp-idf/78__esp-ml307/CMakeFiles/__idf_78__esp-ml307.dir/ml307_mqtt.cc.obj
.PHONY : ml307_mqtt.cc.obj

ml307_mqtt.i: ml307_mqtt.cc.i
.PHONY : ml307_mqtt.i

# target to preprocess a source file
ml307_mqtt.cc.i:
	cd /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build && $(MAKE) $(MAKESILENT) -f esp-idf/78__esp-ml307/CMakeFiles/__idf_78__esp-ml307.dir/build.make esp-idf/78__esp-ml307/CMakeFiles/__idf_78__esp-ml307.dir/ml307_mqtt.cc.i
.PHONY : ml307_mqtt.cc.i

ml307_mqtt.s: ml307_mqtt.cc.s
.PHONY : ml307_mqtt.s

# target to generate assembly for a file
ml307_mqtt.cc.s:
	cd /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build && $(MAKE) $(MAKESILENT) -f esp-idf/78__esp-ml307/CMakeFiles/__idf_78__esp-ml307.dir/build.make esp-idf/78__esp-ml307/CMakeFiles/__idf_78__esp-ml307.dir/ml307_mqtt.cc.s
.PHONY : ml307_mqtt.cc.s

ml307_ssl_transport.obj: ml307_ssl_transport.cc.obj
.PHONY : ml307_ssl_transport.obj

# target to build an object file
ml307_ssl_transport.cc.obj:
	cd /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build && $(MAKE) $(MAKESILENT) -f esp-idf/78__esp-ml307/CMakeFiles/__idf_78__esp-ml307.dir/build.make esp-idf/78__esp-ml307/CMakeFiles/__idf_78__esp-ml307.dir/ml307_ssl_transport.cc.obj
.PHONY : ml307_ssl_transport.cc.obj

ml307_ssl_transport.i: ml307_ssl_transport.cc.i
.PHONY : ml307_ssl_transport.i

# target to preprocess a source file
ml307_ssl_transport.cc.i:
	cd /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build && $(MAKE) $(MAKESILENT) -f esp-idf/78__esp-ml307/CMakeFiles/__idf_78__esp-ml307.dir/build.make esp-idf/78__esp-ml307/CMakeFiles/__idf_78__esp-ml307.dir/ml307_ssl_transport.cc.i
.PHONY : ml307_ssl_transport.cc.i

ml307_ssl_transport.s: ml307_ssl_transport.cc.s
.PHONY : ml307_ssl_transport.s

# target to generate assembly for a file
ml307_ssl_transport.cc.s:
	cd /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build && $(MAKE) $(MAKESILENT) -f esp-idf/78__esp-ml307/CMakeFiles/__idf_78__esp-ml307.dir/build.make esp-idf/78__esp-ml307/CMakeFiles/__idf_78__esp-ml307.dir/ml307_ssl_transport.cc.s
.PHONY : ml307_ssl_transport.cc.s

ml307_udp.obj: ml307_udp.cc.obj
.PHONY : ml307_udp.obj

# target to build an object file
ml307_udp.cc.obj:
	cd /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build && $(MAKE) $(MAKESILENT) -f esp-idf/78__esp-ml307/CMakeFiles/__idf_78__esp-ml307.dir/build.make esp-idf/78__esp-ml307/CMakeFiles/__idf_78__esp-ml307.dir/ml307_udp.cc.obj
.PHONY : ml307_udp.cc.obj

ml307_udp.i: ml307_udp.cc.i
.PHONY : ml307_udp.i

# target to preprocess a source file
ml307_udp.cc.i:
	cd /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build && $(MAKE) $(MAKESILENT) -f esp-idf/78__esp-ml307/CMakeFiles/__idf_78__esp-ml307.dir/build.make esp-idf/78__esp-ml307/CMakeFiles/__idf_78__esp-ml307.dir/ml307_udp.cc.i
.PHONY : ml307_udp.cc.i

ml307_udp.s: ml307_udp.cc.s
.PHONY : ml307_udp.s

# target to generate assembly for a file
ml307_udp.cc.s:
	cd /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build && $(MAKE) $(MAKESILENT) -f esp-idf/78__esp-ml307/CMakeFiles/__idf_78__esp-ml307.dir/build.make esp-idf/78__esp-ml307/CMakeFiles/__idf_78__esp-ml307.dir/ml307_udp.cc.s
.PHONY : ml307_udp.cc.s

tcp_transport.obj: tcp_transport.cc.obj
.PHONY : tcp_transport.obj

# target to build an object file
tcp_transport.cc.obj:
	cd /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build && $(MAKE) $(MAKESILENT) -f esp-idf/78__esp-ml307/CMakeFiles/__idf_78__esp-ml307.dir/build.make esp-idf/78__esp-ml307/CMakeFiles/__idf_78__esp-ml307.dir/tcp_transport.cc.obj
.PHONY : tcp_transport.cc.obj

tcp_transport.i: tcp_transport.cc.i
.PHONY : tcp_transport.i

# target to preprocess a source file
tcp_transport.cc.i:
	cd /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build && $(MAKE) $(MAKESILENT) -f esp-idf/78__esp-ml307/CMakeFiles/__idf_78__esp-ml307.dir/build.make esp-idf/78__esp-ml307/CMakeFiles/__idf_78__esp-ml307.dir/tcp_transport.cc.i
.PHONY : tcp_transport.cc.i

tcp_transport.s: tcp_transport.cc.s
.PHONY : tcp_transport.s

# target to generate assembly for a file
tcp_transport.cc.s:
	cd /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build && $(MAKE) $(MAKESILENT) -f esp-idf/78__esp-ml307/CMakeFiles/__idf_78__esp-ml307.dir/build.make esp-idf/78__esp-ml307/CMakeFiles/__idf_78__esp-ml307.dir/tcp_transport.cc.s
.PHONY : tcp_transport.cc.s

tls_transport.obj: tls_transport.cc.obj
.PHONY : tls_transport.obj

# target to build an object file
tls_transport.cc.obj:
	cd /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build && $(MAKE) $(MAKESILENT) -f esp-idf/78__esp-ml307/CMakeFiles/__idf_78__esp-ml307.dir/build.make esp-idf/78__esp-ml307/CMakeFiles/__idf_78__esp-ml307.dir/tls_transport.cc.obj
.PHONY : tls_transport.cc.obj

tls_transport.i: tls_transport.cc.i
.PHONY : tls_transport.i

# target to preprocess a source file
tls_transport.cc.i:
	cd /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build && $(MAKE) $(MAKESILENT) -f esp-idf/78__esp-ml307/CMakeFiles/__idf_78__esp-ml307.dir/build.make esp-idf/78__esp-ml307/CMakeFiles/__idf_78__esp-ml307.dir/tls_transport.cc.i
.PHONY : tls_transport.cc.i

tls_transport.s: tls_transport.cc.s
.PHONY : tls_transport.s

# target to generate assembly for a file
tls_transport.cc.s:
	cd /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build && $(MAKE) $(MAKESILENT) -f esp-idf/78__esp-ml307/CMakeFiles/__idf_78__esp-ml307.dir/build.make esp-idf/78__esp-ml307/CMakeFiles/__idf_78__esp-ml307.dir/tls_transport.cc.s
.PHONY : tls_transport.cc.s

web_socket.obj: web_socket.cc.obj
.PHONY : web_socket.obj

# target to build an object file
web_socket.cc.obj:
	cd /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build && $(MAKE) $(MAKESILENT) -f esp-idf/78__esp-ml307/CMakeFiles/__idf_78__esp-ml307.dir/build.make esp-idf/78__esp-ml307/CMakeFiles/__idf_78__esp-ml307.dir/web_socket.cc.obj
.PHONY : web_socket.cc.obj

web_socket.i: web_socket.cc.i
.PHONY : web_socket.i

# target to preprocess a source file
web_socket.cc.i:
	cd /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build && $(MAKE) $(MAKESILENT) -f esp-idf/78__esp-ml307/CMakeFiles/__idf_78__esp-ml307.dir/build.make esp-idf/78__esp-ml307/CMakeFiles/__idf_78__esp-ml307.dir/web_socket.cc.i
.PHONY : web_socket.cc.i

web_socket.s: web_socket.cc.s
.PHONY : web_socket.s

# target to generate assembly for a file
web_socket.cc.s:
	cd /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build && $(MAKE) $(MAKESILENT) -f esp-idf/78__esp-ml307/CMakeFiles/__idf_78__esp-ml307.dir/build.make esp-idf/78__esp-ml307/CMakeFiles/__idf_78__esp-ml307.dir/web_socket.cc.s
.PHONY : web_socket.cc.s

# Help Target
help:
	@echo "The following are some of the valid targets for this Makefile:"
	@echo "... all (the default if no target is provided)"
	@echo "... clean"
	@echo "... depend"
	@echo "... edit_cache"
	@echo "... install"
	@echo "... install/local"
	@echo "... install/strip"
	@echo "... list_install_components"
	@echo "... rebuild_cache"
	@echo "... __idf_78__esp-ml307"
	@echo "... esp_http.obj"
	@echo "... esp_http.i"
	@echo "... esp_http.s"
	@echo "... esp_mqtt.obj"
	@echo "... esp_mqtt.i"
	@echo "... esp_mqtt.s"
	@echo "... esp_udp.obj"
	@echo "... esp_udp.i"
	@echo "... esp_udp.s"
	@echo "... ml307_at_modem.obj"
	@echo "... ml307_at_modem.i"
	@echo "... ml307_at_modem.s"
	@echo "... ml307_http.obj"
	@echo "... ml307_http.i"
	@echo "... ml307_http.s"
	@echo "... ml307_mqtt.obj"
	@echo "... ml307_mqtt.i"
	@echo "... ml307_mqtt.s"
	@echo "... ml307_ssl_transport.obj"
	@echo "... ml307_ssl_transport.i"
	@echo "... ml307_ssl_transport.s"
	@echo "... ml307_udp.obj"
	@echo "... ml307_udp.i"
	@echo "... ml307_udp.s"
	@echo "... tcp_transport.obj"
	@echo "... tcp_transport.i"
	@echo "... tcp_transport.s"
	@echo "... tls_transport.obj"
	@echo "... tls_transport.i"
	@echo "... tls_transport.s"
	@echo "... web_socket.obj"
	@echo "... web_socket.i"
	@echo "... web_socket.s"
.PHONY : help



#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	cd /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build && $(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system

