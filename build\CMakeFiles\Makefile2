# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.22

# Default target executed when no arguments are given to make.
default_target: all
.PHONY : default_target

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build

#=============================================================================
# Directory level rules for the build root directory

# The main recursive "all" target.
all: CMakeFiles/bootloader.dir/all
all: CMakeFiles/app.dir/all
all: CMakeFiles/xiaozhi.elf.dir/all
all: esp-idf/all
.PHONY : all

# The main recursive "preinstall" target.
preinstall: esp-idf/preinstall
.PHONY : preinstall

# The main recursive "clean" target.
clean: CMakeFiles/menuconfig.dir/clean
clean: CMakeFiles/confserver.dir/clean
clean: CMakeFiles/save-defconfig.dir/clean
clean: CMakeFiles/bootloader.dir/clean
clean: CMakeFiles/gen_project_binary.dir/clean
clean: CMakeFiles/app.dir/clean
clean: CMakeFiles/erase_flash.dir/clean
clean: CMakeFiles/uf2.dir/clean
clean: CMakeFiles/uf2-app.dir/clean
clean: CMakeFiles/merge-bin.dir/clean
clean: CMakeFiles/monitor.dir/clean
clean: CMakeFiles/flash.dir/clean
clean: CMakeFiles/encrypted-flash.dir/clean
clean: CMakeFiles/_project_elf_src.dir/clean
clean: CMakeFiles/xiaozhi.elf.dir/clean
clean: CMakeFiles/size.dir/clean
clean: CMakeFiles/size-files.dir/clean
clean: CMakeFiles/size-components.dir/clean
clean: CMakeFiles/dfu.dir/clean
clean: CMakeFiles/dfu-list.dir/clean
clean: CMakeFiles/dfu-flash.dir/clean
clean: esp-idf/clean
	$(CMAKE_COMMAND) -P CMakeFiles/cmake_directory_clean.cmake
.PHONY : clean

#=============================================================================
# Directory level rules for directory esp-idf

# Recursive "all" directory target.
esp-idf/all: esp-idf/xtensa/all
esp-idf/all: esp-idf/esp_driver_gpio/all
esp-idf/all: esp-idf/esp_pm/all
esp-idf/all: esp-idf/mbedtls/all
esp-idf/all: esp-idf/bootloader/all
esp-idf/all: esp-idf/esptool_py/all
esp-idf/all: esp-idf/partition_table/all
esp-idf/all: esp-idf/esp_app_format/all
esp-idf/all: esp-idf/esp_bootloader_format/all
esp-idf/all: esp-idf/app_update/all
esp-idf/all: esp-idf/esp_partition/all
esp-idf/all: esp-idf/efuse/all
esp-idf/all: esp-idf/bootloader_support/all
esp-idf/all: esp-idf/esp_mm/all
esp-idf/all: esp-idf/spi_flash/all
esp-idf/all: esp-idf/esp_system/all
esp-idf/all: esp-idf/esp_common/all
esp-idf/all: esp-idf/esp_rom/all
esp-idf/all: esp-idf/hal/all
esp-idf/all: esp-idf/log/all
esp-idf/all: esp-idf/heap/all
esp-idf/all: esp-idf/soc/all
esp-idf/all: esp-idf/esp_hw_support/all
esp-idf/all: esp-idf/freertos/all
esp-idf/all: esp-idf/newlib/all
esp-idf/all: esp-idf/pthread/all
esp-idf/all: esp-idf/cxx/all
esp-idf/all: esp-idf/esp_timer/all
esp-idf/all: esp-idf/esp_driver_gptimer/all
esp-idf/all: esp-idf/esp_ringbuf/all
esp-idf/all: esp-idf/esp_driver_uart/all
esp-idf/all: esp-idf/app_trace/all
esp-idf/all: esp-idf/esp_event/all
esp-idf/all: esp-idf/nvs_flash/all
esp-idf/all: esp-idf/esp_driver_pcnt/all
esp-idf/all: esp-idf/esp_driver_spi/all
esp-idf/all: esp-idf/esp_driver_mcpwm/all
esp-idf/all: esp-idf/esp_driver_ana_cmpr/all
esp-idf/all: esp-idf/esp_driver_i2s/all
esp-idf/all: esp-idf/sdmmc/all
esp-idf/all: esp-idf/esp_driver_sdmmc/all
esp-idf/all: esp-idf/esp_driver_sdspi/all
esp-idf/all: esp-idf/esp_driver_sdio/all
esp-idf/all: esp-idf/esp_driver_dac/all
esp-idf/all: esp-idf/esp_driver_rmt/all
esp-idf/all: esp-idf/esp_driver_tsens/all
esp-idf/all: esp-idf/esp_driver_sdm/all
esp-idf/all: esp-idf/esp_driver_i2c/all
esp-idf/all: esp-idf/esp_driver_ledc/all
esp-idf/all: esp-idf/esp_driver_parlio/all
esp-idf/all: esp-idf/esp_driver_usb_serial_jtag/all
esp-idf/all: esp-idf/driver/all
esp-idf/all: esp-idf/esp_phy/all
esp-idf/all: esp-idf/esp_vfs_console/all
esp-idf/all: esp-idf/vfs/all
esp-idf/all: esp-idf/lwip/all
esp-idf/all: esp-idf/esp_netif_stack/all
esp-idf/all: esp-idf/esp_netif/all
esp-idf/all: esp-idf/wpa_supplicant/all
esp-idf/all: esp-idf/esp_coex/all
esp-idf/all: esp-idf/esp_wifi/all
esp-idf/all: esp-idf/bt/all
esp-idf/all: esp-idf/unity/all
esp-idf/all: esp-idf/cmock/all
esp-idf/all: esp-idf/console/all
esp-idf/all: esp-idf/http_parser/all
esp-idf/all: esp-idf/esp-tls/all
esp-idf/all: esp-idf/esp_adc/all
esp-idf/all: esp-idf/esp_driver_isp/all
esp-idf/all: esp-idf/esp_driver_cam/all
esp-idf/all: esp-idf/esp_driver_jpeg/all
esp-idf/all: esp-idf/esp_driver_ppa/all
esp-idf/all: esp-idf/esp_eth/all
esp-idf/all: esp-idf/esp_gdbstub/all
esp-idf/all: esp-idf/esp_hid/all
esp-idf/all: esp-idf/tcp_transport/all
esp-idf/all: esp-idf/esp_http_client/all
esp-idf/all: esp-idf/esp_http_server/all
esp-idf/all: esp-idf/esp_https_ota/all
esp-idf/all: esp-idf/esp_https_server/all
esp-idf/all: esp-idf/esp_psram/all
esp-idf/all: esp-idf/esp_lcd/all
esp-idf/all: esp-idf/protobuf-c/all
esp-idf/all: esp-idf/protocomm/all
esp-idf/all: esp-idf/esp_local_ctrl/all
esp-idf/all: esp-idf/espcoredump/all
esp-idf/all: esp-idf/wear_levelling/all
esp-idf/all: esp-idf/fatfs/all
esp-idf/all: esp-idf/idf_test/all
esp-idf/all: esp-idf/ieee802154/all
esp-idf/all: esp-idf/json/all
esp-idf/all: esp-idf/mqtt/all
esp-idf/all: esp-idf/nvs_sec_provider/all
esp-idf/all: esp-idf/openthread/all
esp-idf/all: esp-idf/perfmon/all
esp-idf/all: esp-idf/spiffs/all
esp-idf/all: esp-idf/touch_element/all
esp-idf/all: esp-idf/ulp/all
esp-idf/all: esp-idf/usb/all
esp-idf/all: esp-idf/wifi_provisioning/all
esp-idf/all: esp-idf/78__esp-ml307/all
esp-idf/all: esp-idf/78__esp-opus/all
esp-idf/all: esp-idf/78__esp-opus-encoder/all
esp-idf/all: esp-idf/78__esp-wifi-connect/all
esp-idf/all: esp-idf/espressif__cmake_utilities/all
esp-idf/all: esp-idf/espressif__button/all
esp-idf/all: esp-idf/espressif__esp-dsp/all
esp-idf/all: esp-idf/espressif__esp-sr/all
esp-idf/all: esp-idf/espressif__esp_codec_dev/all
esp-idf/all: esp-idf/lvgl__lvgl/all
esp-idf/all: esp-idf/espressif__esp_lvgl_port/all
esp-idf/all: esp-idf/espressif__led_strip/all
esp-idf/all: esp-idf/main/all
.PHONY : esp-idf/all

# Recursive "preinstall" directory target.
esp-idf/preinstall: esp-idf/xtensa/preinstall
esp-idf/preinstall: esp-idf/esp_driver_gpio/preinstall
esp-idf/preinstall: esp-idf/esp_pm/preinstall
esp-idf/preinstall: esp-idf/mbedtls/preinstall
esp-idf/preinstall: esp-idf/bootloader/preinstall
esp-idf/preinstall: esp-idf/esptool_py/preinstall
esp-idf/preinstall: esp-idf/partition_table/preinstall
esp-idf/preinstall: esp-idf/esp_app_format/preinstall
esp-idf/preinstall: esp-idf/esp_bootloader_format/preinstall
esp-idf/preinstall: esp-idf/app_update/preinstall
esp-idf/preinstall: esp-idf/esp_partition/preinstall
esp-idf/preinstall: esp-idf/efuse/preinstall
esp-idf/preinstall: esp-idf/bootloader_support/preinstall
esp-idf/preinstall: esp-idf/esp_mm/preinstall
esp-idf/preinstall: esp-idf/spi_flash/preinstall
esp-idf/preinstall: esp-idf/esp_system/preinstall
esp-idf/preinstall: esp-idf/esp_common/preinstall
esp-idf/preinstall: esp-idf/esp_rom/preinstall
esp-idf/preinstall: esp-idf/hal/preinstall
esp-idf/preinstall: esp-idf/log/preinstall
esp-idf/preinstall: esp-idf/heap/preinstall
esp-idf/preinstall: esp-idf/soc/preinstall
esp-idf/preinstall: esp-idf/esp_hw_support/preinstall
esp-idf/preinstall: esp-idf/freertos/preinstall
esp-idf/preinstall: esp-idf/newlib/preinstall
esp-idf/preinstall: esp-idf/pthread/preinstall
esp-idf/preinstall: esp-idf/cxx/preinstall
esp-idf/preinstall: esp-idf/esp_timer/preinstall
esp-idf/preinstall: esp-idf/esp_driver_gptimer/preinstall
esp-idf/preinstall: esp-idf/esp_ringbuf/preinstall
esp-idf/preinstall: esp-idf/esp_driver_uart/preinstall
esp-idf/preinstall: esp-idf/app_trace/preinstall
esp-idf/preinstall: esp-idf/esp_event/preinstall
esp-idf/preinstall: esp-idf/nvs_flash/preinstall
esp-idf/preinstall: esp-idf/esp_driver_pcnt/preinstall
esp-idf/preinstall: esp-idf/esp_driver_spi/preinstall
esp-idf/preinstall: esp-idf/esp_driver_mcpwm/preinstall
esp-idf/preinstall: esp-idf/esp_driver_ana_cmpr/preinstall
esp-idf/preinstall: esp-idf/esp_driver_i2s/preinstall
esp-idf/preinstall: esp-idf/sdmmc/preinstall
esp-idf/preinstall: esp-idf/esp_driver_sdmmc/preinstall
esp-idf/preinstall: esp-idf/esp_driver_sdspi/preinstall
esp-idf/preinstall: esp-idf/esp_driver_sdio/preinstall
esp-idf/preinstall: esp-idf/esp_driver_dac/preinstall
esp-idf/preinstall: esp-idf/esp_driver_rmt/preinstall
esp-idf/preinstall: esp-idf/esp_driver_tsens/preinstall
esp-idf/preinstall: esp-idf/esp_driver_sdm/preinstall
esp-idf/preinstall: esp-idf/esp_driver_i2c/preinstall
esp-idf/preinstall: esp-idf/esp_driver_ledc/preinstall
esp-idf/preinstall: esp-idf/esp_driver_parlio/preinstall
esp-idf/preinstall: esp-idf/esp_driver_usb_serial_jtag/preinstall
esp-idf/preinstall: esp-idf/driver/preinstall
esp-idf/preinstall: esp-idf/esp_phy/preinstall
esp-idf/preinstall: esp-idf/esp_vfs_console/preinstall
esp-idf/preinstall: esp-idf/vfs/preinstall
esp-idf/preinstall: esp-idf/lwip/preinstall
esp-idf/preinstall: esp-idf/esp_netif_stack/preinstall
esp-idf/preinstall: esp-idf/esp_netif/preinstall
esp-idf/preinstall: esp-idf/wpa_supplicant/preinstall
esp-idf/preinstall: esp-idf/esp_coex/preinstall
esp-idf/preinstall: esp-idf/esp_wifi/preinstall
esp-idf/preinstall: esp-idf/bt/preinstall
esp-idf/preinstall: esp-idf/unity/preinstall
esp-idf/preinstall: esp-idf/cmock/preinstall
esp-idf/preinstall: esp-idf/console/preinstall
esp-idf/preinstall: esp-idf/http_parser/preinstall
esp-idf/preinstall: esp-idf/esp-tls/preinstall
esp-idf/preinstall: esp-idf/esp_adc/preinstall
esp-idf/preinstall: esp-idf/esp_driver_isp/preinstall
esp-idf/preinstall: esp-idf/esp_driver_cam/preinstall
esp-idf/preinstall: esp-idf/esp_driver_jpeg/preinstall
esp-idf/preinstall: esp-idf/esp_driver_ppa/preinstall
esp-idf/preinstall: esp-idf/esp_eth/preinstall
esp-idf/preinstall: esp-idf/esp_gdbstub/preinstall
esp-idf/preinstall: esp-idf/esp_hid/preinstall
esp-idf/preinstall: esp-idf/tcp_transport/preinstall
esp-idf/preinstall: esp-idf/esp_http_client/preinstall
esp-idf/preinstall: esp-idf/esp_http_server/preinstall
esp-idf/preinstall: esp-idf/esp_https_ota/preinstall
esp-idf/preinstall: esp-idf/esp_https_server/preinstall
esp-idf/preinstall: esp-idf/esp_psram/preinstall
esp-idf/preinstall: esp-idf/esp_lcd/preinstall
esp-idf/preinstall: esp-idf/protobuf-c/preinstall
esp-idf/preinstall: esp-idf/protocomm/preinstall
esp-idf/preinstall: esp-idf/esp_local_ctrl/preinstall
esp-idf/preinstall: esp-idf/espcoredump/preinstall
esp-idf/preinstall: esp-idf/wear_levelling/preinstall
esp-idf/preinstall: esp-idf/fatfs/preinstall
esp-idf/preinstall: esp-idf/idf_test/preinstall
esp-idf/preinstall: esp-idf/ieee802154/preinstall
esp-idf/preinstall: esp-idf/json/preinstall
esp-idf/preinstall: esp-idf/mqtt/preinstall
esp-idf/preinstall: esp-idf/nvs_sec_provider/preinstall
esp-idf/preinstall: esp-idf/openthread/preinstall
esp-idf/preinstall: esp-idf/perfmon/preinstall
esp-idf/preinstall: esp-idf/spiffs/preinstall
esp-idf/preinstall: esp-idf/touch_element/preinstall
esp-idf/preinstall: esp-idf/ulp/preinstall
esp-idf/preinstall: esp-idf/usb/preinstall
esp-idf/preinstall: esp-idf/wifi_provisioning/preinstall
esp-idf/preinstall: esp-idf/78__esp-ml307/preinstall
esp-idf/preinstall: esp-idf/78__esp-opus/preinstall
esp-idf/preinstall: esp-idf/78__esp-opus-encoder/preinstall
esp-idf/preinstall: esp-idf/78__esp-wifi-connect/preinstall
esp-idf/preinstall: esp-idf/espressif__cmake_utilities/preinstall
esp-idf/preinstall: esp-idf/espressif__button/preinstall
esp-idf/preinstall: esp-idf/espressif__esp-dsp/preinstall
esp-idf/preinstall: esp-idf/espressif__esp-sr/preinstall
esp-idf/preinstall: esp-idf/espressif__esp_codec_dev/preinstall
esp-idf/preinstall: esp-idf/lvgl__lvgl/preinstall
esp-idf/preinstall: esp-idf/espressif__esp_lvgl_port/preinstall
esp-idf/preinstall: esp-idf/espressif__led_strip/preinstall
esp-idf/preinstall: esp-idf/main/preinstall
.PHONY : esp-idf/preinstall

# Recursive "clean" directory target.
esp-idf/clean: esp-idf/xtensa/clean
esp-idf/clean: esp-idf/esp_driver_gpio/clean
esp-idf/clean: esp-idf/esp_pm/clean
esp-idf/clean: esp-idf/mbedtls/clean
esp-idf/clean: esp-idf/bootloader/clean
esp-idf/clean: esp-idf/esptool_py/clean
esp-idf/clean: esp-idf/partition_table/clean
esp-idf/clean: esp-idf/esp_app_format/clean
esp-idf/clean: esp-idf/esp_bootloader_format/clean
esp-idf/clean: esp-idf/app_update/clean
esp-idf/clean: esp-idf/esp_partition/clean
esp-idf/clean: esp-idf/efuse/clean
esp-idf/clean: esp-idf/bootloader_support/clean
esp-idf/clean: esp-idf/esp_mm/clean
esp-idf/clean: esp-idf/spi_flash/clean
esp-idf/clean: esp-idf/esp_system/clean
esp-idf/clean: esp-idf/esp_common/clean
esp-idf/clean: esp-idf/esp_rom/clean
esp-idf/clean: esp-idf/hal/clean
esp-idf/clean: esp-idf/log/clean
esp-idf/clean: esp-idf/heap/clean
esp-idf/clean: esp-idf/soc/clean
esp-idf/clean: esp-idf/esp_hw_support/clean
esp-idf/clean: esp-idf/freertos/clean
esp-idf/clean: esp-idf/newlib/clean
esp-idf/clean: esp-idf/pthread/clean
esp-idf/clean: esp-idf/cxx/clean
esp-idf/clean: esp-idf/esp_timer/clean
esp-idf/clean: esp-idf/esp_driver_gptimer/clean
esp-idf/clean: esp-idf/esp_ringbuf/clean
esp-idf/clean: esp-idf/esp_driver_uart/clean
esp-idf/clean: esp-idf/app_trace/clean
esp-idf/clean: esp-idf/esp_event/clean
esp-idf/clean: esp-idf/nvs_flash/clean
esp-idf/clean: esp-idf/esp_driver_pcnt/clean
esp-idf/clean: esp-idf/esp_driver_spi/clean
esp-idf/clean: esp-idf/esp_driver_mcpwm/clean
esp-idf/clean: esp-idf/esp_driver_ana_cmpr/clean
esp-idf/clean: esp-idf/esp_driver_i2s/clean
esp-idf/clean: esp-idf/sdmmc/clean
esp-idf/clean: esp-idf/esp_driver_sdmmc/clean
esp-idf/clean: esp-idf/esp_driver_sdspi/clean
esp-idf/clean: esp-idf/esp_driver_sdio/clean
esp-idf/clean: esp-idf/esp_driver_dac/clean
esp-idf/clean: esp-idf/esp_driver_rmt/clean
esp-idf/clean: esp-idf/esp_driver_tsens/clean
esp-idf/clean: esp-idf/esp_driver_sdm/clean
esp-idf/clean: esp-idf/esp_driver_i2c/clean
esp-idf/clean: esp-idf/esp_driver_ledc/clean
esp-idf/clean: esp-idf/esp_driver_parlio/clean
esp-idf/clean: esp-idf/esp_driver_usb_serial_jtag/clean
esp-idf/clean: esp-idf/driver/clean
esp-idf/clean: esp-idf/esp_phy/clean
esp-idf/clean: esp-idf/esp_vfs_console/clean
esp-idf/clean: esp-idf/vfs/clean
esp-idf/clean: esp-idf/lwip/clean
esp-idf/clean: esp-idf/esp_netif_stack/clean
esp-idf/clean: esp-idf/esp_netif/clean
esp-idf/clean: esp-idf/wpa_supplicant/clean
esp-idf/clean: esp-idf/esp_coex/clean
esp-idf/clean: esp-idf/esp_wifi/clean
esp-idf/clean: esp-idf/bt/clean
esp-idf/clean: esp-idf/unity/clean
esp-idf/clean: esp-idf/cmock/clean
esp-idf/clean: esp-idf/console/clean
esp-idf/clean: esp-idf/http_parser/clean
esp-idf/clean: esp-idf/esp-tls/clean
esp-idf/clean: esp-idf/esp_adc/clean
esp-idf/clean: esp-idf/esp_driver_isp/clean
esp-idf/clean: esp-idf/esp_driver_cam/clean
esp-idf/clean: esp-idf/esp_driver_jpeg/clean
esp-idf/clean: esp-idf/esp_driver_ppa/clean
esp-idf/clean: esp-idf/esp_eth/clean
esp-idf/clean: esp-idf/esp_gdbstub/clean
esp-idf/clean: esp-idf/esp_hid/clean
esp-idf/clean: esp-idf/tcp_transport/clean
esp-idf/clean: esp-idf/esp_http_client/clean
esp-idf/clean: esp-idf/esp_http_server/clean
esp-idf/clean: esp-idf/esp_https_ota/clean
esp-idf/clean: esp-idf/esp_https_server/clean
esp-idf/clean: esp-idf/esp_psram/clean
esp-idf/clean: esp-idf/esp_lcd/clean
esp-idf/clean: esp-idf/protobuf-c/clean
esp-idf/clean: esp-idf/protocomm/clean
esp-idf/clean: esp-idf/esp_local_ctrl/clean
esp-idf/clean: esp-idf/espcoredump/clean
esp-idf/clean: esp-idf/wear_levelling/clean
esp-idf/clean: esp-idf/fatfs/clean
esp-idf/clean: esp-idf/idf_test/clean
esp-idf/clean: esp-idf/ieee802154/clean
esp-idf/clean: esp-idf/json/clean
esp-idf/clean: esp-idf/mqtt/clean
esp-idf/clean: esp-idf/nvs_sec_provider/clean
esp-idf/clean: esp-idf/openthread/clean
esp-idf/clean: esp-idf/perfmon/clean
esp-idf/clean: esp-idf/spiffs/clean
esp-idf/clean: esp-idf/touch_element/clean
esp-idf/clean: esp-idf/ulp/clean
esp-idf/clean: esp-idf/usb/clean
esp-idf/clean: esp-idf/wifi_provisioning/clean
esp-idf/clean: esp-idf/78__esp-ml307/clean
esp-idf/clean: esp-idf/78__esp-opus/clean
esp-idf/clean: esp-idf/78__esp-opus-encoder/clean
esp-idf/clean: esp-idf/78__esp-wifi-connect/clean
esp-idf/clean: esp-idf/espressif__cmake_utilities/clean
esp-idf/clean: esp-idf/espressif__button/clean
esp-idf/clean: esp-idf/espressif__esp-dsp/clean
esp-idf/clean: esp-idf/espressif__esp-sr/clean
esp-idf/clean: esp-idf/espressif__esp_codec_dev/clean
esp-idf/clean: esp-idf/lvgl__lvgl/clean
esp-idf/clean: esp-idf/espressif__esp_lvgl_port/clean
esp-idf/clean: esp-idf/espressif__led_strip/clean
esp-idf/clean: esp-idf/main/clean
.PHONY : esp-idf/clean

#=============================================================================
# Directory level rules for directory esp-idf/78__esp-ml307

# Recursive "all" directory target.
esp-idf/78__esp-ml307/all: esp-idf/78__esp-ml307/CMakeFiles/__idf_78__esp-ml307.dir/all
.PHONY : esp-idf/78__esp-ml307/all

# Recursive "preinstall" directory target.
esp-idf/78__esp-ml307/preinstall:
.PHONY : esp-idf/78__esp-ml307/preinstall

# Recursive "clean" directory target.
esp-idf/78__esp-ml307/clean: esp-idf/78__esp-ml307/CMakeFiles/__idf_78__esp-ml307.dir/clean
.PHONY : esp-idf/78__esp-ml307/clean

#=============================================================================
# Directory level rules for directory esp-idf/78__esp-opus

# Recursive "all" directory target.
esp-idf/78__esp-opus/all: esp-idf/78__esp-opus/CMakeFiles/__idf_78__esp-opus.dir/all
.PHONY : esp-idf/78__esp-opus/all

# Recursive "preinstall" directory target.
esp-idf/78__esp-opus/preinstall:
.PHONY : esp-idf/78__esp-opus/preinstall

# Recursive "clean" directory target.
esp-idf/78__esp-opus/clean: esp-idf/78__esp-opus/CMakeFiles/__idf_78__esp-opus.dir/clean
.PHONY : esp-idf/78__esp-opus/clean

#=============================================================================
# Directory level rules for directory esp-idf/78__esp-opus-encoder

# Recursive "all" directory target.
esp-idf/78__esp-opus-encoder/all: esp-idf/78__esp-opus-encoder/CMakeFiles/__idf_78__esp-opus-encoder.dir/all
.PHONY : esp-idf/78__esp-opus-encoder/all

# Recursive "preinstall" directory target.
esp-idf/78__esp-opus-encoder/preinstall:
.PHONY : esp-idf/78__esp-opus-encoder/preinstall

# Recursive "clean" directory target.
esp-idf/78__esp-opus-encoder/clean: esp-idf/78__esp-opus-encoder/CMakeFiles/__idf_78__esp-opus-encoder.dir/clean
.PHONY : esp-idf/78__esp-opus-encoder/clean

#=============================================================================
# Directory level rules for directory esp-idf/78__esp-wifi-connect

# Recursive "all" directory target.
esp-idf/78__esp-wifi-connect/all: esp-idf/78__esp-wifi-connect/CMakeFiles/__idf_78__esp-wifi-connect.dir/all
.PHONY : esp-idf/78__esp-wifi-connect/all

# Recursive "preinstall" directory target.
esp-idf/78__esp-wifi-connect/preinstall:
.PHONY : esp-idf/78__esp-wifi-connect/preinstall

# Recursive "clean" directory target.
esp-idf/78__esp-wifi-connect/clean: esp-idf/78__esp-wifi-connect/CMakeFiles/__idf_78__esp-wifi-connect.dir/clean
	$(CMAKE_COMMAND) -P esp-idf/78__esp-wifi-connect/CMakeFiles/cmake_directory_clean.cmake
.PHONY : esp-idf/78__esp-wifi-connect/clean

#=============================================================================
# Directory level rules for directory esp-idf/app_trace

# Recursive "all" directory target.
esp-idf/app_trace/all: esp-idf/app_trace/CMakeFiles/__idf_app_trace.dir/all
.PHONY : esp-idf/app_trace/all

# Recursive "preinstall" directory target.
esp-idf/app_trace/preinstall:
.PHONY : esp-idf/app_trace/preinstall

# Recursive "clean" directory target.
esp-idf/app_trace/clean: esp-idf/app_trace/CMakeFiles/__idf_app_trace.dir/clean
.PHONY : esp-idf/app_trace/clean

#=============================================================================
# Directory level rules for directory esp-idf/app_update

# Recursive "all" directory target.
esp-idf/app_update/all: esp-idf/app_update/CMakeFiles/__idf_app_update.dir/all
esp-idf/app_update/all: esp-idf/app_update/CMakeFiles/blank_ota_data.dir/all
.PHONY : esp-idf/app_update/all

# Recursive "preinstall" directory target.
esp-idf/app_update/preinstall:
.PHONY : esp-idf/app_update/preinstall

# Recursive "clean" directory target.
esp-idf/app_update/clean: esp-idf/app_update/CMakeFiles/__idf_app_update.dir/clean
esp-idf/app_update/clean: esp-idf/app_update/CMakeFiles/blank_ota_data.dir/clean
esp-idf/app_update/clean: esp-idf/app_update/CMakeFiles/read-otadata.dir/clean
esp-idf/app_update/clean: esp-idf/app_update/CMakeFiles/read_otadata.dir/clean
esp-idf/app_update/clean: esp-idf/app_update/CMakeFiles/erase-otadata.dir/clean
esp-idf/app_update/clean: esp-idf/app_update/CMakeFiles/erase_otadata.dir/clean
esp-idf/app_update/clean: esp-idf/app_update/CMakeFiles/otadata-flash.dir/clean
esp-idf/app_update/clean: esp-idf/app_update/CMakeFiles/encrypted-otadata-flash.dir/clean
.PHONY : esp-idf/app_update/clean

#=============================================================================
# Directory level rules for directory esp-idf/bootloader

# Recursive "all" directory target.
esp-idf/bootloader/all:
.PHONY : esp-idf/bootloader/all

# Recursive "preinstall" directory target.
esp-idf/bootloader/preinstall:
.PHONY : esp-idf/bootloader/preinstall

# Recursive "clean" directory target.
esp-idf/bootloader/clean: esp-idf/bootloader/CMakeFiles/bootloader-flash.dir/clean
esp-idf/bootloader/clean: esp-idf/bootloader/CMakeFiles/encrypted-bootloader-flash.dir/clean
	$(CMAKE_COMMAND) -P esp-idf/bootloader/CMakeFiles/cmake_directory_clean.cmake
.PHONY : esp-idf/bootloader/clean

#=============================================================================
# Directory level rules for directory esp-idf/bootloader_support

# Recursive "all" directory target.
esp-idf/bootloader_support/all: esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/all
.PHONY : esp-idf/bootloader_support/all

# Recursive "preinstall" directory target.
esp-idf/bootloader_support/preinstall:
.PHONY : esp-idf/bootloader_support/preinstall

# Recursive "clean" directory target.
esp-idf/bootloader_support/clean: esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/clean
.PHONY : esp-idf/bootloader_support/clean

#=============================================================================
# Directory level rules for directory esp-idf/bt

# Recursive "all" directory target.
esp-idf/bt/all:
.PHONY : esp-idf/bt/all

# Recursive "preinstall" directory target.
esp-idf/bt/preinstall:
.PHONY : esp-idf/bt/preinstall

# Recursive "clean" directory target.
esp-idf/bt/clean:
.PHONY : esp-idf/bt/clean

#=============================================================================
# Directory level rules for directory esp-idf/cmock

# Recursive "all" directory target.
esp-idf/cmock/all: esp-idf/cmock/CMakeFiles/__idf_cmock.dir/all
.PHONY : esp-idf/cmock/all

# Recursive "preinstall" directory target.
esp-idf/cmock/preinstall:
.PHONY : esp-idf/cmock/preinstall

# Recursive "clean" directory target.
esp-idf/cmock/clean: esp-idf/cmock/CMakeFiles/__idf_cmock.dir/clean
.PHONY : esp-idf/cmock/clean

#=============================================================================
# Directory level rules for directory esp-idf/console

# Recursive "all" directory target.
esp-idf/console/all: esp-idf/console/CMakeFiles/__idf_console.dir/all
.PHONY : esp-idf/console/all

# Recursive "preinstall" directory target.
esp-idf/console/preinstall:
.PHONY : esp-idf/console/preinstall

# Recursive "clean" directory target.
esp-idf/console/clean: esp-idf/console/CMakeFiles/__idf_console.dir/clean
.PHONY : esp-idf/console/clean

#=============================================================================
# Directory level rules for directory esp-idf/cxx

# Recursive "all" directory target.
esp-idf/cxx/all: esp-idf/cxx/CMakeFiles/__idf_cxx.dir/all
.PHONY : esp-idf/cxx/all

# Recursive "preinstall" directory target.
esp-idf/cxx/preinstall:
.PHONY : esp-idf/cxx/preinstall

# Recursive "clean" directory target.
esp-idf/cxx/clean: esp-idf/cxx/CMakeFiles/__idf_cxx.dir/clean
.PHONY : esp-idf/cxx/clean

#=============================================================================
# Directory level rules for directory esp-idf/driver

# Recursive "all" directory target.
esp-idf/driver/all: esp-idf/driver/CMakeFiles/__idf_driver.dir/all
.PHONY : esp-idf/driver/all

# Recursive "preinstall" directory target.
esp-idf/driver/preinstall:
.PHONY : esp-idf/driver/preinstall

# Recursive "clean" directory target.
esp-idf/driver/clean: esp-idf/driver/CMakeFiles/__idf_driver.dir/clean
.PHONY : esp-idf/driver/clean

#=============================================================================
# Directory level rules for directory esp-idf/efuse

# Recursive "all" directory target.
esp-idf/efuse/all: esp-idf/efuse/CMakeFiles/__idf_efuse.dir/all
.PHONY : esp-idf/efuse/all

# Recursive "preinstall" directory target.
esp-idf/efuse/preinstall:
.PHONY : esp-idf/efuse/preinstall

# Recursive "clean" directory target.
esp-idf/efuse/clean: esp-idf/efuse/CMakeFiles/__idf_efuse.dir/clean
esp-idf/efuse/clean: esp-idf/efuse/CMakeFiles/efuse-common-table.dir/clean
esp-idf/efuse/clean: esp-idf/efuse/CMakeFiles/efuse_common_table.dir/clean
esp-idf/efuse/clean: esp-idf/efuse/CMakeFiles/efuse-custom-table.dir/clean
esp-idf/efuse/clean: esp-idf/efuse/CMakeFiles/efuse_custom_table.dir/clean
esp-idf/efuse/clean: esp-idf/efuse/CMakeFiles/show-efuse-table.dir/clean
esp-idf/efuse/clean: esp-idf/efuse/CMakeFiles/show_efuse_table.dir/clean
esp-idf/efuse/clean: esp-idf/efuse/CMakeFiles/efuse_test_table.dir/clean
.PHONY : esp-idf/efuse/clean

#=============================================================================
# Directory level rules for directory esp-idf/esp-tls

# Recursive "all" directory target.
esp-idf/esp-tls/all: esp-idf/esp-tls/CMakeFiles/__idf_esp-tls.dir/all
.PHONY : esp-idf/esp-tls/all

# Recursive "preinstall" directory target.
esp-idf/esp-tls/preinstall:
.PHONY : esp-idf/esp-tls/preinstall

# Recursive "clean" directory target.
esp-idf/esp-tls/clean: esp-idf/esp-tls/CMakeFiles/__idf_esp-tls.dir/clean
.PHONY : esp-idf/esp-tls/clean

#=============================================================================
# Directory level rules for directory esp-idf/esp_adc

# Recursive "all" directory target.
esp-idf/esp_adc/all: esp-idf/esp_adc/CMakeFiles/__idf_esp_adc.dir/all
.PHONY : esp-idf/esp_adc/all

# Recursive "preinstall" directory target.
esp-idf/esp_adc/preinstall:
.PHONY : esp-idf/esp_adc/preinstall

# Recursive "clean" directory target.
esp-idf/esp_adc/clean: esp-idf/esp_adc/CMakeFiles/__idf_esp_adc.dir/clean
.PHONY : esp-idf/esp_adc/clean

#=============================================================================
# Directory level rules for directory esp-idf/esp_app_format

# Recursive "all" directory target.
esp-idf/esp_app_format/all: esp-idf/esp_app_format/CMakeFiles/__idf_esp_app_format.dir/all
.PHONY : esp-idf/esp_app_format/all

# Recursive "preinstall" directory target.
esp-idf/esp_app_format/preinstall:
.PHONY : esp-idf/esp_app_format/preinstall

# Recursive "clean" directory target.
esp-idf/esp_app_format/clean: esp-idf/esp_app_format/CMakeFiles/__idf_esp_app_format.dir/clean
.PHONY : esp-idf/esp_app_format/clean

#=============================================================================
# Directory level rules for directory esp-idf/esp_bootloader_format

# Recursive "all" directory target.
esp-idf/esp_bootloader_format/all: esp-idf/esp_bootloader_format/CMakeFiles/__idf_esp_bootloader_format.dir/all
.PHONY : esp-idf/esp_bootloader_format/all

# Recursive "preinstall" directory target.
esp-idf/esp_bootloader_format/preinstall:
.PHONY : esp-idf/esp_bootloader_format/preinstall

# Recursive "clean" directory target.
esp-idf/esp_bootloader_format/clean: esp-idf/esp_bootloader_format/CMakeFiles/__idf_esp_bootloader_format.dir/clean
.PHONY : esp-idf/esp_bootloader_format/clean

#=============================================================================
# Directory level rules for directory esp-idf/esp_coex

# Recursive "all" directory target.
esp-idf/esp_coex/all: esp-idf/esp_coex/CMakeFiles/__idf_esp_coex.dir/all
.PHONY : esp-idf/esp_coex/all

# Recursive "preinstall" directory target.
esp-idf/esp_coex/preinstall:
.PHONY : esp-idf/esp_coex/preinstall

# Recursive "clean" directory target.
esp-idf/esp_coex/clean: esp-idf/esp_coex/CMakeFiles/__idf_esp_coex.dir/clean
.PHONY : esp-idf/esp_coex/clean

#=============================================================================
# Directory level rules for directory esp-idf/esp_common

# Recursive "all" directory target.
esp-idf/esp_common/all: esp-idf/esp_common/CMakeFiles/__idf_esp_common.dir/all
.PHONY : esp-idf/esp_common/all

# Recursive "preinstall" directory target.
esp-idf/esp_common/preinstall:
.PHONY : esp-idf/esp_common/preinstall

# Recursive "clean" directory target.
esp-idf/esp_common/clean: esp-idf/esp_common/CMakeFiles/__idf_esp_common.dir/clean
.PHONY : esp-idf/esp_common/clean

#=============================================================================
# Directory level rules for directory esp-idf/esp_driver_ana_cmpr

# Recursive "all" directory target.
esp-idf/esp_driver_ana_cmpr/all:
.PHONY : esp-idf/esp_driver_ana_cmpr/all

# Recursive "preinstall" directory target.
esp-idf/esp_driver_ana_cmpr/preinstall:
.PHONY : esp-idf/esp_driver_ana_cmpr/preinstall

# Recursive "clean" directory target.
esp-idf/esp_driver_ana_cmpr/clean:
.PHONY : esp-idf/esp_driver_ana_cmpr/clean

#=============================================================================
# Directory level rules for directory esp-idf/esp_driver_cam

# Recursive "all" directory target.
esp-idf/esp_driver_cam/all: esp-idf/esp_driver_cam/CMakeFiles/__idf_esp_driver_cam.dir/all
.PHONY : esp-idf/esp_driver_cam/all

# Recursive "preinstall" directory target.
esp-idf/esp_driver_cam/preinstall:
.PHONY : esp-idf/esp_driver_cam/preinstall

# Recursive "clean" directory target.
esp-idf/esp_driver_cam/clean: esp-idf/esp_driver_cam/CMakeFiles/__idf_esp_driver_cam.dir/clean
.PHONY : esp-idf/esp_driver_cam/clean

#=============================================================================
# Directory level rules for directory esp-idf/esp_driver_dac

# Recursive "all" directory target.
esp-idf/esp_driver_dac/all:
.PHONY : esp-idf/esp_driver_dac/all

# Recursive "preinstall" directory target.
esp-idf/esp_driver_dac/preinstall:
.PHONY : esp-idf/esp_driver_dac/preinstall

# Recursive "clean" directory target.
esp-idf/esp_driver_dac/clean:
.PHONY : esp-idf/esp_driver_dac/clean

#=============================================================================
# Directory level rules for directory esp-idf/esp_driver_gpio

# Recursive "all" directory target.
esp-idf/esp_driver_gpio/all: esp-idf/esp_driver_gpio/CMakeFiles/__idf_esp_driver_gpio.dir/all
.PHONY : esp-idf/esp_driver_gpio/all

# Recursive "preinstall" directory target.
esp-idf/esp_driver_gpio/preinstall:
.PHONY : esp-idf/esp_driver_gpio/preinstall

# Recursive "clean" directory target.
esp-idf/esp_driver_gpio/clean: esp-idf/esp_driver_gpio/CMakeFiles/__idf_esp_driver_gpio.dir/clean
.PHONY : esp-idf/esp_driver_gpio/clean

#=============================================================================
# Directory level rules for directory esp-idf/esp_driver_gptimer

# Recursive "all" directory target.
esp-idf/esp_driver_gptimer/all: esp-idf/esp_driver_gptimer/CMakeFiles/__idf_esp_driver_gptimer.dir/all
.PHONY : esp-idf/esp_driver_gptimer/all

# Recursive "preinstall" directory target.
esp-idf/esp_driver_gptimer/preinstall:
.PHONY : esp-idf/esp_driver_gptimer/preinstall

# Recursive "clean" directory target.
esp-idf/esp_driver_gptimer/clean: esp-idf/esp_driver_gptimer/CMakeFiles/__idf_esp_driver_gptimer.dir/clean
.PHONY : esp-idf/esp_driver_gptimer/clean

#=============================================================================
# Directory level rules for directory esp-idf/esp_driver_i2c

# Recursive "all" directory target.
esp-idf/esp_driver_i2c/all: esp-idf/esp_driver_i2c/CMakeFiles/__idf_esp_driver_i2c.dir/all
.PHONY : esp-idf/esp_driver_i2c/all

# Recursive "preinstall" directory target.
esp-idf/esp_driver_i2c/preinstall:
.PHONY : esp-idf/esp_driver_i2c/preinstall

# Recursive "clean" directory target.
esp-idf/esp_driver_i2c/clean: esp-idf/esp_driver_i2c/CMakeFiles/__idf_esp_driver_i2c.dir/clean
.PHONY : esp-idf/esp_driver_i2c/clean

#=============================================================================
# Directory level rules for directory esp-idf/esp_driver_i2s

# Recursive "all" directory target.
esp-idf/esp_driver_i2s/all: esp-idf/esp_driver_i2s/CMakeFiles/__idf_esp_driver_i2s.dir/all
.PHONY : esp-idf/esp_driver_i2s/all

# Recursive "preinstall" directory target.
esp-idf/esp_driver_i2s/preinstall:
.PHONY : esp-idf/esp_driver_i2s/preinstall

# Recursive "clean" directory target.
esp-idf/esp_driver_i2s/clean: esp-idf/esp_driver_i2s/CMakeFiles/__idf_esp_driver_i2s.dir/clean
.PHONY : esp-idf/esp_driver_i2s/clean

#=============================================================================
# Directory level rules for directory esp-idf/esp_driver_isp

# Recursive "all" directory target.
esp-idf/esp_driver_isp/all:
.PHONY : esp-idf/esp_driver_isp/all

# Recursive "preinstall" directory target.
esp-idf/esp_driver_isp/preinstall:
.PHONY : esp-idf/esp_driver_isp/preinstall

# Recursive "clean" directory target.
esp-idf/esp_driver_isp/clean:
.PHONY : esp-idf/esp_driver_isp/clean

#=============================================================================
# Directory level rules for directory esp-idf/esp_driver_jpeg

# Recursive "all" directory target.
esp-idf/esp_driver_jpeg/all:
.PHONY : esp-idf/esp_driver_jpeg/all

# Recursive "preinstall" directory target.
esp-idf/esp_driver_jpeg/preinstall:
.PHONY : esp-idf/esp_driver_jpeg/preinstall

# Recursive "clean" directory target.
esp-idf/esp_driver_jpeg/clean:
.PHONY : esp-idf/esp_driver_jpeg/clean

#=============================================================================
# Directory level rules for directory esp-idf/esp_driver_ledc

# Recursive "all" directory target.
esp-idf/esp_driver_ledc/all: esp-idf/esp_driver_ledc/CMakeFiles/__idf_esp_driver_ledc.dir/all
.PHONY : esp-idf/esp_driver_ledc/all

# Recursive "preinstall" directory target.
esp-idf/esp_driver_ledc/preinstall:
.PHONY : esp-idf/esp_driver_ledc/preinstall

# Recursive "clean" directory target.
esp-idf/esp_driver_ledc/clean: esp-idf/esp_driver_ledc/CMakeFiles/__idf_esp_driver_ledc.dir/clean
.PHONY : esp-idf/esp_driver_ledc/clean

#=============================================================================
# Directory level rules for directory esp-idf/esp_driver_mcpwm

# Recursive "all" directory target.
esp-idf/esp_driver_mcpwm/all: esp-idf/esp_driver_mcpwm/CMakeFiles/__idf_esp_driver_mcpwm.dir/all
.PHONY : esp-idf/esp_driver_mcpwm/all

# Recursive "preinstall" directory target.
esp-idf/esp_driver_mcpwm/preinstall:
.PHONY : esp-idf/esp_driver_mcpwm/preinstall

# Recursive "clean" directory target.
esp-idf/esp_driver_mcpwm/clean: esp-idf/esp_driver_mcpwm/CMakeFiles/__idf_esp_driver_mcpwm.dir/clean
.PHONY : esp-idf/esp_driver_mcpwm/clean

#=============================================================================
# Directory level rules for directory esp-idf/esp_driver_parlio

# Recursive "all" directory target.
esp-idf/esp_driver_parlio/all:
.PHONY : esp-idf/esp_driver_parlio/all

# Recursive "preinstall" directory target.
esp-idf/esp_driver_parlio/preinstall:
.PHONY : esp-idf/esp_driver_parlio/preinstall

# Recursive "clean" directory target.
esp-idf/esp_driver_parlio/clean:
.PHONY : esp-idf/esp_driver_parlio/clean

#=============================================================================
# Directory level rules for directory esp-idf/esp_driver_pcnt

# Recursive "all" directory target.
esp-idf/esp_driver_pcnt/all: esp-idf/esp_driver_pcnt/CMakeFiles/__idf_esp_driver_pcnt.dir/all
.PHONY : esp-idf/esp_driver_pcnt/all

# Recursive "preinstall" directory target.
esp-idf/esp_driver_pcnt/preinstall:
.PHONY : esp-idf/esp_driver_pcnt/preinstall

# Recursive "clean" directory target.
esp-idf/esp_driver_pcnt/clean: esp-idf/esp_driver_pcnt/CMakeFiles/__idf_esp_driver_pcnt.dir/clean
.PHONY : esp-idf/esp_driver_pcnt/clean

#=============================================================================
# Directory level rules for directory esp-idf/esp_driver_ppa

# Recursive "all" directory target.
esp-idf/esp_driver_ppa/all:
.PHONY : esp-idf/esp_driver_ppa/all

# Recursive "preinstall" directory target.
esp-idf/esp_driver_ppa/preinstall:
.PHONY : esp-idf/esp_driver_ppa/preinstall

# Recursive "clean" directory target.
esp-idf/esp_driver_ppa/clean:
.PHONY : esp-idf/esp_driver_ppa/clean

#=============================================================================
# Directory level rules for directory esp-idf/esp_driver_rmt

# Recursive "all" directory target.
esp-idf/esp_driver_rmt/all: esp-idf/esp_driver_rmt/CMakeFiles/__idf_esp_driver_rmt.dir/all
.PHONY : esp-idf/esp_driver_rmt/all

# Recursive "preinstall" directory target.
esp-idf/esp_driver_rmt/preinstall:
.PHONY : esp-idf/esp_driver_rmt/preinstall

# Recursive "clean" directory target.
esp-idf/esp_driver_rmt/clean: esp-idf/esp_driver_rmt/CMakeFiles/__idf_esp_driver_rmt.dir/clean
.PHONY : esp-idf/esp_driver_rmt/clean

#=============================================================================
# Directory level rules for directory esp-idf/esp_driver_sdio

# Recursive "all" directory target.
esp-idf/esp_driver_sdio/all:
.PHONY : esp-idf/esp_driver_sdio/all

# Recursive "preinstall" directory target.
esp-idf/esp_driver_sdio/preinstall:
.PHONY : esp-idf/esp_driver_sdio/preinstall

# Recursive "clean" directory target.
esp-idf/esp_driver_sdio/clean:
.PHONY : esp-idf/esp_driver_sdio/clean

#=============================================================================
# Directory level rules for directory esp-idf/esp_driver_sdm

# Recursive "all" directory target.
esp-idf/esp_driver_sdm/all: esp-idf/esp_driver_sdm/CMakeFiles/__idf_esp_driver_sdm.dir/all
.PHONY : esp-idf/esp_driver_sdm/all

# Recursive "preinstall" directory target.
esp-idf/esp_driver_sdm/preinstall:
.PHONY : esp-idf/esp_driver_sdm/preinstall

# Recursive "clean" directory target.
esp-idf/esp_driver_sdm/clean: esp-idf/esp_driver_sdm/CMakeFiles/__idf_esp_driver_sdm.dir/clean
.PHONY : esp-idf/esp_driver_sdm/clean

#=============================================================================
# Directory level rules for directory esp-idf/esp_driver_sdmmc

# Recursive "all" directory target.
esp-idf/esp_driver_sdmmc/all: esp-idf/esp_driver_sdmmc/CMakeFiles/__idf_esp_driver_sdmmc.dir/all
.PHONY : esp-idf/esp_driver_sdmmc/all

# Recursive "preinstall" directory target.
esp-idf/esp_driver_sdmmc/preinstall:
.PHONY : esp-idf/esp_driver_sdmmc/preinstall

# Recursive "clean" directory target.
esp-idf/esp_driver_sdmmc/clean: esp-idf/esp_driver_sdmmc/CMakeFiles/__idf_esp_driver_sdmmc.dir/clean
.PHONY : esp-idf/esp_driver_sdmmc/clean

#=============================================================================
# Directory level rules for directory esp-idf/esp_driver_sdspi

# Recursive "all" directory target.
esp-idf/esp_driver_sdspi/all: esp-idf/esp_driver_sdspi/CMakeFiles/__idf_esp_driver_sdspi.dir/all
.PHONY : esp-idf/esp_driver_sdspi/all

# Recursive "preinstall" directory target.
esp-idf/esp_driver_sdspi/preinstall:
.PHONY : esp-idf/esp_driver_sdspi/preinstall

# Recursive "clean" directory target.
esp-idf/esp_driver_sdspi/clean: esp-idf/esp_driver_sdspi/CMakeFiles/__idf_esp_driver_sdspi.dir/clean
.PHONY : esp-idf/esp_driver_sdspi/clean

#=============================================================================
# Directory level rules for directory esp-idf/esp_driver_spi

# Recursive "all" directory target.
esp-idf/esp_driver_spi/all: esp-idf/esp_driver_spi/CMakeFiles/__idf_esp_driver_spi.dir/all
.PHONY : esp-idf/esp_driver_spi/all

# Recursive "preinstall" directory target.
esp-idf/esp_driver_spi/preinstall:
.PHONY : esp-idf/esp_driver_spi/preinstall

# Recursive "clean" directory target.
esp-idf/esp_driver_spi/clean: esp-idf/esp_driver_spi/CMakeFiles/__idf_esp_driver_spi.dir/clean
.PHONY : esp-idf/esp_driver_spi/clean

#=============================================================================
# Directory level rules for directory esp-idf/esp_driver_tsens

# Recursive "all" directory target.
esp-idf/esp_driver_tsens/all: esp-idf/esp_driver_tsens/CMakeFiles/__idf_esp_driver_tsens.dir/all
.PHONY : esp-idf/esp_driver_tsens/all

# Recursive "preinstall" directory target.
esp-idf/esp_driver_tsens/preinstall:
.PHONY : esp-idf/esp_driver_tsens/preinstall

# Recursive "clean" directory target.
esp-idf/esp_driver_tsens/clean: esp-idf/esp_driver_tsens/CMakeFiles/__idf_esp_driver_tsens.dir/clean
.PHONY : esp-idf/esp_driver_tsens/clean

#=============================================================================
# Directory level rules for directory esp-idf/esp_driver_uart

# Recursive "all" directory target.
esp-idf/esp_driver_uart/all: esp-idf/esp_driver_uart/CMakeFiles/__idf_esp_driver_uart.dir/all
.PHONY : esp-idf/esp_driver_uart/all

# Recursive "preinstall" directory target.
esp-idf/esp_driver_uart/preinstall:
.PHONY : esp-idf/esp_driver_uart/preinstall

# Recursive "clean" directory target.
esp-idf/esp_driver_uart/clean: esp-idf/esp_driver_uart/CMakeFiles/__idf_esp_driver_uart.dir/clean
.PHONY : esp-idf/esp_driver_uart/clean

#=============================================================================
# Directory level rules for directory esp-idf/esp_driver_usb_serial_jtag

# Recursive "all" directory target.
esp-idf/esp_driver_usb_serial_jtag/all: esp-idf/esp_driver_usb_serial_jtag/CMakeFiles/__idf_esp_driver_usb_serial_jtag.dir/all
.PHONY : esp-idf/esp_driver_usb_serial_jtag/all

# Recursive "preinstall" directory target.
esp-idf/esp_driver_usb_serial_jtag/preinstall:
.PHONY : esp-idf/esp_driver_usb_serial_jtag/preinstall

# Recursive "clean" directory target.
esp-idf/esp_driver_usb_serial_jtag/clean: esp-idf/esp_driver_usb_serial_jtag/CMakeFiles/__idf_esp_driver_usb_serial_jtag.dir/clean
.PHONY : esp-idf/esp_driver_usb_serial_jtag/clean

#=============================================================================
# Directory level rules for directory esp-idf/esp_eth

# Recursive "all" directory target.
esp-idf/esp_eth/all: esp-idf/esp_eth/CMakeFiles/__idf_esp_eth.dir/all
.PHONY : esp-idf/esp_eth/all

# Recursive "preinstall" directory target.
esp-idf/esp_eth/preinstall:
.PHONY : esp-idf/esp_eth/preinstall

# Recursive "clean" directory target.
esp-idf/esp_eth/clean: esp-idf/esp_eth/CMakeFiles/__idf_esp_eth.dir/clean
.PHONY : esp-idf/esp_eth/clean

#=============================================================================
# Directory level rules for directory esp-idf/esp_event

# Recursive "all" directory target.
esp-idf/esp_event/all: esp-idf/esp_event/CMakeFiles/__idf_esp_event.dir/all
.PHONY : esp-idf/esp_event/all

# Recursive "preinstall" directory target.
esp-idf/esp_event/preinstall:
.PHONY : esp-idf/esp_event/preinstall

# Recursive "clean" directory target.
esp-idf/esp_event/clean: esp-idf/esp_event/CMakeFiles/__idf_esp_event.dir/clean
.PHONY : esp-idf/esp_event/clean

#=============================================================================
# Directory level rules for directory esp-idf/esp_gdbstub

# Recursive "all" directory target.
esp-idf/esp_gdbstub/all: esp-idf/esp_gdbstub/CMakeFiles/__idf_esp_gdbstub.dir/all
.PHONY : esp-idf/esp_gdbstub/all

# Recursive "preinstall" directory target.
esp-idf/esp_gdbstub/preinstall:
.PHONY : esp-idf/esp_gdbstub/preinstall

# Recursive "clean" directory target.
esp-idf/esp_gdbstub/clean: esp-idf/esp_gdbstub/CMakeFiles/__idf_esp_gdbstub.dir/clean
.PHONY : esp-idf/esp_gdbstub/clean

#=============================================================================
# Directory level rules for directory esp-idf/esp_hid

# Recursive "all" directory target.
esp-idf/esp_hid/all: esp-idf/esp_hid/CMakeFiles/__idf_esp_hid.dir/all
.PHONY : esp-idf/esp_hid/all

# Recursive "preinstall" directory target.
esp-idf/esp_hid/preinstall:
.PHONY : esp-idf/esp_hid/preinstall

# Recursive "clean" directory target.
esp-idf/esp_hid/clean: esp-idf/esp_hid/CMakeFiles/__idf_esp_hid.dir/clean
.PHONY : esp-idf/esp_hid/clean

#=============================================================================
# Directory level rules for directory esp-idf/esp_http_client

# Recursive "all" directory target.
esp-idf/esp_http_client/all: esp-idf/esp_http_client/CMakeFiles/__idf_esp_http_client.dir/all
.PHONY : esp-idf/esp_http_client/all

# Recursive "preinstall" directory target.
esp-idf/esp_http_client/preinstall:
.PHONY : esp-idf/esp_http_client/preinstall

# Recursive "clean" directory target.
esp-idf/esp_http_client/clean: esp-idf/esp_http_client/CMakeFiles/__idf_esp_http_client.dir/clean
.PHONY : esp-idf/esp_http_client/clean

#=============================================================================
# Directory level rules for directory esp-idf/esp_http_server

# Recursive "all" directory target.
esp-idf/esp_http_server/all: esp-idf/esp_http_server/CMakeFiles/__idf_esp_http_server.dir/all
.PHONY : esp-idf/esp_http_server/all

# Recursive "preinstall" directory target.
esp-idf/esp_http_server/preinstall:
.PHONY : esp-idf/esp_http_server/preinstall

# Recursive "clean" directory target.
esp-idf/esp_http_server/clean: esp-idf/esp_http_server/CMakeFiles/__idf_esp_http_server.dir/clean
.PHONY : esp-idf/esp_http_server/clean

#=============================================================================
# Directory level rules for directory esp-idf/esp_https_ota

# Recursive "all" directory target.
esp-idf/esp_https_ota/all: esp-idf/esp_https_ota/CMakeFiles/__idf_esp_https_ota.dir/all
.PHONY : esp-idf/esp_https_ota/all

# Recursive "preinstall" directory target.
esp-idf/esp_https_ota/preinstall:
.PHONY : esp-idf/esp_https_ota/preinstall

# Recursive "clean" directory target.
esp-idf/esp_https_ota/clean: esp-idf/esp_https_ota/CMakeFiles/__idf_esp_https_ota.dir/clean
.PHONY : esp-idf/esp_https_ota/clean

#=============================================================================
# Directory level rules for directory esp-idf/esp_https_server

# Recursive "all" directory target.
esp-idf/esp_https_server/all: esp-idf/esp_https_server/CMakeFiles/__idf_esp_https_server.dir/all
.PHONY : esp-idf/esp_https_server/all

# Recursive "preinstall" directory target.
esp-idf/esp_https_server/preinstall:
.PHONY : esp-idf/esp_https_server/preinstall

# Recursive "clean" directory target.
esp-idf/esp_https_server/clean: esp-idf/esp_https_server/CMakeFiles/__idf_esp_https_server.dir/clean
.PHONY : esp-idf/esp_https_server/clean

#=============================================================================
# Directory level rules for directory esp-idf/esp_hw_support

# Recursive "all" directory target.
esp-idf/esp_hw_support/all: esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/all
esp-idf/esp_hw_support/all: esp-idf/esp_hw_support/port/esp32s3/all
esp-idf/esp_hw_support/all: esp-idf/esp_hw_support/lowpower/all
.PHONY : esp-idf/esp_hw_support/all

# Recursive "preinstall" directory target.
esp-idf/esp_hw_support/preinstall: esp-idf/esp_hw_support/port/esp32s3/preinstall
esp-idf/esp_hw_support/preinstall: esp-idf/esp_hw_support/lowpower/preinstall
.PHONY : esp-idf/esp_hw_support/preinstall

# Recursive "clean" directory target.
esp-idf/esp_hw_support/clean: esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/clean
esp-idf/esp_hw_support/clean: esp-idf/esp_hw_support/port/esp32s3/clean
esp-idf/esp_hw_support/clean: esp-idf/esp_hw_support/lowpower/clean
.PHONY : esp-idf/esp_hw_support/clean

#=============================================================================
# Directory level rules for directory esp-idf/esp_hw_support/lowpower

# Recursive "all" directory target.
esp-idf/esp_hw_support/lowpower/all:
.PHONY : esp-idf/esp_hw_support/lowpower/all

# Recursive "preinstall" directory target.
esp-idf/esp_hw_support/lowpower/preinstall:
.PHONY : esp-idf/esp_hw_support/lowpower/preinstall

# Recursive "clean" directory target.
esp-idf/esp_hw_support/lowpower/clean:
.PHONY : esp-idf/esp_hw_support/lowpower/clean

#=============================================================================
# Directory level rules for directory esp-idf/esp_hw_support/port/esp32s3

# Recursive "all" directory target.
esp-idf/esp_hw_support/port/esp32s3/all:
.PHONY : esp-idf/esp_hw_support/port/esp32s3/all

# Recursive "preinstall" directory target.
esp-idf/esp_hw_support/port/esp32s3/preinstall:
.PHONY : esp-idf/esp_hw_support/port/esp32s3/preinstall

# Recursive "clean" directory target.
esp-idf/esp_hw_support/port/esp32s3/clean:
.PHONY : esp-idf/esp_hw_support/port/esp32s3/clean

#=============================================================================
# Directory level rules for directory esp-idf/esp_lcd

# Recursive "all" directory target.
esp-idf/esp_lcd/all: esp-idf/esp_lcd/CMakeFiles/__idf_esp_lcd.dir/all
.PHONY : esp-idf/esp_lcd/all

# Recursive "preinstall" directory target.
esp-idf/esp_lcd/preinstall:
.PHONY : esp-idf/esp_lcd/preinstall

# Recursive "clean" directory target.
esp-idf/esp_lcd/clean: esp-idf/esp_lcd/CMakeFiles/__idf_esp_lcd.dir/clean
.PHONY : esp-idf/esp_lcd/clean

#=============================================================================
# Directory level rules for directory esp-idf/esp_local_ctrl

# Recursive "all" directory target.
esp-idf/esp_local_ctrl/all: esp-idf/esp_local_ctrl/CMakeFiles/__idf_esp_local_ctrl.dir/all
.PHONY : esp-idf/esp_local_ctrl/all

# Recursive "preinstall" directory target.
esp-idf/esp_local_ctrl/preinstall:
.PHONY : esp-idf/esp_local_ctrl/preinstall

# Recursive "clean" directory target.
esp-idf/esp_local_ctrl/clean: esp-idf/esp_local_ctrl/CMakeFiles/__idf_esp_local_ctrl.dir/clean
.PHONY : esp-idf/esp_local_ctrl/clean

#=============================================================================
# Directory level rules for directory esp-idf/esp_mm

# Recursive "all" directory target.
esp-idf/esp_mm/all: esp-idf/esp_mm/CMakeFiles/__idf_esp_mm.dir/all
.PHONY : esp-idf/esp_mm/all

# Recursive "preinstall" directory target.
esp-idf/esp_mm/preinstall:
.PHONY : esp-idf/esp_mm/preinstall

# Recursive "clean" directory target.
esp-idf/esp_mm/clean: esp-idf/esp_mm/CMakeFiles/__idf_esp_mm.dir/clean
.PHONY : esp-idf/esp_mm/clean

#=============================================================================
# Directory level rules for directory esp-idf/esp_netif

# Recursive "all" directory target.
esp-idf/esp_netif/all: esp-idf/esp_netif/CMakeFiles/__idf_esp_netif.dir/all
.PHONY : esp-idf/esp_netif/all

# Recursive "preinstall" directory target.
esp-idf/esp_netif/preinstall:
.PHONY : esp-idf/esp_netif/preinstall

# Recursive "clean" directory target.
esp-idf/esp_netif/clean: esp-idf/esp_netif/CMakeFiles/__idf_esp_netif.dir/clean
.PHONY : esp-idf/esp_netif/clean

#=============================================================================
# Directory level rules for directory esp-idf/esp_netif_stack

# Recursive "all" directory target.
esp-idf/esp_netif_stack/all:
.PHONY : esp-idf/esp_netif_stack/all

# Recursive "preinstall" directory target.
esp-idf/esp_netif_stack/preinstall:
.PHONY : esp-idf/esp_netif_stack/preinstall

# Recursive "clean" directory target.
esp-idf/esp_netif_stack/clean:
.PHONY : esp-idf/esp_netif_stack/clean

#=============================================================================
# Directory level rules for directory esp-idf/esp_partition

# Recursive "all" directory target.
esp-idf/esp_partition/all: esp-idf/esp_partition/CMakeFiles/__idf_esp_partition.dir/all
.PHONY : esp-idf/esp_partition/all

# Recursive "preinstall" directory target.
esp-idf/esp_partition/preinstall:
.PHONY : esp-idf/esp_partition/preinstall

# Recursive "clean" directory target.
esp-idf/esp_partition/clean: esp-idf/esp_partition/CMakeFiles/__idf_esp_partition.dir/clean
.PHONY : esp-idf/esp_partition/clean

#=============================================================================
# Directory level rules for directory esp-idf/esp_phy

# Recursive "all" directory target.
esp-idf/esp_phy/all: esp-idf/esp_phy/CMakeFiles/__idf_esp_phy.dir/all
.PHONY : esp-idf/esp_phy/all

# Recursive "preinstall" directory target.
esp-idf/esp_phy/preinstall:
.PHONY : esp-idf/esp_phy/preinstall

# Recursive "clean" directory target.
esp-idf/esp_phy/clean: esp-idf/esp_phy/CMakeFiles/__idf_esp_phy.dir/clean
.PHONY : esp-idf/esp_phy/clean

#=============================================================================
# Directory level rules for directory esp-idf/esp_pm

# Recursive "all" directory target.
esp-idf/esp_pm/all: esp-idf/esp_pm/CMakeFiles/__idf_esp_pm.dir/all
.PHONY : esp-idf/esp_pm/all

# Recursive "preinstall" directory target.
esp-idf/esp_pm/preinstall:
.PHONY : esp-idf/esp_pm/preinstall

# Recursive "clean" directory target.
esp-idf/esp_pm/clean: esp-idf/esp_pm/CMakeFiles/__idf_esp_pm.dir/clean
.PHONY : esp-idf/esp_pm/clean

#=============================================================================
# Directory level rules for directory esp-idf/esp_psram

# Recursive "all" directory target.
esp-idf/esp_psram/all: esp-idf/esp_psram/CMakeFiles/__idf_esp_psram.dir/all
.PHONY : esp-idf/esp_psram/all

# Recursive "preinstall" directory target.
esp-idf/esp_psram/preinstall:
.PHONY : esp-idf/esp_psram/preinstall

# Recursive "clean" directory target.
esp-idf/esp_psram/clean: esp-idf/esp_psram/CMakeFiles/__idf_esp_psram.dir/clean
.PHONY : esp-idf/esp_psram/clean

#=============================================================================
# Directory level rules for directory esp-idf/esp_ringbuf

# Recursive "all" directory target.
esp-idf/esp_ringbuf/all: esp-idf/esp_ringbuf/CMakeFiles/__idf_esp_ringbuf.dir/all
.PHONY : esp-idf/esp_ringbuf/all

# Recursive "preinstall" directory target.
esp-idf/esp_ringbuf/preinstall:
.PHONY : esp-idf/esp_ringbuf/preinstall

# Recursive "clean" directory target.
esp-idf/esp_ringbuf/clean: esp-idf/esp_ringbuf/CMakeFiles/__idf_esp_ringbuf.dir/clean
.PHONY : esp-idf/esp_ringbuf/clean

#=============================================================================
# Directory level rules for directory esp-idf/esp_rom

# Recursive "all" directory target.
esp-idf/esp_rom/all: esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/all
.PHONY : esp-idf/esp_rom/all

# Recursive "preinstall" directory target.
esp-idf/esp_rom/preinstall:
.PHONY : esp-idf/esp_rom/preinstall

# Recursive "clean" directory target.
esp-idf/esp_rom/clean: esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/clean
.PHONY : esp-idf/esp_rom/clean

#=============================================================================
# Directory level rules for directory esp-idf/esp_system

# Recursive "all" directory target.
esp-idf/esp_system/all: esp-idf/esp_system/CMakeFiles/__idf_esp_system.dir/all
esp-idf/esp_system/all: esp-idf/esp_system/port/all
.PHONY : esp-idf/esp_system/all

# Recursive "preinstall" directory target.
esp-idf/esp_system/preinstall: esp-idf/esp_system/port/preinstall
.PHONY : esp-idf/esp_system/preinstall

# Recursive "clean" directory target.
esp-idf/esp_system/clean: esp-idf/esp_system/CMakeFiles/__idf_esp_system.dir/clean
esp-idf/esp_system/clean: esp-idf/esp_system/CMakeFiles/memory.ld.dir/clean
esp-idf/esp_system/clean: esp-idf/esp_system/CMakeFiles/sections.ld.in.dir/clean
esp-idf/esp_system/clean: esp-idf/esp_system/CMakeFiles/__ldgen_output_sections.ld.dir/clean
esp-idf/esp_system/clean: esp-idf/esp_system/port/clean
	$(CMAKE_COMMAND) -P esp-idf/esp_system/CMakeFiles/cmake_directory_clean.cmake
.PHONY : esp-idf/esp_system/clean

#=============================================================================
# Directory level rules for directory esp-idf/esp_system/port

# Recursive "all" directory target.
esp-idf/esp_system/port/all: esp-idf/esp_system/port/soc/esp32s3/all
.PHONY : esp-idf/esp_system/port/all

# Recursive "preinstall" directory target.
esp-idf/esp_system/port/preinstall: esp-idf/esp_system/port/soc/esp32s3/preinstall
.PHONY : esp-idf/esp_system/port/preinstall

# Recursive "clean" directory target.
esp-idf/esp_system/port/clean: esp-idf/esp_system/port/soc/esp32s3/clean
.PHONY : esp-idf/esp_system/port/clean

#=============================================================================
# Directory level rules for directory esp-idf/esp_system/port/soc/esp32s3

# Recursive "all" directory target.
esp-idf/esp_system/port/soc/esp32s3/all:
.PHONY : esp-idf/esp_system/port/soc/esp32s3/all

# Recursive "preinstall" directory target.
esp-idf/esp_system/port/soc/esp32s3/preinstall:
.PHONY : esp-idf/esp_system/port/soc/esp32s3/preinstall

# Recursive "clean" directory target.
esp-idf/esp_system/port/soc/esp32s3/clean:
.PHONY : esp-idf/esp_system/port/soc/esp32s3/clean

#=============================================================================
# Directory level rules for directory esp-idf/esp_timer

# Recursive "all" directory target.
esp-idf/esp_timer/all: esp-idf/esp_timer/CMakeFiles/__idf_esp_timer.dir/all
.PHONY : esp-idf/esp_timer/all

# Recursive "preinstall" directory target.
esp-idf/esp_timer/preinstall:
.PHONY : esp-idf/esp_timer/preinstall

# Recursive "clean" directory target.
esp-idf/esp_timer/clean: esp-idf/esp_timer/CMakeFiles/__idf_esp_timer.dir/clean
.PHONY : esp-idf/esp_timer/clean

#=============================================================================
# Directory level rules for directory esp-idf/esp_vfs_console

# Recursive "all" directory target.
esp-idf/esp_vfs_console/all: esp-idf/esp_vfs_console/CMakeFiles/__idf_esp_vfs_console.dir/all
.PHONY : esp-idf/esp_vfs_console/all

# Recursive "preinstall" directory target.
esp-idf/esp_vfs_console/preinstall:
.PHONY : esp-idf/esp_vfs_console/preinstall

# Recursive "clean" directory target.
esp-idf/esp_vfs_console/clean: esp-idf/esp_vfs_console/CMakeFiles/__idf_esp_vfs_console.dir/clean
.PHONY : esp-idf/esp_vfs_console/clean

#=============================================================================
# Directory level rules for directory esp-idf/esp_wifi

# Recursive "all" directory target.
esp-idf/esp_wifi/all: esp-idf/esp_wifi/CMakeFiles/__idf_esp_wifi.dir/all
.PHONY : esp-idf/esp_wifi/all

# Recursive "preinstall" directory target.
esp-idf/esp_wifi/preinstall:
.PHONY : esp-idf/esp_wifi/preinstall

# Recursive "clean" directory target.
esp-idf/esp_wifi/clean: esp-idf/esp_wifi/CMakeFiles/__idf_esp_wifi.dir/clean
.PHONY : esp-idf/esp_wifi/clean

#=============================================================================
# Directory level rules for directory esp-idf/espcoredump

# Recursive "all" directory target.
esp-idf/espcoredump/all: esp-idf/espcoredump/CMakeFiles/__idf_espcoredump.dir/all
.PHONY : esp-idf/espcoredump/all

# Recursive "preinstall" directory target.
esp-idf/espcoredump/preinstall:
.PHONY : esp-idf/espcoredump/preinstall

# Recursive "clean" directory target.
esp-idf/espcoredump/clean: esp-idf/espcoredump/CMakeFiles/__idf_espcoredump.dir/clean
.PHONY : esp-idf/espcoredump/clean

#=============================================================================
# Directory level rules for directory esp-idf/espressif__button

# Recursive "all" directory target.
esp-idf/espressif__button/all: esp-idf/espressif__button/CMakeFiles/__idf_espressif__button.dir/all
.PHONY : esp-idf/espressif__button/all

# Recursive "preinstall" directory target.
esp-idf/espressif__button/preinstall:
.PHONY : esp-idf/espressif__button/preinstall

# Recursive "clean" directory target.
esp-idf/espressif__button/clean: esp-idf/espressif__button/CMakeFiles/__idf_espressif__button.dir/clean
.PHONY : esp-idf/espressif__button/clean

#=============================================================================
# Directory level rules for directory esp-idf/espressif__cmake_utilities

# Recursive "all" directory target.
esp-idf/espressif__cmake_utilities/all:
.PHONY : esp-idf/espressif__cmake_utilities/all

# Recursive "preinstall" directory target.
esp-idf/espressif__cmake_utilities/preinstall:
.PHONY : esp-idf/espressif__cmake_utilities/preinstall

# Recursive "clean" directory target.
esp-idf/espressif__cmake_utilities/clean:
.PHONY : esp-idf/espressif__cmake_utilities/clean

#=============================================================================
# Directory level rules for directory esp-idf/espressif__esp-dsp

# Recursive "all" directory target.
esp-idf/espressif__esp-dsp/all: esp-idf/espressif__esp-dsp/CMakeFiles/__idf_espressif__esp-dsp.dir/all
.PHONY : esp-idf/espressif__esp-dsp/all

# Recursive "preinstall" directory target.
esp-idf/espressif__esp-dsp/preinstall:
.PHONY : esp-idf/espressif__esp-dsp/preinstall

# Recursive "clean" directory target.
esp-idf/espressif__esp-dsp/clean: esp-idf/espressif__esp-dsp/CMakeFiles/__idf_espressif__esp-dsp.dir/clean
.PHONY : esp-idf/espressif__esp-dsp/clean

#=============================================================================
# Directory level rules for directory esp-idf/espressif__esp-sr

# Recursive "all" directory target.
esp-idf/espressif__esp-sr/all: esp-idf/espressif__esp-sr/CMakeFiles/__idf_espressif__esp-sr.dir/all
esp-idf/espressif__esp-sr/all: esp-idf/espressif__esp-sr/CMakeFiles/srmodels_bin.dir/all
.PHONY : esp-idf/espressif__esp-sr/all

# Recursive "preinstall" directory target.
esp-idf/espressif__esp-sr/preinstall:
.PHONY : esp-idf/espressif__esp-sr/preinstall

# Recursive "clean" directory target.
esp-idf/espressif__esp-sr/clean: esp-idf/espressif__esp-sr/CMakeFiles/__idf_espressif__esp-sr.dir/clean
esp-idf/espressif__esp-sr/clean: esp-idf/espressif__esp-sr/CMakeFiles/srmodels_bin.dir/clean
.PHONY : esp-idf/espressif__esp-sr/clean

#=============================================================================
# Directory level rules for directory esp-idf/espressif__esp_codec_dev

# Recursive "all" directory target.
esp-idf/espressif__esp_codec_dev/all: esp-idf/espressif__esp_codec_dev/CMakeFiles/__idf_espressif__esp_codec_dev.dir/all
.PHONY : esp-idf/espressif__esp_codec_dev/all

# Recursive "preinstall" directory target.
esp-idf/espressif__esp_codec_dev/preinstall:
.PHONY : esp-idf/espressif__esp_codec_dev/preinstall

# Recursive "clean" directory target.
esp-idf/espressif__esp_codec_dev/clean: esp-idf/espressif__esp_codec_dev/CMakeFiles/__idf_espressif__esp_codec_dev.dir/clean
.PHONY : esp-idf/espressif__esp_codec_dev/clean

#=============================================================================
# Directory level rules for directory esp-idf/espressif__esp_lvgl_port

# Recursive "all" directory target.
esp-idf/espressif__esp_lvgl_port/all: esp-idf/espressif__esp_lvgl_port/CMakeFiles/lvgl_port_lib.dir/all
.PHONY : esp-idf/espressif__esp_lvgl_port/all

# Recursive "preinstall" directory target.
esp-idf/espressif__esp_lvgl_port/preinstall:
.PHONY : esp-idf/espressif__esp_lvgl_port/preinstall

# Recursive "clean" directory target.
esp-idf/espressif__esp_lvgl_port/clean: esp-idf/espressif__esp_lvgl_port/CMakeFiles/lvgl_port_lib.dir/clean
.PHONY : esp-idf/espressif__esp_lvgl_port/clean

#=============================================================================
# Directory level rules for directory esp-idf/espressif__led_strip

# Recursive "all" directory target.
esp-idf/espressif__led_strip/all: esp-idf/espressif__led_strip/CMakeFiles/__idf_espressif__led_strip.dir/all
.PHONY : esp-idf/espressif__led_strip/all

# Recursive "preinstall" directory target.
esp-idf/espressif__led_strip/preinstall:
.PHONY : esp-idf/espressif__led_strip/preinstall

# Recursive "clean" directory target.
esp-idf/espressif__led_strip/clean: esp-idf/espressif__led_strip/CMakeFiles/__idf_espressif__led_strip.dir/clean
.PHONY : esp-idf/espressif__led_strip/clean

#=============================================================================
# Directory level rules for directory esp-idf/esptool_py

# Recursive "all" directory target.
esp-idf/esptool_py/all:
.PHONY : esp-idf/esptool_py/all

# Recursive "preinstall" directory target.
esp-idf/esptool_py/preinstall:
.PHONY : esp-idf/esptool_py/preinstall

# Recursive "clean" directory target.
esp-idf/esptool_py/clean: esp-idf/esptool_py/CMakeFiles/app-flash.dir/clean
esp-idf/esptool_py/clean: esp-idf/esptool_py/CMakeFiles/encrypted-app-flash.dir/clean
esp-idf/esptool_py/clean: esp-idf/esptool_py/CMakeFiles/app_check_size.dir/clean
	$(CMAKE_COMMAND) -P esp-idf/esptool_py/CMakeFiles/cmake_directory_clean.cmake
.PHONY : esp-idf/esptool_py/clean

#=============================================================================
# Directory level rules for directory esp-idf/fatfs

# Recursive "all" directory target.
esp-idf/fatfs/all: esp-idf/fatfs/CMakeFiles/__idf_fatfs.dir/all
.PHONY : esp-idf/fatfs/all

# Recursive "preinstall" directory target.
esp-idf/fatfs/preinstall:
.PHONY : esp-idf/fatfs/preinstall

# Recursive "clean" directory target.
esp-idf/fatfs/clean: esp-idf/fatfs/CMakeFiles/__idf_fatfs.dir/clean
.PHONY : esp-idf/fatfs/clean

#=============================================================================
# Directory level rules for directory esp-idf/freertos

# Recursive "all" directory target.
esp-idf/freertos/all: esp-idf/freertos/CMakeFiles/__idf_freertos.dir/all
.PHONY : esp-idf/freertos/all

# Recursive "preinstall" directory target.
esp-idf/freertos/preinstall:
.PHONY : esp-idf/freertos/preinstall

# Recursive "clean" directory target.
esp-idf/freertos/clean: esp-idf/freertos/CMakeFiles/__idf_freertos.dir/clean
.PHONY : esp-idf/freertos/clean

#=============================================================================
# Directory level rules for directory esp-idf/hal

# Recursive "all" directory target.
esp-idf/hal/all: esp-idf/hal/CMakeFiles/__idf_hal.dir/all
.PHONY : esp-idf/hal/all

# Recursive "preinstall" directory target.
esp-idf/hal/preinstall:
.PHONY : esp-idf/hal/preinstall

# Recursive "clean" directory target.
esp-idf/hal/clean: esp-idf/hal/CMakeFiles/__idf_hal.dir/clean
.PHONY : esp-idf/hal/clean

#=============================================================================
# Directory level rules for directory esp-idf/heap

# Recursive "all" directory target.
esp-idf/heap/all: esp-idf/heap/CMakeFiles/__idf_heap.dir/all
.PHONY : esp-idf/heap/all

# Recursive "preinstall" directory target.
esp-idf/heap/preinstall:
.PHONY : esp-idf/heap/preinstall

# Recursive "clean" directory target.
esp-idf/heap/clean: esp-idf/heap/CMakeFiles/__idf_heap.dir/clean
.PHONY : esp-idf/heap/clean

#=============================================================================
# Directory level rules for directory esp-idf/http_parser

# Recursive "all" directory target.
esp-idf/http_parser/all: esp-idf/http_parser/CMakeFiles/__idf_http_parser.dir/all
.PHONY : esp-idf/http_parser/all

# Recursive "preinstall" directory target.
esp-idf/http_parser/preinstall:
.PHONY : esp-idf/http_parser/preinstall

# Recursive "clean" directory target.
esp-idf/http_parser/clean: esp-idf/http_parser/CMakeFiles/__idf_http_parser.dir/clean
.PHONY : esp-idf/http_parser/clean

#=============================================================================
# Directory level rules for directory esp-idf/idf_test

# Recursive "all" directory target.
esp-idf/idf_test/all:
.PHONY : esp-idf/idf_test/all

# Recursive "preinstall" directory target.
esp-idf/idf_test/preinstall:
.PHONY : esp-idf/idf_test/preinstall

# Recursive "clean" directory target.
esp-idf/idf_test/clean:
.PHONY : esp-idf/idf_test/clean

#=============================================================================
# Directory level rules for directory esp-idf/ieee802154

# Recursive "all" directory target.
esp-idf/ieee802154/all:
.PHONY : esp-idf/ieee802154/all

# Recursive "preinstall" directory target.
esp-idf/ieee802154/preinstall:
.PHONY : esp-idf/ieee802154/preinstall

# Recursive "clean" directory target.
esp-idf/ieee802154/clean:
.PHONY : esp-idf/ieee802154/clean

#=============================================================================
# Directory level rules for directory esp-idf/json

# Recursive "all" directory target.
esp-idf/json/all: esp-idf/json/CMakeFiles/__idf_json.dir/all
.PHONY : esp-idf/json/all

# Recursive "preinstall" directory target.
esp-idf/json/preinstall:
.PHONY : esp-idf/json/preinstall

# Recursive "clean" directory target.
esp-idf/json/clean: esp-idf/json/CMakeFiles/__idf_json.dir/clean
.PHONY : esp-idf/json/clean

#=============================================================================
# Directory level rules for directory esp-idf/log

# Recursive "all" directory target.
esp-idf/log/all: esp-idf/log/CMakeFiles/__idf_log.dir/all
.PHONY : esp-idf/log/all

# Recursive "preinstall" directory target.
esp-idf/log/preinstall:
.PHONY : esp-idf/log/preinstall

# Recursive "clean" directory target.
esp-idf/log/clean: esp-idf/log/CMakeFiles/__idf_log.dir/clean
.PHONY : esp-idf/log/clean

#=============================================================================
# Directory level rules for directory esp-idf/lvgl__lvgl

# Recursive "all" directory target.
esp-idf/lvgl__lvgl/all: esp-idf/lvgl__lvgl/CMakeFiles/__idf_lvgl__lvgl.dir/all
.PHONY : esp-idf/lvgl__lvgl/all

# Recursive "preinstall" directory target.
esp-idf/lvgl__lvgl/preinstall:
.PHONY : esp-idf/lvgl__lvgl/preinstall

# Recursive "clean" directory target.
esp-idf/lvgl__lvgl/clean: esp-idf/lvgl__lvgl/CMakeFiles/__idf_lvgl__lvgl.dir/clean
.PHONY : esp-idf/lvgl__lvgl/clean

#=============================================================================
# Directory level rules for directory esp-idf/lwip

# Recursive "all" directory target.
esp-idf/lwip/all: esp-idf/lwip/CMakeFiles/__idf_lwip.dir/all
.PHONY : esp-idf/lwip/all

# Recursive "preinstall" directory target.
esp-idf/lwip/preinstall:
.PHONY : esp-idf/lwip/preinstall

# Recursive "clean" directory target.
esp-idf/lwip/clean: esp-idf/lwip/CMakeFiles/__idf_lwip.dir/clean
.PHONY : esp-idf/lwip/clean

#=============================================================================
# Directory level rules for directory esp-idf/main

# Recursive "all" directory target.
esp-idf/main/all: esp-idf/main/CMakeFiles/__idf_main.dir/all
.PHONY : esp-idf/main/all

# Recursive "preinstall" directory target.
esp-idf/main/preinstall:
.PHONY : esp-idf/main/preinstall

# Recursive "clean" directory target.
esp-idf/main/clean: esp-idf/main/CMakeFiles/__idf_main.dir/clean
	$(CMAKE_COMMAND) -P esp-idf/main/CMakeFiles/cmake_directory_clean.cmake
.PHONY : esp-idf/main/clean

#=============================================================================
# Directory level rules for directory esp-idf/mbedtls

# Recursive "all" directory target.
esp-idf/mbedtls/all: esp-idf/mbedtls/CMakeFiles/__idf_mbedtls.dir/all
esp-idf/mbedtls/all: esp-idf/mbedtls/mbedtls/all
.PHONY : esp-idf/mbedtls/all

# Recursive "preinstall" directory target.
esp-idf/mbedtls/preinstall: esp-idf/mbedtls/mbedtls/preinstall
.PHONY : esp-idf/mbedtls/preinstall

# Recursive "clean" directory target.
esp-idf/mbedtls/clean: esp-idf/mbedtls/CMakeFiles/__idf_mbedtls.dir/clean
esp-idf/mbedtls/clean: esp-idf/mbedtls/CMakeFiles/custom_bundle.dir/clean
esp-idf/mbedtls/clean: esp-idf/mbedtls/mbedtls/clean
	$(CMAKE_COMMAND) -P esp-idf/mbedtls/CMakeFiles/cmake_directory_clean.cmake
.PHONY : esp-idf/mbedtls/clean

#=============================================================================
# Directory level rules for directory esp-idf/mbedtls/mbedtls

# Recursive "all" directory target.
esp-idf/mbedtls/mbedtls/all: esp-idf/mbedtls/mbedtls/include/all
esp-idf/mbedtls/mbedtls/all: esp-idf/mbedtls/mbedtls/3rdparty/all
esp-idf/mbedtls/mbedtls/all: esp-idf/mbedtls/mbedtls/library/all
esp-idf/mbedtls/mbedtls/all: esp-idf/mbedtls/mbedtls/pkgconfig/all
.PHONY : esp-idf/mbedtls/mbedtls/all

# Recursive "preinstall" directory target.
esp-idf/mbedtls/mbedtls/preinstall: esp-idf/mbedtls/mbedtls/include/preinstall
esp-idf/mbedtls/mbedtls/preinstall: esp-idf/mbedtls/mbedtls/3rdparty/preinstall
esp-idf/mbedtls/mbedtls/preinstall: esp-idf/mbedtls/mbedtls/library/preinstall
esp-idf/mbedtls/mbedtls/preinstall: esp-idf/mbedtls/mbedtls/pkgconfig/preinstall
.PHONY : esp-idf/mbedtls/mbedtls/preinstall

# Recursive "clean" directory target.
esp-idf/mbedtls/mbedtls/clean: esp-idf/mbedtls/mbedtls/CMakeFiles/apidoc.dir/clean
esp-idf/mbedtls/mbedtls/clean: esp-idf/mbedtls/mbedtls/include/clean
esp-idf/mbedtls/mbedtls/clean: esp-idf/mbedtls/mbedtls/3rdparty/clean
esp-idf/mbedtls/mbedtls/clean: esp-idf/mbedtls/mbedtls/library/clean
esp-idf/mbedtls/mbedtls/clean: esp-idf/mbedtls/mbedtls/pkgconfig/clean
.PHONY : esp-idf/mbedtls/mbedtls/clean

#=============================================================================
# Directory level rules for directory esp-idf/mbedtls/mbedtls/3rdparty

# Recursive "all" directory target.
esp-idf/mbedtls/mbedtls/3rdparty/all: esp-idf/mbedtls/mbedtls/3rdparty/everest/all
esp-idf/mbedtls/mbedtls/3rdparty/all: esp-idf/mbedtls/mbedtls/3rdparty/p256-m/all
.PHONY : esp-idf/mbedtls/mbedtls/3rdparty/all

# Recursive "preinstall" directory target.
esp-idf/mbedtls/mbedtls/3rdparty/preinstall: esp-idf/mbedtls/mbedtls/3rdparty/everest/preinstall
esp-idf/mbedtls/mbedtls/3rdparty/preinstall: esp-idf/mbedtls/mbedtls/3rdparty/p256-m/preinstall
.PHONY : esp-idf/mbedtls/mbedtls/3rdparty/preinstall

# Recursive "clean" directory target.
esp-idf/mbedtls/mbedtls/3rdparty/clean: esp-idf/mbedtls/mbedtls/3rdparty/everest/clean
esp-idf/mbedtls/mbedtls/3rdparty/clean: esp-idf/mbedtls/mbedtls/3rdparty/p256-m/clean
.PHONY : esp-idf/mbedtls/mbedtls/3rdparty/clean

#=============================================================================
# Directory level rules for directory esp-idf/mbedtls/mbedtls/3rdparty/everest

# Recursive "all" directory target.
esp-idf/mbedtls/mbedtls/3rdparty/everest/all: esp-idf/mbedtls/mbedtls/3rdparty/everest/CMakeFiles/everest.dir/all
.PHONY : esp-idf/mbedtls/mbedtls/3rdparty/everest/all

# Recursive "preinstall" directory target.
esp-idf/mbedtls/mbedtls/3rdparty/everest/preinstall:
.PHONY : esp-idf/mbedtls/mbedtls/3rdparty/everest/preinstall

# Recursive "clean" directory target.
esp-idf/mbedtls/mbedtls/3rdparty/everest/clean: esp-idf/mbedtls/mbedtls/3rdparty/everest/CMakeFiles/everest.dir/clean
.PHONY : esp-idf/mbedtls/mbedtls/3rdparty/everest/clean

#=============================================================================
# Directory level rules for directory esp-idf/mbedtls/mbedtls/3rdparty/p256-m

# Recursive "all" directory target.
esp-idf/mbedtls/mbedtls/3rdparty/p256-m/all: esp-idf/mbedtls/mbedtls/3rdparty/p256-m/CMakeFiles/p256m.dir/all
.PHONY : esp-idf/mbedtls/mbedtls/3rdparty/p256-m/all

# Recursive "preinstall" directory target.
esp-idf/mbedtls/mbedtls/3rdparty/p256-m/preinstall:
.PHONY : esp-idf/mbedtls/mbedtls/3rdparty/p256-m/preinstall

# Recursive "clean" directory target.
esp-idf/mbedtls/mbedtls/3rdparty/p256-m/clean: esp-idf/mbedtls/mbedtls/3rdparty/p256-m/CMakeFiles/p256m.dir/clean
.PHONY : esp-idf/mbedtls/mbedtls/3rdparty/p256-m/clean

#=============================================================================
# Directory level rules for directory esp-idf/mbedtls/mbedtls/include

# Recursive "all" directory target.
esp-idf/mbedtls/mbedtls/include/all:
.PHONY : esp-idf/mbedtls/mbedtls/include/all

# Recursive "preinstall" directory target.
esp-idf/mbedtls/mbedtls/include/preinstall:
.PHONY : esp-idf/mbedtls/mbedtls/include/preinstall

# Recursive "clean" directory target.
esp-idf/mbedtls/mbedtls/include/clean:
.PHONY : esp-idf/mbedtls/mbedtls/include/clean

#=============================================================================
# Directory level rules for directory esp-idf/mbedtls/mbedtls/library

# Recursive "all" directory target.
esp-idf/mbedtls/mbedtls/library/all: esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/all
esp-idf/mbedtls/mbedtls/library/all: esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedx509.dir/all
esp-idf/mbedtls/mbedtls/library/all: esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedtls.dir/all
.PHONY : esp-idf/mbedtls/mbedtls/library/all

# Recursive "preinstall" directory target.
esp-idf/mbedtls/mbedtls/library/preinstall:
.PHONY : esp-idf/mbedtls/mbedtls/library/preinstall

# Recursive "clean" directory target.
esp-idf/mbedtls/mbedtls/library/clean: esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/clean
esp-idf/mbedtls/mbedtls/library/clean: esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedx509.dir/clean
esp-idf/mbedtls/mbedtls/library/clean: esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedtls.dir/clean
esp-idf/mbedtls/mbedtls/library/clean: esp-idf/mbedtls/mbedtls/library/CMakeFiles/lib.dir/clean
.PHONY : esp-idf/mbedtls/mbedtls/library/clean

#=============================================================================
# Directory level rules for directory esp-idf/mbedtls/mbedtls/pkgconfig

# Recursive "all" directory target.
esp-idf/mbedtls/mbedtls/pkgconfig/all:
.PHONY : esp-idf/mbedtls/mbedtls/pkgconfig/all

# Recursive "preinstall" directory target.
esp-idf/mbedtls/mbedtls/pkgconfig/preinstall:
.PHONY : esp-idf/mbedtls/mbedtls/pkgconfig/preinstall

# Recursive "clean" directory target.
esp-idf/mbedtls/mbedtls/pkgconfig/clean:
.PHONY : esp-idf/mbedtls/mbedtls/pkgconfig/clean

#=============================================================================
# Directory level rules for directory esp-idf/mqtt

# Recursive "all" directory target.
esp-idf/mqtt/all: esp-idf/mqtt/CMakeFiles/__idf_mqtt.dir/all
.PHONY : esp-idf/mqtt/all

# Recursive "preinstall" directory target.
esp-idf/mqtt/preinstall:
.PHONY : esp-idf/mqtt/preinstall

# Recursive "clean" directory target.
esp-idf/mqtt/clean: esp-idf/mqtt/CMakeFiles/__idf_mqtt.dir/clean
.PHONY : esp-idf/mqtt/clean

#=============================================================================
# Directory level rules for directory esp-idf/newlib

# Recursive "all" directory target.
esp-idf/newlib/all: esp-idf/newlib/CMakeFiles/__idf_newlib.dir/all
esp-idf/newlib/all: esp-idf/newlib/port/all
.PHONY : esp-idf/newlib/all

# Recursive "preinstall" directory target.
esp-idf/newlib/preinstall: esp-idf/newlib/port/preinstall
.PHONY : esp-idf/newlib/preinstall

# Recursive "clean" directory target.
esp-idf/newlib/clean: esp-idf/newlib/CMakeFiles/__idf_newlib.dir/clean
esp-idf/newlib/clean: esp-idf/newlib/port/clean
.PHONY : esp-idf/newlib/clean

#=============================================================================
# Directory level rules for directory esp-idf/newlib/port

# Recursive "all" directory target.
esp-idf/newlib/port/all:
.PHONY : esp-idf/newlib/port/all

# Recursive "preinstall" directory target.
esp-idf/newlib/port/preinstall:
.PHONY : esp-idf/newlib/port/preinstall

# Recursive "clean" directory target.
esp-idf/newlib/port/clean:
.PHONY : esp-idf/newlib/port/clean

#=============================================================================
# Directory level rules for directory esp-idf/nvs_flash

# Recursive "all" directory target.
esp-idf/nvs_flash/all: esp-idf/nvs_flash/CMakeFiles/__idf_nvs_flash.dir/all
.PHONY : esp-idf/nvs_flash/all

# Recursive "preinstall" directory target.
esp-idf/nvs_flash/preinstall:
.PHONY : esp-idf/nvs_flash/preinstall

# Recursive "clean" directory target.
esp-idf/nvs_flash/clean: esp-idf/nvs_flash/CMakeFiles/__idf_nvs_flash.dir/clean
.PHONY : esp-idf/nvs_flash/clean

#=============================================================================
# Directory level rules for directory esp-idf/nvs_sec_provider

# Recursive "all" directory target.
esp-idf/nvs_sec_provider/all: esp-idf/nvs_sec_provider/CMakeFiles/__idf_nvs_sec_provider.dir/all
.PHONY : esp-idf/nvs_sec_provider/all

# Recursive "preinstall" directory target.
esp-idf/nvs_sec_provider/preinstall:
.PHONY : esp-idf/nvs_sec_provider/preinstall

# Recursive "clean" directory target.
esp-idf/nvs_sec_provider/clean: esp-idf/nvs_sec_provider/CMakeFiles/__idf_nvs_sec_provider.dir/clean
.PHONY : esp-idf/nvs_sec_provider/clean

#=============================================================================
# Directory level rules for directory esp-idf/openthread

# Recursive "all" directory target.
esp-idf/openthread/all:
.PHONY : esp-idf/openthread/all

# Recursive "preinstall" directory target.
esp-idf/openthread/preinstall:
.PHONY : esp-idf/openthread/preinstall

# Recursive "clean" directory target.
esp-idf/openthread/clean:
.PHONY : esp-idf/openthread/clean

#=============================================================================
# Directory level rules for directory esp-idf/partition_table

# Recursive "all" directory target.
esp-idf/partition_table/all:
.PHONY : esp-idf/partition_table/all

# Recursive "preinstall" directory target.
esp-idf/partition_table/preinstall:
.PHONY : esp-idf/partition_table/preinstall

# Recursive "clean" directory target.
esp-idf/partition_table/clean: esp-idf/partition_table/CMakeFiles/partition_table_bin.dir/clean
esp-idf/partition_table/clean: esp-idf/partition_table/CMakeFiles/partition-table.dir/clean
esp-idf/partition_table/clean: esp-idf/partition_table/CMakeFiles/partition_table.dir/clean
esp-idf/partition_table/clean: esp-idf/partition_table/CMakeFiles/partition-table-flash.dir/clean
esp-idf/partition_table/clean: esp-idf/partition_table/CMakeFiles/encrypted-partition-table-flash.dir/clean
esp-idf/partition_table/clean: esp-idf/partition_table/CMakeFiles/partition_table-flash.dir/clean
.PHONY : esp-idf/partition_table/clean

#=============================================================================
# Directory level rules for directory esp-idf/perfmon

# Recursive "all" directory target.
esp-idf/perfmon/all: esp-idf/perfmon/CMakeFiles/__idf_perfmon.dir/all
.PHONY : esp-idf/perfmon/all

# Recursive "preinstall" directory target.
esp-idf/perfmon/preinstall:
.PHONY : esp-idf/perfmon/preinstall

# Recursive "clean" directory target.
esp-idf/perfmon/clean: esp-idf/perfmon/CMakeFiles/__idf_perfmon.dir/clean
.PHONY : esp-idf/perfmon/clean

#=============================================================================
# Directory level rules for directory esp-idf/protobuf-c

# Recursive "all" directory target.
esp-idf/protobuf-c/all: esp-idf/protobuf-c/CMakeFiles/__idf_protobuf-c.dir/all
.PHONY : esp-idf/protobuf-c/all

# Recursive "preinstall" directory target.
esp-idf/protobuf-c/preinstall:
.PHONY : esp-idf/protobuf-c/preinstall

# Recursive "clean" directory target.
esp-idf/protobuf-c/clean: esp-idf/protobuf-c/CMakeFiles/__idf_protobuf-c.dir/clean
.PHONY : esp-idf/protobuf-c/clean

#=============================================================================
# Directory level rules for directory esp-idf/protocomm

# Recursive "all" directory target.
esp-idf/protocomm/all: esp-idf/protocomm/CMakeFiles/__idf_protocomm.dir/all
.PHONY : esp-idf/protocomm/all

# Recursive "preinstall" directory target.
esp-idf/protocomm/preinstall:
.PHONY : esp-idf/protocomm/preinstall

# Recursive "clean" directory target.
esp-idf/protocomm/clean: esp-idf/protocomm/CMakeFiles/__idf_protocomm.dir/clean
.PHONY : esp-idf/protocomm/clean

#=============================================================================
# Directory level rules for directory esp-idf/pthread

# Recursive "all" directory target.
esp-idf/pthread/all: esp-idf/pthread/CMakeFiles/__idf_pthread.dir/all
.PHONY : esp-idf/pthread/all

# Recursive "preinstall" directory target.
esp-idf/pthread/preinstall:
.PHONY : esp-idf/pthread/preinstall

# Recursive "clean" directory target.
esp-idf/pthread/clean: esp-idf/pthread/CMakeFiles/__idf_pthread.dir/clean
.PHONY : esp-idf/pthread/clean

#=============================================================================
# Directory level rules for directory esp-idf/sdmmc

# Recursive "all" directory target.
esp-idf/sdmmc/all: esp-idf/sdmmc/CMakeFiles/__idf_sdmmc.dir/all
.PHONY : esp-idf/sdmmc/all

# Recursive "preinstall" directory target.
esp-idf/sdmmc/preinstall:
.PHONY : esp-idf/sdmmc/preinstall

# Recursive "clean" directory target.
esp-idf/sdmmc/clean: esp-idf/sdmmc/CMakeFiles/__idf_sdmmc.dir/clean
.PHONY : esp-idf/sdmmc/clean

#=============================================================================
# Directory level rules for directory esp-idf/soc

# Recursive "all" directory target.
esp-idf/soc/all: esp-idf/soc/CMakeFiles/__idf_soc.dir/all
.PHONY : esp-idf/soc/all

# Recursive "preinstall" directory target.
esp-idf/soc/preinstall:
.PHONY : esp-idf/soc/preinstall

# Recursive "clean" directory target.
esp-idf/soc/clean: esp-idf/soc/CMakeFiles/__idf_soc.dir/clean
.PHONY : esp-idf/soc/clean

#=============================================================================
# Directory level rules for directory esp-idf/spi_flash

# Recursive "all" directory target.
esp-idf/spi_flash/all: esp-idf/spi_flash/CMakeFiles/__idf_spi_flash.dir/all
.PHONY : esp-idf/spi_flash/all

# Recursive "preinstall" directory target.
esp-idf/spi_flash/preinstall:
.PHONY : esp-idf/spi_flash/preinstall

# Recursive "clean" directory target.
esp-idf/spi_flash/clean: esp-idf/spi_flash/CMakeFiles/__idf_spi_flash.dir/clean
.PHONY : esp-idf/spi_flash/clean

#=============================================================================
# Directory level rules for directory esp-idf/spiffs

# Recursive "all" directory target.
esp-idf/spiffs/all: esp-idf/spiffs/CMakeFiles/__idf_spiffs.dir/all
.PHONY : esp-idf/spiffs/all

# Recursive "preinstall" directory target.
esp-idf/spiffs/preinstall:
.PHONY : esp-idf/spiffs/preinstall

# Recursive "clean" directory target.
esp-idf/spiffs/clean: esp-idf/spiffs/CMakeFiles/__idf_spiffs.dir/clean
.PHONY : esp-idf/spiffs/clean

#=============================================================================
# Directory level rules for directory esp-idf/tcp_transport

# Recursive "all" directory target.
esp-idf/tcp_transport/all: esp-idf/tcp_transport/CMakeFiles/__idf_tcp_transport.dir/all
.PHONY : esp-idf/tcp_transport/all

# Recursive "preinstall" directory target.
esp-idf/tcp_transport/preinstall:
.PHONY : esp-idf/tcp_transport/preinstall

# Recursive "clean" directory target.
esp-idf/tcp_transport/clean: esp-idf/tcp_transport/CMakeFiles/__idf_tcp_transport.dir/clean
.PHONY : esp-idf/tcp_transport/clean

#=============================================================================
# Directory level rules for directory esp-idf/touch_element

# Recursive "all" directory target.
esp-idf/touch_element/all: esp-idf/touch_element/CMakeFiles/__idf_touch_element.dir/all
.PHONY : esp-idf/touch_element/all

# Recursive "preinstall" directory target.
esp-idf/touch_element/preinstall:
.PHONY : esp-idf/touch_element/preinstall

# Recursive "clean" directory target.
esp-idf/touch_element/clean: esp-idf/touch_element/CMakeFiles/__idf_touch_element.dir/clean
.PHONY : esp-idf/touch_element/clean

#=============================================================================
# Directory level rules for directory esp-idf/ulp

# Recursive "all" directory target.
esp-idf/ulp/all:
.PHONY : esp-idf/ulp/all

# Recursive "preinstall" directory target.
esp-idf/ulp/preinstall:
.PHONY : esp-idf/ulp/preinstall

# Recursive "clean" directory target.
esp-idf/ulp/clean:
.PHONY : esp-idf/ulp/clean

#=============================================================================
# Directory level rules for directory esp-idf/unity

# Recursive "all" directory target.
esp-idf/unity/all: esp-idf/unity/CMakeFiles/__idf_unity.dir/all
.PHONY : esp-idf/unity/all

# Recursive "preinstall" directory target.
esp-idf/unity/preinstall:
.PHONY : esp-idf/unity/preinstall

# Recursive "clean" directory target.
esp-idf/unity/clean: esp-idf/unity/CMakeFiles/__idf_unity.dir/clean
.PHONY : esp-idf/unity/clean

#=============================================================================
# Directory level rules for directory esp-idf/usb

# Recursive "all" directory target.
esp-idf/usb/all: esp-idf/usb/CMakeFiles/__idf_usb.dir/all
.PHONY : esp-idf/usb/all

# Recursive "preinstall" directory target.
esp-idf/usb/preinstall:
.PHONY : esp-idf/usb/preinstall

# Recursive "clean" directory target.
esp-idf/usb/clean: esp-idf/usb/CMakeFiles/__idf_usb.dir/clean
.PHONY : esp-idf/usb/clean

#=============================================================================
# Directory level rules for directory esp-idf/vfs

# Recursive "all" directory target.
esp-idf/vfs/all: esp-idf/vfs/CMakeFiles/__idf_vfs.dir/all
.PHONY : esp-idf/vfs/all

# Recursive "preinstall" directory target.
esp-idf/vfs/preinstall:
.PHONY : esp-idf/vfs/preinstall

# Recursive "clean" directory target.
esp-idf/vfs/clean: esp-idf/vfs/CMakeFiles/__idf_vfs.dir/clean
.PHONY : esp-idf/vfs/clean

#=============================================================================
# Directory level rules for directory esp-idf/wear_levelling

# Recursive "all" directory target.
esp-idf/wear_levelling/all: esp-idf/wear_levelling/CMakeFiles/__idf_wear_levelling.dir/all
.PHONY : esp-idf/wear_levelling/all

# Recursive "preinstall" directory target.
esp-idf/wear_levelling/preinstall:
.PHONY : esp-idf/wear_levelling/preinstall

# Recursive "clean" directory target.
esp-idf/wear_levelling/clean: esp-idf/wear_levelling/CMakeFiles/__idf_wear_levelling.dir/clean
.PHONY : esp-idf/wear_levelling/clean

#=============================================================================
# Directory level rules for directory esp-idf/wifi_provisioning

# Recursive "all" directory target.
esp-idf/wifi_provisioning/all: esp-idf/wifi_provisioning/CMakeFiles/__idf_wifi_provisioning.dir/all
.PHONY : esp-idf/wifi_provisioning/all

# Recursive "preinstall" directory target.
esp-idf/wifi_provisioning/preinstall:
.PHONY : esp-idf/wifi_provisioning/preinstall

# Recursive "clean" directory target.
esp-idf/wifi_provisioning/clean: esp-idf/wifi_provisioning/CMakeFiles/__idf_wifi_provisioning.dir/clean
.PHONY : esp-idf/wifi_provisioning/clean

#=============================================================================
# Directory level rules for directory esp-idf/wpa_supplicant

# Recursive "all" directory target.
esp-idf/wpa_supplicant/all: esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/all
.PHONY : esp-idf/wpa_supplicant/all

# Recursive "preinstall" directory target.
esp-idf/wpa_supplicant/preinstall:
.PHONY : esp-idf/wpa_supplicant/preinstall

# Recursive "clean" directory target.
esp-idf/wpa_supplicant/clean: esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/clean
.PHONY : esp-idf/wpa_supplicant/clean

#=============================================================================
# Directory level rules for directory esp-idf/xtensa

# Recursive "all" directory target.
esp-idf/xtensa/all: esp-idf/xtensa/CMakeFiles/__idf_xtensa.dir/all
.PHONY : esp-idf/xtensa/all

# Recursive "preinstall" directory target.
esp-idf/xtensa/preinstall:
.PHONY : esp-idf/xtensa/preinstall

# Recursive "clean" directory target.
esp-idf/xtensa/clean: esp-idf/xtensa/CMakeFiles/__idf_xtensa.dir/clean
.PHONY : esp-idf/xtensa/clean

#=============================================================================
# Target rules for target CMakeFiles/menuconfig.dir

# All Build rule for target.
CMakeFiles/menuconfig.dir/all:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/menuconfig.dir/build.make CMakeFiles/menuconfig.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/menuconfig.dir/build.make CMakeFiles/menuconfig.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles --progress-num= "Built target menuconfig"
.PHONY : CMakeFiles/menuconfig.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/menuconfig.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles 0
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/menuconfig.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles 0
.PHONY : CMakeFiles/menuconfig.dir/rule

# Convenience name for target.
menuconfig: CMakeFiles/menuconfig.dir/rule
.PHONY : menuconfig

# clean rule for target.
CMakeFiles/menuconfig.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/menuconfig.dir/build.make CMakeFiles/menuconfig.dir/clean
.PHONY : CMakeFiles/menuconfig.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/confserver.dir

# All Build rule for target.
CMakeFiles/confserver.dir/all:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/confserver.dir/build.make CMakeFiles/confserver.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/confserver.dir/build.make CMakeFiles/confserver.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles --progress-num= "Built target confserver"
.PHONY : CMakeFiles/confserver.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/confserver.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles 0
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/confserver.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles 0
.PHONY : CMakeFiles/confserver.dir/rule

# Convenience name for target.
confserver: CMakeFiles/confserver.dir/rule
.PHONY : confserver

# clean rule for target.
CMakeFiles/confserver.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/confserver.dir/build.make CMakeFiles/confserver.dir/clean
.PHONY : CMakeFiles/confserver.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/save-defconfig.dir

# All Build rule for target.
CMakeFiles/save-defconfig.dir/all:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/save-defconfig.dir/build.make CMakeFiles/save-defconfig.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/save-defconfig.dir/build.make CMakeFiles/save-defconfig.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles --progress-num= "Built target save-defconfig"
.PHONY : CMakeFiles/save-defconfig.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/save-defconfig.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles 0
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/save-defconfig.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles 0
.PHONY : CMakeFiles/save-defconfig.dir/rule

# Convenience name for target.
save-defconfig: CMakeFiles/save-defconfig.dir/rule
.PHONY : save-defconfig

# clean rule for target.
CMakeFiles/save-defconfig.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/save-defconfig.dir/build.make CMakeFiles/save-defconfig.dir/clean
.PHONY : CMakeFiles/save-defconfig.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/bootloader.dir

# All Build rule for target.
CMakeFiles/bootloader.dir/all: esp-idf/partition_table/CMakeFiles/partition_table_bin.dir/all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/bootloader.dir/build.make CMakeFiles/bootloader.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/bootloader.dir/build.make CMakeFiles/bootloader.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles --progress-num= "Built target bootloader"
.PHONY : CMakeFiles/bootloader.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/bootloader.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles 0
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/bootloader.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles 0
.PHONY : CMakeFiles/bootloader.dir/rule

# Convenience name for target.
bootloader: CMakeFiles/bootloader.dir/rule
.PHONY : bootloader

# clean rule for target.
CMakeFiles/bootloader.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/bootloader.dir/build.make CMakeFiles/bootloader.dir/clean
.PHONY : CMakeFiles/bootloader.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/gen_project_binary.dir

# All Build rule for target.
CMakeFiles/gen_project_binary.dir/all: CMakeFiles/xiaozhi.elf.dir/all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/gen_project_binary.dir/build.make CMakeFiles/gen_project_binary.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/gen_project_binary.dir/build.make CMakeFiles/gen_project_binary.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles --progress-num= "Built target gen_project_binary"
.PHONY : CMakeFiles/gen_project_binary.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/gen_project_binary.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles 100
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/gen_project_binary.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles 0
.PHONY : CMakeFiles/gen_project_binary.dir/rule

# Convenience name for target.
gen_project_binary: CMakeFiles/gen_project_binary.dir/rule
.PHONY : gen_project_binary

# clean rule for target.
CMakeFiles/gen_project_binary.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/gen_project_binary.dir/build.make CMakeFiles/gen_project_binary.dir/clean
.PHONY : CMakeFiles/gen_project_binary.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/app.dir

# All Build rule for target.
CMakeFiles/app.dir/all: CMakeFiles/gen_project_binary.dir/all
CMakeFiles/app.dir/all: esp-idf/esptool_py/CMakeFiles/app_check_size.dir/all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/app.dir/build.make CMakeFiles/app.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/app.dir/build.make CMakeFiles/app.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles --progress-num= "Built target app"
.PHONY : CMakeFiles/app.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/app.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles 100
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/app.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles 0
.PHONY : CMakeFiles/app.dir/rule

# Convenience name for target.
app: CMakeFiles/app.dir/rule
.PHONY : app

# clean rule for target.
CMakeFiles/app.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/app.dir/build.make CMakeFiles/app.dir/clean
.PHONY : CMakeFiles/app.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/erase_flash.dir

# All Build rule for target.
CMakeFiles/erase_flash.dir/all:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/erase_flash.dir/build.make CMakeFiles/erase_flash.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/erase_flash.dir/build.make CMakeFiles/erase_flash.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles --progress-num= "Built target erase_flash"
.PHONY : CMakeFiles/erase_flash.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/erase_flash.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles 0
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/erase_flash.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles 0
.PHONY : CMakeFiles/erase_flash.dir/rule

# Convenience name for target.
erase_flash: CMakeFiles/erase_flash.dir/rule
.PHONY : erase_flash

# clean rule for target.
CMakeFiles/erase_flash.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/erase_flash.dir/build.make CMakeFiles/erase_flash.dir/clean
.PHONY : CMakeFiles/erase_flash.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/uf2.dir

# All Build rule for target.
CMakeFiles/uf2.dir/all:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/uf2.dir/build.make CMakeFiles/uf2.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/uf2.dir/build.make CMakeFiles/uf2.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles --progress-num= "Built target uf2"
.PHONY : CMakeFiles/uf2.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/uf2.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles 0
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/uf2.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles 0
.PHONY : CMakeFiles/uf2.dir/rule

# Convenience name for target.
uf2: CMakeFiles/uf2.dir/rule
.PHONY : uf2

# clean rule for target.
CMakeFiles/uf2.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/uf2.dir/build.make CMakeFiles/uf2.dir/clean
.PHONY : CMakeFiles/uf2.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/uf2-app.dir

# All Build rule for target.
CMakeFiles/uf2-app.dir/all:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/uf2-app.dir/build.make CMakeFiles/uf2-app.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/uf2-app.dir/build.make CMakeFiles/uf2-app.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles --progress-num= "Built target uf2-app"
.PHONY : CMakeFiles/uf2-app.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/uf2-app.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles 0
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/uf2-app.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles 0
.PHONY : CMakeFiles/uf2-app.dir/rule

# Convenience name for target.
uf2-app: CMakeFiles/uf2-app.dir/rule
.PHONY : uf2-app

# clean rule for target.
CMakeFiles/uf2-app.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/uf2-app.dir/build.make CMakeFiles/uf2-app.dir/clean
.PHONY : CMakeFiles/uf2-app.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/merge-bin.dir

# All Build rule for target.
CMakeFiles/merge-bin.dir/all: CMakeFiles/bootloader.dir/all
CMakeFiles/merge-bin.dir/all: CMakeFiles/gen_project_binary.dir/all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/merge-bin.dir/build.make CMakeFiles/merge-bin.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/merge-bin.dir/build.make CMakeFiles/merge-bin.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles --progress-num= "Built target merge-bin"
.PHONY : CMakeFiles/merge-bin.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/merge-bin.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles 100
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/merge-bin.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles 0
.PHONY : CMakeFiles/merge-bin.dir/rule

# Convenience name for target.
merge-bin: CMakeFiles/merge-bin.dir/rule
.PHONY : merge-bin

# clean rule for target.
CMakeFiles/merge-bin.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/merge-bin.dir/build.make CMakeFiles/merge-bin.dir/clean
.PHONY : CMakeFiles/merge-bin.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/monitor.dir

# All Build rule for target.
CMakeFiles/monitor.dir/all: CMakeFiles/xiaozhi.elf.dir/all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/monitor.dir/build.make CMakeFiles/monitor.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/monitor.dir/build.make CMakeFiles/monitor.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles --progress-num= "Built target monitor"
.PHONY : CMakeFiles/monitor.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/monitor.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles 100
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/monitor.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles 0
.PHONY : CMakeFiles/monitor.dir/rule

# Convenience name for target.
monitor: CMakeFiles/monitor.dir/rule
.PHONY : monitor

# clean rule for target.
CMakeFiles/monitor.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/monitor.dir/build.make CMakeFiles/monitor.dir/clean
.PHONY : CMakeFiles/monitor.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/flash.dir

# All Build rule for target.
CMakeFiles/flash.dir/all: CMakeFiles/bootloader.dir/all
CMakeFiles/flash.dir/all: CMakeFiles/app.dir/all
CMakeFiles/flash.dir/all: esp-idf/partition_table/CMakeFiles/partition_table_bin.dir/all
CMakeFiles/flash.dir/all: esp-idf/app_update/CMakeFiles/blank_ota_data.dir/all
CMakeFiles/flash.dir/all: esp-idf/espressif__esp-sr/CMakeFiles/srmodels_bin.dir/all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/flash.dir/build.make CMakeFiles/flash.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/flash.dir/build.make CMakeFiles/flash.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles --progress-num= "Built target flash"
.PHONY : CMakeFiles/flash.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/flash.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles 100
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/flash.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles 0
.PHONY : CMakeFiles/flash.dir/rule

# Convenience name for target.
flash: CMakeFiles/flash.dir/rule
.PHONY : flash

# clean rule for target.
CMakeFiles/flash.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/flash.dir/build.make CMakeFiles/flash.dir/clean
.PHONY : CMakeFiles/flash.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/encrypted-flash.dir

# All Build rule for target.
CMakeFiles/encrypted-flash.dir/all: esp-idf/app_update/CMakeFiles/blank_ota_data.dir/all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/encrypted-flash.dir/build.make CMakeFiles/encrypted-flash.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/encrypted-flash.dir/build.make CMakeFiles/encrypted-flash.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles --progress-num= "Built target encrypted-flash"
.PHONY : CMakeFiles/encrypted-flash.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/encrypted-flash.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles 0
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/encrypted-flash.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles 0
.PHONY : CMakeFiles/encrypted-flash.dir/rule

# Convenience name for target.
encrypted-flash: CMakeFiles/encrypted-flash.dir/rule
.PHONY : encrypted-flash

# clean rule for target.
CMakeFiles/encrypted-flash.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/encrypted-flash.dir/build.make CMakeFiles/encrypted-flash.dir/clean
.PHONY : CMakeFiles/encrypted-flash.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/_project_elf_src.dir

# All Build rule for target.
CMakeFiles/_project_elf_src.dir/all:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/_project_elf_src.dir/build.make CMakeFiles/_project_elf_src.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/_project_elf_src.dir/build.make CMakeFiles/_project_elf_src.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles --progress-num= "Built target _project_elf_src"
.PHONY : CMakeFiles/_project_elf_src.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/_project_elf_src.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles 0
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/_project_elf_src.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles 0
.PHONY : CMakeFiles/_project_elf_src.dir/rule

# Convenience name for target.
_project_elf_src: CMakeFiles/_project_elf_src.dir/rule
.PHONY : _project_elf_src

# clean rule for target.
CMakeFiles/_project_elf_src.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/_project_elf_src.dir/build.make CMakeFiles/_project_elf_src.dir/clean
.PHONY : CMakeFiles/_project_elf_src.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/xiaozhi.elf.dir

# All Build rule for target.
CMakeFiles/xiaozhi.elf.dir/all: CMakeFiles/_project_elf_src.dir/all
CMakeFiles/xiaozhi.elf.dir/all: esp-idf/xtensa/CMakeFiles/__idf_xtensa.dir/all
CMakeFiles/xiaozhi.elf.dir/all: esp-idf/esp_system/CMakeFiles/__ldgen_output_sections.ld.dir/all
CMakeFiles/xiaozhi.elf.dir/all: esp-idf/app_trace/CMakeFiles/__idf_app_trace.dir/all
CMakeFiles/xiaozhi.elf.dir/all: esp-idf/unity/CMakeFiles/__idf_unity.dir/all
CMakeFiles/xiaozhi.elf.dir/all: esp-idf/cmock/CMakeFiles/__idf_cmock.dir/all
CMakeFiles/xiaozhi.elf.dir/all: esp-idf/console/CMakeFiles/__idf_console.dir/all
CMakeFiles/xiaozhi.elf.dir/all: esp-idf/esp_driver_cam/CMakeFiles/__idf_esp_driver_cam.dir/all
CMakeFiles/xiaozhi.elf.dir/all: esp-idf/esp_hid/CMakeFiles/__idf_esp_hid.dir/all
CMakeFiles/xiaozhi.elf.dir/all: esp-idf/esp_https_server/CMakeFiles/__idf_esp_https_server.dir/all
CMakeFiles/xiaozhi.elf.dir/all: esp-idf/esp_lcd/CMakeFiles/__idf_esp_lcd.dir/all
CMakeFiles/xiaozhi.elf.dir/all: esp-idf/protobuf-c/CMakeFiles/__idf_protobuf-c.dir/all
CMakeFiles/xiaozhi.elf.dir/all: esp-idf/protocomm/CMakeFiles/__idf_protocomm.dir/all
CMakeFiles/xiaozhi.elf.dir/all: esp-idf/esp_local_ctrl/CMakeFiles/__idf_esp_local_ctrl.dir/all
CMakeFiles/xiaozhi.elf.dir/all: esp-idf/espcoredump/CMakeFiles/__idf_espcoredump.dir/all
CMakeFiles/xiaozhi.elf.dir/all: esp-idf/wear_levelling/CMakeFiles/__idf_wear_levelling.dir/all
CMakeFiles/xiaozhi.elf.dir/all: esp-idf/fatfs/CMakeFiles/__idf_fatfs.dir/all
CMakeFiles/xiaozhi.elf.dir/all: esp-idf/json/CMakeFiles/__idf_json.dir/all
CMakeFiles/xiaozhi.elf.dir/all: esp-idf/mqtt/CMakeFiles/__idf_mqtt.dir/all
CMakeFiles/xiaozhi.elf.dir/all: esp-idf/nvs_sec_provider/CMakeFiles/__idf_nvs_sec_provider.dir/all
CMakeFiles/xiaozhi.elf.dir/all: esp-idf/perfmon/CMakeFiles/__idf_perfmon.dir/all
CMakeFiles/xiaozhi.elf.dir/all: esp-idf/spiffs/CMakeFiles/__idf_spiffs.dir/all
CMakeFiles/xiaozhi.elf.dir/all: esp-idf/touch_element/CMakeFiles/__idf_touch_element.dir/all
CMakeFiles/xiaozhi.elf.dir/all: esp-idf/usb/CMakeFiles/__idf_usb.dir/all
CMakeFiles/xiaozhi.elf.dir/all: esp-idf/wifi_provisioning/CMakeFiles/__idf_wifi_provisioning.dir/all
CMakeFiles/xiaozhi.elf.dir/all: esp-idf/78__esp-ml307/CMakeFiles/__idf_78__esp-ml307.dir/all
CMakeFiles/xiaozhi.elf.dir/all: esp-idf/78__esp-opus/CMakeFiles/__idf_78__esp-opus.dir/all
CMakeFiles/xiaozhi.elf.dir/all: esp-idf/78__esp-opus-encoder/CMakeFiles/__idf_78__esp-opus-encoder.dir/all
CMakeFiles/xiaozhi.elf.dir/all: esp-idf/78__esp-wifi-connect/CMakeFiles/__idf_78__esp-wifi-connect.dir/all
CMakeFiles/xiaozhi.elf.dir/all: esp-idf/espressif__button/CMakeFiles/__idf_espressif__button.dir/all
CMakeFiles/xiaozhi.elf.dir/all: esp-idf/espressif__esp-dsp/CMakeFiles/__idf_espressif__esp-dsp.dir/all
CMakeFiles/xiaozhi.elf.dir/all: esp-idf/espressif__esp-sr/CMakeFiles/__idf_espressif__esp-sr.dir/all
CMakeFiles/xiaozhi.elf.dir/all: esp-idf/espressif__esp_codec_dev/CMakeFiles/__idf_espressif__esp_codec_dev.dir/all
CMakeFiles/xiaozhi.elf.dir/all: esp-idf/lvgl__lvgl/CMakeFiles/__idf_lvgl__lvgl.dir/all
CMakeFiles/xiaozhi.elf.dir/all: esp-idf/espressif__esp_lvgl_port/CMakeFiles/lvgl_port_lib.dir/all
CMakeFiles/xiaozhi.elf.dir/all: esp-idf/espressif__led_strip/CMakeFiles/__idf_espressif__led_strip.dir/all
CMakeFiles/xiaozhi.elf.dir/all: esp-idf/main/CMakeFiles/__idf_main.dir/all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/xiaozhi.elf.dir/build.make CMakeFiles/xiaozhi.elf.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/xiaozhi.elf.dir/build.make CMakeFiles/xiaozhi.elf.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles --progress-num=100 "Built target xiaozhi.elf"
.PHONY : CMakeFiles/xiaozhi.elf.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/xiaozhi.elf.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles 100
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/xiaozhi.elf.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles 0
.PHONY : CMakeFiles/xiaozhi.elf.dir/rule

# Convenience name for target.
xiaozhi.elf: CMakeFiles/xiaozhi.elf.dir/rule
.PHONY : xiaozhi.elf

# clean rule for target.
CMakeFiles/xiaozhi.elf.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/xiaozhi.elf.dir/build.make CMakeFiles/xiaozhi.elf.dir/clean
.PHONY : CMakeFiles/xiaozhi.elf.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/size.dir

# All Build rule for target.
CMakeFiles/size.dir/all:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/size.dir/build.make CMakeFiles/size.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/size.dir/build.make CMakeFiles/size.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles --progress-num= "Built target size"
.PHONY : CMakeFiles/size.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/size.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles 0
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/size.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles 0
.PHONY : CMakeFiles/size.dir/rule

# Convenience name for target.
size: CMakeFiles/size.dir/rule
.PHONY : size

# clean rule for target.
CMakeFiles/size.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/size.dir/build.make CMakeFiles/size.dir/clean
.PHONY : CMakeFiles/size.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/size-files.dir

# All Build rule for target.
CMakeFiles/size-files.dir/all:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/size-files.dir/build.make CMakeFiles/size-files.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/size-files.dir/build.make CMakeFiles/size-files.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles --progress-num= "Built target size-files"
.PHONY : CMakeFiles/size-files.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/size-files.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles 0
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/size-files.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles 0
.PHONY : CMakeFiles/size-files.dir/rule

# Convenience name for target.
size-files: CMakeFiles/size-files.dir/rule
.PHONY : size-files

# clean rule for target.
CMakeFiles/size-files.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/size-files.dir/build.make CMakeFiles/size-files.dir/clean
.PHONY : CMakeFiles/size-files.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/size-components.dir

# All Build rule for target.
CMakeFiles/size-components.dir/all:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/size-components.dir/build.make CMakeFiles/size-components.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/size-components.dir/build.make CMakeFiles/size-components.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles --progress-num= "Built target size-components"
.PHONY : CMakeFiles/size-components.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/size-components.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles 0
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/size-components.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles 0
.PHONY : CMakeFiles/size-components.dir/rule

# Convenience name for target.
size-components: CMakeFiles/size-components.dir/rule
.PHONY : size-components

# clean rule for target.
CMakeFiles/size-components.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/size-components.dir/build.make CMakeFiles/size-components.dir/clean
.PHONY : CMakeFiles/size-components.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/dfu.dir

# All Build rule for target.
CMakeFiles/dfu.dir/all: CMakeFiles/bootloader.dir/all
CMakeFiles/dfu.dir/all: CMakeFiles/gen_project_binary.dir/all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/dfu.dir/build.make CMakeFiles/dfu.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/dfu.dir/build.make CMakeFiles/dfu.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles --progress-num= "Built target dfu"
.PHONY : CMakeFiles/dfu.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/dfu.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles 100
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/dfu.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles 0
.PHONY : CMakeFiles/dfu.dir/rule

# Convenience name for target.
dfu: CMakeFiles/dfu.dir/rule
.PHONY : dfu

# clean rule for target.
CMakeFiles/dfu.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/dfu.dir/build.make CMakeFiles/dfu.dir/clean
.PHONY : CMakeFiles/dfu.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/dfu-list.dir

# All Build rule for target.
CMakeFiles/dfu-list.dir/all:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/dfu-list.dir/build.make CMakeFiles/dfu-list.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/dfu-list.dir/build.make CMakeFiles/dfu-list.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles --progress-num= "Built target dfu-list"
.PHONY : CMakeFiles/dfu-list.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/dfu-list.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles 0
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/dfu-list.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles 0
.PHONY : CMakeFiles/dfu-list.dir/rule

# Convenience name for target.
dfu-list: CMakeFiles/dfu-list.dir/rule
.PHONY : dfu-list

# clean rule for target.
CMakeFiles/dfu-list.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/dfu-list.dir/build.make CMakeFiles/dfu-list.dir/clean
.PHONY : CMakeFiles/dfu-list.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/dfu-flash.dir

# All Build rule for target.
CMakeFiles/dfu-flash.dir/all:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/dfu-flash.dir/build.make CMakeFiles/dfu-flash.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/dfu-flash.dir/build.make CMakeFiles/dfu-flash.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles --progress-num= "Built target dfu-flash"
.PHONY : CMakeFiles/dfu-flash.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/dfu-flash.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles 0
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/dfu-flash.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles 0
.PHONY : CMakeFiles/dfu-flash.dir/rule

# Convenience name for target.
dfu-flash: CMakeFiles/dfu-flash.dir/rule
.PHONY : dfu-flash

# clean rule for target.
CMakeFiles/dfu-flash.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/dfu-flash.dir/build.make CMakeFiles/dfu-flash.dir/clean
.PHONY : CMakeFiles/dfu-flash.dir/clean

#=============================================================================
# Target rules for target esp-idf/xtensa/CMakeFiles/__idf_xtensa.dir

# All Build rule for target.
esp-idf/xtensa/CMakeFiles/__idf_xtensa.dir/all: esp-idf/esp_driver_gpio/CMakeFiles/__idf_esp_driver_gpio.dir/all
	$(MAKE) $(MAKESILENT) -f esp-idf/xtensa/CMakeFiles/__idf_xtensa.dir/build.make esp-idf/xtensa/CMakeFiles/__idf_xtensa.dir/depend
	$(MAKE) $(MAKESILENT) -f esp-idf/xtensa/CMakeFiles/__idf_xtensa.dir/build.make esp-idf/xtensa/CMakeFiles/__idf_xtensa.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles --progress-num=90 "Built target __idf_xtensa"
.PHONY : esp-idf/xtensa/CMakeFiles/__idf_xtensa.dir/all

# Build rule for subdir invocation for target.
esp-idf/xtensa/CMakeFiles/__idf_xtensa.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles 50
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 esp-idf/xtensa/CMakeFiles/__idf_xtensa.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles 0
.PHONY : esp-idf/xtensa/CMakeFiles/__idf_xtensa.dir/rule

# Convenience name for target.
__idf_xtensa: esp-idf/xtensa/CMakeFiles/__idf_xtensa.dir/rule
.PHONY : __idf_xtensa

# clean rule for target.
esp-idf/xtensa/CMakeFiles/__idf_xtensa.dir/clean:
	$(MAKE) $(MAKESILENT) -f esp-idf/xtensa/CMakeFiles/__idf_xtensa.dir/build.make esp-idf/xtensa/CMakeFiles/__idf_xtensa.dir/clean
.PHONY : esp-idf/xtensa/CMakeFiles/__idf_xtensa.dir/clean

#=============================================================================
# Target rules for target esp-idf/esp_driver_gpio/CMakeFiles/__idf_esp_driver_gpio.dir

# All Build rule for target.
esp-idf/esp_driver_gpio/CMakeFiles/__idf_esp_driver_gpio.dir/all: esp-idf/esp_pm/CMakeFiles/__idf_esp_pm.dir/all
	$(MAKE) $(MAKESILENT) -f esp-idf/esp_driver_gpio/CMakeFiles/__idf_esp_driver_gpio.dir/build.make esp-idf/esp_driver_gpio/CMakeFiles/__idf_esp_driver_gpio.dir/depend
	$(MAKE) $(MAKESILENT) -f esp-idf/esp_driver_gpio/CMakeFiles/__idf_esp_driver_gpio.dir/build.make esp-idf/esp_driver_gpio/CMakeFiles/__idf_esp_driver_gpio.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles --progress-num= "Built target __idf_esp_driver_gpio"
.PHONY : esp-idf/esp_driver_gpio/CMakeFiles/__idf_esp_driver_gpio.dir/all

# Build rule for subdir invocation for target.
esp-idf/esp_driver_gpio/CMakeFiles/__idf_esp_driver_gpio.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles 49
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 esp-idf/esp_driver_gpio/CMakeFiles/__idf_esp_driver_gpio.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles 0
.PHONY : esp-idf/esp_driver_gpio/CMakeFiles/__idf_esp_driver_gpio.dir/rule

# Convenience name for target.
__idf_esp_driver_gpio: esp-idf/esp_driver_gpio/CMakeFiles/__idf_esp_driver_gpio.dir/rule
.PHONY : __idf_esp_driver_gpio

# clean rule for target.
esp-idf/esp_driver_gpio/CMakeFiles/__idf_esp_driver_gpio.dir/clean:
	$(MAKE) $(MAKESILENT) -f esp-idf/esp_driver_gpio/CMakeFiles/__idf_esp_driver_gpio.dir/build.make esp-idf/esp_driver_gpio/CMakeFiles/__idf_esp_driver_gpio.dir/clean
.PHONY : esp-idf/esp_driver_gpio/CMakeFiles/__idf_esp_driver_gpio.dir/clean

#=============================================================================
# Target rules for target esp-idf/esp_pm/CMakeFiles/__idf_esp_pm.dir

# All Build rule for target.
esp-idf/esp_pm/CMakeFiles/__idf_esp_pm.dir/all: esp-idf/mbedtls/CMakeFiles/__idf_mbedtls.dir/all
	$(MAKE) $(MAKESILENT) -f esp-idf/esp_pm/CMakeFiles/__idf_esp_pm.dir/build.make esp-idf/esp_pm/CMakeFiles/__idf_esp_pm.dir/depend
	$(MAKE) $(MAKESILENT) -f esp-idf/esp_pm/CMakeFiles/__idf_esp_pm.dir/build.make esp-idf/esp_pm/CMakeFiles/__idf_esp_pm.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles --progress-num= "Built target __idf_esp_pm"
.PHONY : esp-idf/esp_pm/CMakeFiles/__idf_esp_pm.dir/all

# Build rule for subdir invocation for target.
esp-idf/esp_pm/CMakeFiles/__idf_esp_pm.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles 49
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 esp-idf/esp_pm/CMakeFiles/__idf_esp_pm.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles 0
.PHONY : esp-idf/esp_pm/CMakeFiles/__idf_esp_pm.dir/rule

# Convenience name for target.
__idf_esp_pm: esp-idf/esp_pm/CMakeFiles/__idf_esp_pm.dir/rule
.PHONY : __idf_esp_pm

# clean rule for target.
esp-idf/esp_pm/CMakeFiles/__idf_esp_pm.dir/clean:
	$(MAKE) $(MAKESILENT) -f esp-idf/esp_pm/CMakeFiles/__idf_esp_pm.dir/build.make esp-idf/esp_pm/CMakeFiles/__idf_esp_pm.dir/clean
.PHONY : esp-idf/esp_pm/CMakeFiles/__idf_esp_pm.dir/clean

#=============================================================================
# Target rules for target esp-idf/mbedtls/CMakeFiles/__idf_mbedtls.dir

# All Build rule for target.
esp-idf/mbedtls/CMakeFiles/__idf_mbedtls.dir/all: esp-idf/mbedtls/mbedtls/3rdparty/everest/CMakeFiles/everest.dir/all
	$(MAKE) $(MAKESILENT) -f esp-idf/mbedtls/CMakeFiles/__idf_mbedtls.dir/build.make esp-idf/mbedtls/CMakeFiles/__idf_mbedtls.dir/depend
	$(MAKE) $(MAKESILENT) -f esp-idf/mbedtls/CMakeFiles/__idf_mbedtls.dir/build.make esp-idf/mbedtls/CMakeFiles/__idf_mbedtls.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles --progress-num= "Built target __idf_mbedtls"
.PHONY : esp-idf/mbedtls/CMakeFiles/__idf_mbedtls.dir/all

# Build rule for subdir invocation for target.
esp-idf/mbedtls/CMakeFiles/__idf_mbedtls.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles 49
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 esp-idf/mbedtls/CMakeFiles/__idf_mbedtls.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles 0
.PHONY : esp-idf/mbedtls/CMakeFiles/__idf_mbedtls.dir/rule

# Convenience name for target.
__idf_mbedtls: esp-idf/mbedtls/CMakeFiles/__idf_mbedtls.dir/rule
.PHONY : __idf_mbedtls

# clean rule for target.
esp-idf/mbedtls/CMakeFiles/__idf_mbedtls.dir/clean:
	$(MAKE) $(MAKESILENT) -f esp-idf/mbedtls/CMakeFiles/__idf_mbedtls.dir/build.make esp-idf/mbedtls/CMakeFiles/__idf_mbedtls.dir/clean
.PHONY : esp-idf/mbedtls/CMakeFiles/__idf_mbedtls.dir/clean

#=============================================================================
# Target rules for target esp-idf/mbedtls/CMakeFiles/custom_bundle.dir

# All Build rule for target.
esp-idf/mbedtls/CMakeFiles/custom_bundle.dir/all:
	$(MAKE) $(MAKESILENT) -f esp-idf/mbedtls/CMakeFiles/custom_bundle.dir/build.make esp-idf/mbedtls/CMakeFiles/custom_bundle.dir/depend
	$(MAKE) $(MAKESILENT) -f esp-idf/mbedtls/CMakeFiles/custom_bundle.dir/build.make esp-idf/mbedtls/CMakeFiles/custom_bundle.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles --progress-num= "Built target custom_bundle"
.PHONY : esp-idf/mbedtls/CMakeFiles/custom_bundle.dir/all

# Build rule for subdir invocation for target.
esp-idf/mbedtls/CMakeFiles/custom_bundle.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles 0
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 esp-idf/mbedtls/CMakeFiles/custom_bundle.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles 0
.PHONY : esp-idf/mbedtls/CMakeFiles/custom_bundle.dir/rule

# Convenience name for target.
custom_bundle: esp-idf/mbedtls/CMakeFiles/custom_bundle.dir/rule
.PHONY : custom_bundle

# clean rule for target.
esp-idf/mbedtls/CMakeFiles/custom_bundle.dir/clean:
	$(MAKE) $(MAKESILENT) -f esp-idf/mbedtls/CMakeFiles/custom_bundle.dir/build.make esp-idf/mbedtls/CMakeFiles/custom_bundle.dir/clean
.PHONY : esp-idf/mbedtls/CMakeFiles/custom_bundle.dir/clean

#=============================================================================
# Target rules for target esp-idf/mbedtls/mbedtls/CMakeFiles/apidoc.dir

# All Build rule for target.
esp-idf/mbedtls/mbedtls/CMakeFiles/apidoc.dir/all:
	$(MAKE) $(MAKESILENT) -f esp-idf/mbedtls/mbedtls/CMakeFiles/apidoc.dir/build.make esp-idf/mbedtls/mbedtls/CMakeFiles/apidoc.dir/depend
	$(MAKE) $(MAKESILENT) -f esp-idf/mbedtls/mbedtls/CMakeFiles/apidoc.dir/build.make esp-idf/mbedtls/mbedtls/CMakeFiles/apidoc.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles --progress-num= "Built target apidoc"
.PHONY : esp-idf/mbedtls/mbedtls/CMakeFiles/apidoc.dir/all

# Build rule for subdir invocation for target.
esp-idf/mbedtls/mbedtls/CMakeFiles/apidoc.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles 0
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 esp-idf/mbedtls/mbedtls/CMakeFiles/apidoc.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles 0
.PHONY : esp-idf/mbedtls/mbedtls/CMakeFiles/apidoc.dir/rule

# Convenience name for target.
apidoc: esp-idf/mbedtls/mbedtls/CMakeFiles/apidoc.dir/rule
.PHONY : apidoc

# clean rule for target.
esp-idf/mbedtls/mbedtls/CMakeFiles/apidoc.dir/clean:
	$(MAKE) $(MAKESILENT) -f esp-idf/mbedtls/mbedtls/CMakeFiles/apidoc.dir/build.make esp-idf/mbedtls/mbedtls/CMakeFiles/apidoc.dir/clean
.PHONY : esp-idf/mbedtls/mbedtls/CMakeFiles/apidoc.dir/clean

#=============================================================================
# Target rules for target esp-idf/mbedtls/mbedtls/3rdparty/everest/CMakeFiles/everest.dir

# All Build rule for target.
esp-idf/mbedtls/mbedtls/3rdparty/everest/CMakeFiles/everest.dir/all: esp-idf/mbedtls/mbedtls/3rdparty/p256-m/CMakeFiles/p256m.dir/all
	$(MAKE) $(MAKESILENT) -f esp-idf/mbedtls/mbedtls/3rdparty/everest/CMakeFiles/everest.dir/build.make esp-idf/mbedtls/mbedtls/3rdparty/everest/CMakeFiles/everest.dir/depend
	$(MAKE) $(MAKESILENT) -f esp-idf/mbedtls/mbedtls/3rdparty/everest/CMakeFiles/everest.dir/build.make esp-idf/mbedtls/mbedtls/3rdparty/everest/CMakeFiles/everest.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles --progress-num=91 "Built target everest"
.PHONY : esp-idf/mbedtls/mbedtls/3rdparty/everest/CMakeFiles/everest.dir/all

# Build rule for subdir invocation for target.
esp-idf/mbedtls/mbedtls/3rdparty/everest/CMakeFiles/everest.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles 49
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 esp-idf/mbedtls/mbedtls/3rdparty/everest/CMakeFiles/everest.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles 0
.PHONY : esp-idf/mbedtls/mbedtls/3rdparty/everest/CMakeFiles/everest.dir/rule

# Convenience name for target.
everest: esp-idf/mbedtls/mbedtls/3rdparty/everest/CMakeFiles/everest.dir/rule
.PHONY : everest

# clean rule for target.
esp-idf/mbedtls/mbedtls/3rdparty/everest/CMakeFiles/everest.dir/clean:
	$(MAKE) $(MAKESILENT) -f esp-idf/mbedtls/mbedtls/3rdparty/everest/CMakeFiles/everest.dir/build.make esp-idf/mbedtls/mbedtls/3rdparty/everest/CMakeFiles/everest.dir/clean
.PHONY : esp-idf/mbedtls/mbedtls/3rdparty/everest/CMakeFiles/everest.dir/clean

#=============================================================================
# Target rules for target esp-idf/mbedtls/mbedtls/3rdparty/p256-m/CMakeFiles/p256m.dir

# All Build rule for target.
esp-idf/mbedtls/mbedtls/3rdparty/p256-m/CMakeFiles/p256m.dir/all: esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/all
	$(MAKE) $(MAKESILENT) -f esp-idf/mbedtls/mbedtls/3rdparty/p256-m/CMakeFiles/p256m.dir/build.make esp-idf/mbedtls/mbedtls/3rdparty/p256-m/CMakeFiles/p256m.dir/depend
	$(MAKE) $(MAKESILENT) -f esp-idf/mbedtls/mbedtls/3rdparty/p256-m/CMakeFiles/p256m.dir/build.make esp-idf/mbedtls/mbedtls/3rdparty/p256-m/CMakeFiles/p256m.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles --progress-num= "Built target p256m"
.PHONY : esp-idf/mbedtls/mbedtls/3rdparty/p256-m/CMakeFiles/p256m.dir/all

# Build rule for subdir invocation for target.
esp-idf/mbedtls/mbedtls/3rdparty/p256-m/CMakeFiles/p256m.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles 48
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 esp-idf/mbedtls/mbedtls/3rdparty/p256-m/CMakeFiles/p256m.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles 0
.PHONY : esp-idf/mbedtls/mbedtls/3rdparty/p256-m/CMakeFiles/p256m.dir/rule

# Convenience name for target.
p256m: esp-idf/mbedtls/mbedtls/3rdparty/p256-m/CMakeFiles/p256m.dir/rule
.PHONY : p256m

# clean rule for target.
esp-idf/mbedtls/mbedtls/3rdparty/p256-m/CMakeFiles/p256m.dir/clean:
	$(MAKE) $(MAKESILENT) -f esp-idf/mbedtls/mbedtls/3rdparty/p256-m/CMakeFiles/p256m.dir/build.make esp-idf/mbedtls/mbedtls/3rdparty/p256-m/CMakeFiles/p256m.dir/clean
.PHONY : esp-idf/mbedtls/mbedtls/3rdparty/p256-m/CMakeFiles/p256m.dir/clean

#=============================================================================
# Target rules for target esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir

# All Build rule for target.
esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/all: esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedx509.dir/all
	$(MAKE) $(MAKESILENT) -f esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/build.make esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/depend
	$(MAKE) $(MAKESILENT) -f esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/build.make esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles --progress-num=92,93,94,95,96,97 "Built target mbedcrypto"
.PHONY : esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/all

# Build rule for subdir invocation for target.
esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles 48
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles 0
.PHONY : esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/rule

# Convenience name for target.
mbedcrypto: esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/rule
.PHONY : mbedcrypto

# clean rule for target.
esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/clean:
	$(MAKE) $(MAKESILENT) -f esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/build.make esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/clean
.PHONY : esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/clean

#=============================================================================
# Target rules for target esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedx509.dir

# All Build rule for target.
esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedx509.dir/all: esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedtls.dir/all
	$(MAKE) $(MAKESILENT) -f esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedx509.dir/build.make esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedx509.dir/depend
	$(MAKE) $(MAKESILENT) -f esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedx509.dir/build.make esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedx509.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles --progress-num=99 "Built target mbedx509"
.PHONY : esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedx509.dir/all

# Build rule for subdir invocation for target.
esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedx509.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles 42
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedx509.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles 0
.PHONY : esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedx509.dir/rule

# Convenience name for target.
mbedx509: esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedx509.dir/rule
.PHONY : mbedx509

# clean rule for target.
esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedx509.dir/clean:
	$(MAKE) $(MAKESILENT) -f esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedx509.dir/build.make esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedx509.dir/clean
.PHONY : esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedx509.dir/clean

#=============================================================================
# Target rules for target esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedtls.dir

# All Build rule for target.
esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedtls.dir/all: esp-idf/esp_app_format/CMakeFiles/__idf_esp_app_format.dir/all
	$(MAKE) $(MAKESILENT) -f esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedtls.dir/build.make esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedtls.dir/depend
	$(MAKE) $(MAKESILENT) -f esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedtls.dir/build.make esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedtls.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles --progress-num=98 "Built target mbedtls"
.PHONY : esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedtls.dir/all

# Build rule for subdir invocation for target.
esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedtls.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles 41
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedtls.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles 0
.PHONY : esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedtls.dir/rule

# Convenience name for target.
mbedtls: esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedtls.dir/rule
.PHONY : mbedtls

# clean rule for target.
esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedtls.dir/clean:
	$(MAKE) $(MAKESILENT) -f esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedtls.dir/build.make esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedtls.dir/clean
.PHONY : esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedtls.dir/clean

#=============================================================================
# Target rules for target esp-idf/mbedtls/mbedtls/library/CMakeFiles/lib.dir

# All Build rule for target.
esp-idf/mbedtls/mbedtls/library/CMakeFiles/lib.dir/all: esp-idf/xtensa/CMakeFiles/__idf_xtensa.dir/all
	$(MAKE) $(MAKESILENT) -f esp-idf/mbedtls/mbedtls/library/CMakeFiles/lib.dir/build.make esp-idf/mbedtls/mbedtls/library/CMakeFiles/lib.dir/depend
	$(MAKE) $(MAKESILENT) -f esp-idf/mbedtls/mbedtls/library/CMakeFiles/lib.dir/build.make esp-idf/mbedtls/mbedtls/library/CMakeFiles/lib.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles --progress-num= "Built target lib"
.PHONY : esp-idf/mbedtls/mbedtls/library/CMakeFiles/lib.dir/all

# Build rule for subdir invocation for target.
esp-idf/mbedtls/mbedtls/library/CMakeFiles/lib.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles 50
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 esp-idf/mbedtls/mbedtls/library/CMakeFiles/lib.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles 0
.PHONY : esp-idf/mbedtls/mbedtls/library/CMakeFiles/lib.dir/rule

# Convenience name for target.
lib: esp-idf/mbedtls/mbedtls/library/CMakeFiles/lib.dir/rule
.PHONY : lib

# clean rule for target.
esp-idf/mbedtls/mbedtls/library/CMakeFiles/lib.dir/clean:
	$(MAKE) $(MAKESILENT) -f esp-idf/mbedtls/mbedtls/library/CMakeFiles/lib.dir/build.make esp-idf/mbedtls/mbedtls/library/CMakeFiles/lib.dir/clean
.PHONY : esp-idf/mbedtls/mbedtls/library/CMakeFiles/lib.dir/clean

#=============================================================================
# Target rules for target esp-idf/bootloader/CMakeFiles/bootloader-flash.dir

# All Build rule for target.
esp-idf/bootloader/CMakeFiles/bootloader-flash.dir/all: CMakeFiles/bootloader.dir/all
	$(MAKE) $(MAKESILENT) -f esp-idf/bootloader/CMakeFiles/bootloader-flash.dir/build.make esp-idf/bootloader/CMakeFiles/bootloader-flash.dir/depend
	$(MAKE) $(MAKESILENT) -f esp-idf/bootloader/CMakeFiles/bootloader-flash.dir/build.make esp-idf/bootloader/CMakeFiles/bootloader-flash.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles --progress-num= "Built target bootloader-flash"
.PHONY : esp-idf/bootloader/CMakeFiles/bootloader-flash.dir/all

# Build rule for subdir invocation for target.
esp-idf/bootloader/CMakeFiles/bootloader-flash.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles 0
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 esp-idf/bootloader/CMakeFiles/bootloader-flash.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles 0
.PHONY : esp-idf/bootloader/CMakeFiles/bootloader-flash.dir/rule

# Convenience name for target.
bootloader-flash: esp-idf/bootloader/CMakeFiles/bootloader-flash.dir/rule
.PHONY : bootloader-flash

# clean rule for target.
esp-idf/bootloader/CMakeFiles/bootloader-flash.dir/clean:
	$(MAKE) $(MAKESILENT) -f esp-idf/bootloader/CMakeFiles/bootloader-flash.dir/build.make esp-idf/bootloader/CMakeFiles/bootloader-flash.dir/clean
.PHONY : esp-idf/bootloader/CMakeFiles/bootloader-flash.dir/clean

#=============================================================================
# Target rules for target esp-idf/bootloader/CMakeFiles/encrypted-bootloader-flash.dir

# All Build rule for target.
esp-idf/bootloader/CMakeFiles/encrypted-bootloader-flash.dir/all:
	$(MAKE) $(MAKESILENT) -f esp-idf/bootloader/CMakeFiles/encrypted-bootloader-flash.dir/build.make esp-idf/bootloader/CMakeFiles/encrypted-bootloader-flash.dir/depend
	$(MAKE) $(MAKESILENT) -f esp-idf/bootloader/CMakeFiles/encrypted-bootloader-flash.dir/build.make esp-idf/bootloader/CMakeFiles/encrypted-bootloader-flash.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles --progress-num= "Built target encrypted-bootloader-flash"
.PHONY : esp-idf/bootloader/CMakeFiles/encrypted-bootloader-flash.dir/all

# Build rule for subdir invocation for target.
esp-idf/bootloader/CMakeFiles/encrypted-bootloader-flash.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles 0
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 esp-idf/bootloader/CMakeFiles/encrypted-bootloader-flash.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles 0
.PHONY : esp-idf/bootloader/CMakeFiles/encrypted-bootloader-flash.dir/rule

# Convenience name for target.
encrypted-bootloader-flash: esp-idf/bootloader/CMakeFiles/encrypted-bootloader-flash.dir/rule
.PHONY : encrypted-bootloader-flash

# clean rule for target.
esp-idf/bootloader/CMakeFiles/encrypted-bootloader-flash.dir/clean:
	$(MAKE) $(MAKESILENT) -f esp-idf/bootloader/CMakeFiles/encrypted-bootloader-flash.dir/build.make esp-idf/bootloader/CMakeFiles/encrypted-bootloader-flash.dir/clean
.PHONY : esp-idf/bootloader/CMakeFiles/encrypted-bootloader-flash.dir/clean

#=============================================================================
# Target rules for target esp-idf/esptool_py/CMakeFiles/app-flash.dir

# All Build rule for target.
esp-idf/esptool_py/CMakeFiles/app-flash.dir/all: CMakeFiles/app.dir/all
	$(MAKE) $(MAKESILENT) -f esp-idf/esptool_py/CMakeFiles/app-flash.dir/build.make esp-idf/esptool_py/CMakeFiles/app-flash.dir/depend
	$(MAKE) $(MAKESILENT) -f esp-idf/esptool_py/CMakeFiles/app-flash.dir/build.make esp-idf/esptool_py/CMakeFiles/app-flash.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles --progress-num= "Built target app-flash"
.PHONY : esp-idf/esptool_py/CMakeFiles/app-flash.dir/all

# Build rule for subdir invocation for target.
esp-idf/esptool_py/CMakeFiles/app-flash.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles 100
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 esp-idf/esptool_py/CMakeFiles/app-flash.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles 0
.PHONY : esp-idf/esptool_py/CMakeFiles/app-flash.dir/rule

# Convenience name for target.
app-flash: esp-idf/esptool_py/CMakeFiles/app-flash.dir/rule
.PHONY : app-flash

# clean rule for target.
esp-idf/esptool_py/CMakeFiles/app-flash.dir/clean:
	$(MAKE) $(MAKESILENT) -f esp-idf/esptool_py/CMakeFiles/app-flash.dir/build.make esp-idf/esptool_py/CMakeFiles/app-flash.dir/clean
.PHONY : esp-idf/esptool_py/CMakeFiles/app-flash.dir/clean

#=============================================================================
# Target rules for target esp-idf/esptool_py/CMakeFiles/encrypted-app-flash.dir

# All Build rule for target.
esp-idf/esptool_py/CMakeFiles/encrypted-app-flash.dir/all:
	$(MAKE) $(MAKESILENT) -f esp-idf/esptool_py/CMakeFiles/encrypted-app-flash.dir/build.make esp-idf/esptool_py/CMakeFiles/encrypted-app-flash.dir/depend
	$(MAKE) $(MAKESILENT) -f esp-idf/esptool_py/CMakeFiles/encrypted-app-flash.dir/build.make esp-idf/esptool_py/CMakeFiles/encrypted-app-flash.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles --progress-num= "Built target encrypted-app-flash"
.PHONY : esp-idf/esptool_py/CMakeFiles/encrypted-app-flash.dir/all

# Build rule for subdir invocation for target.
esp-idf/esptool_py/CMakeFiles/encrypted-app-flash.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles 0
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 esp-idf/esptool_py/CMakeFiles/encrypted-app-flash.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles 0
.PHONY : esp-idf/esptool_py/CMakeFiles/encrypted-app-flash.dir/rule

# Convenience name for target.
encrypted-app-flash: esp-idf/esptool_py/CMakeFiles/encrypted-app-flash.dir/rule
.PHONY : encrypted-app-flash

# clean rule for target.
esp-idf/esptool_py/CMakeFiles/encrypted-app-flash.dir/clean:
	$(MAKE) $(MAKESILENT) -f esp-idf/esptool_py/CMakeFiles/encrypted-app-flash.dir/build.make esp-idf/esptool_py/CMakeFiles/encrypted-app-flash.dir/clean
.PHONY : esp-idf/esptool_py/CMakeFiles/encrypted-app-flash.dir/clean

#=============================================================================
# Target rules for target esp-idf/esptool_py/CMakeFiles/app_check_size.dir

# All Build rule for target.
esp-idf/esptool_py/CMakeFiles/app_check_size.dir/all: CMakeFiles/gen_project_binary.dir/all
esp-idf/esptool_py/CMakeFiles/app_check_size.dir/all: esp-idf/partition_table/CMakeFiles/partition_table_bin.dir/all
	$(MAKE) $(MAKESILENT) -f esp-idf/esptool_py/CMakeFiles/app_check_size.dir/build.make esp-idf/esptool_py/CMakeFiles/app_check_size.dir/depend
	$(MAKE) $(MAKESILENT) -f esp-idf/esptool_py/CMakeFiles/app_check_size.dir/build.make esp-idf/esptool_py/CMakeFiles/app_check_size.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles --progress-num= "Built target app_check_size"
.PHONY : esp-idf/esptool_py/CMakeFiles/app_check_size.dir/all

# Build rule for subdir invocation for target.
esp-idf/esptool_py/CMakeFiles/app_check_size.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles 100
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 esp-idf/esptool_py/CMakeFiles/app_check_size.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles 0
.PHONY : esp-idf/esptool_py/CMakeFiles/app_check_size.dir/rule

# Convenience name for target.
app_check_size: esp-idf/esptool_py/CMakeFiles/app_check_size.dir/rule
.PHONY : app_check_size

# clean rule for target.
esp-idf/esptool_py/CMakeFiles/app_check_size.dir/clean:
	$(MAKE) $(MAKESILENT) -f esp-idf/esptool_py/CMakeFiles/app_check_size.dir/build.make esp-idf/esptool_py/CMakeFiles/app_check_size.dir/clean
.PHONY : esp-idf/esptool_py/CMakeFiles/app_check_size.dir/clean

#=============================================================================
# Target rules for target esp-idf/partition_table/CMakeFiles/partition_table_bin.dir

# All Build rule for target.
esp-idf/partition_table/CMakeFiles/partition_table_bin.dir/all:
	$(MAKE) $(MAKESILENT) -f esp-idf/partition_table/CMakeFiles/partition_table_bin.dir/build.make esp-idf/partition_table/CMakeFiles/partition_table_bin.dir/depend
	$(MAKE) $(MAKESILENT) -f esp-idf/partition_table/CMakeFiles/partition_table_bin.dir/build.make esp-idf/partition_table/CMakeFiles/partition_table_bin.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles --progress-num= "Built target partition_table_bin"
.PHONY : esp-idf/partition_table/CMakeFiles/partition_table_bin.dir/all

# Build rule for subdir invocation for target.
esp-idf/partition_table/CMakeFiles/partition_table_bin.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles 0
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 esp-idf/partition_table/CMakeFiles/partition_table_bin.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles 0
.PHONY : esp-idf/partition_table/CMakeFiles/partition_table_bin.dir/rule

# Convenience name for target.
partition_table_bin: esp-idf/partition_table/CMakeFiles/partition_table_bin.dir/rule
.PHONY : partition_table_bin

# clean rule for target.
esp-idf/partition_table/CMakeFiles/partition_table_bin.dir/clean:
	$(MAKE) $(MAKESILENT) -f esp-idf/partition_table/CMakeFiles/partition_table_bin.dir/build.make esp-idf/partition_table/CMakeFiles/partition_table_bin.dir/clean
.PHONY : esp-idf/partition_table/CMakeFiles/partition_table_bin.dir/clean

#=============================================================================
# Target rules for target esp-idf/partition_table/CMakeFiles/partition-table.dir

# All Build rule for target.
esp-idf/partition_table/CMakeFiles/partition-table.dir/all: esp-idf/partition_table/CMakeFiles/partition_table_bin.dir/all
	$(MAKE) $(MAKESILENT) -f esp-idf/partition_table/CMakeFiles/partition-table.dir/build.make esp-idf/partition_table/CMakeFiles/partition-table.dir/depend
	$(MAKE) $(MAKESILENT) -f esp-idf/partition_table/CMakeFiles/partition-table.dir/build.make esp-idf/partition_table/CMakeFiles/partition-table.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles --progress-num= "Built target partition-table"
.PHONY : esp-idf/partition_table/CMakeFiles/partition-table.dir/all

# Build rule for subdir invocation for target.
esp-idf/partition_table/CMakeFiles/partition-table.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles 0
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 esp-idf/partition_table/CMakeFiles/partition-table.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles 0
.PHONY : esp-idf/partition_table/CMakeFiles/partition-table.dir/rule

# Convenience name for target.
partition-table: esp-idf/partition_table/CMakeFiles/partition-table.dir/rule
.PHONY : partition-table

# clean rule for target.
esp-idf/partition_table/CMakeFiles/partition-table.dir/clean:
	$(MAKE) $(MAKESILENT) -f esp-idf/partition_table/CMakeFiles/partition-table.dir/build.make esp-idf/partition_table/CMakeFiles/partition-table.dir/clean
.PHONY : esp-idf/partition_table/CMakeFiles/partition-table.dir/clean

#=============================================================================
# Target rules for target esp-idf/partition_table/CMakeFiles/partition_table.dir

# All Build rule for target.
esp-idf/partition_table/CMakeFiles/partition_table.dir/all: esp-idf/partition_table/CMakeFiles/partition-table.dir/all
	$(MAKE) $(MAKESILENT) -f esp-idf/partition_table/CMakeFiles/partition_table.dir/build.make esp-idf/partition_table/CMakeFiles/partition_table.dir/depend
	$(MAKE) $(MAKESILENT) -f esp-idf/partition_table/CMakeFiles/partition_table.dir/build.make esp-idf/partition_table/CMakeFiles/partition_table.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles --progress-num= "Built target partition_table"
.PHONY : esp-idf/partition_table/CMakeFiles/partition_table.dir/all

# Build rule for subdir invocation for target.
esp-idf/partition_table/CMakeFiles/partition_table.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles 0
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 esp-idf/partition_table/CMakeFiles/partition_table.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles 0
.PHONY : esp-idf/partition_table/CMakeFiles/partition_table.dir/rule

# Convenience name for target.
partition_table: esp-idf/partition_table/CMakeFiles/partition_table.dir/rule
.PHONY : partition_table

# clean rule for target.
esp-idf/partition_table/CMakeFiles/partition_table.dir/clean:
	$(MAKE) $(MAKESILENT) -f esp-idf/partition_table/CMakeFiles/partition_table.dir/build.make esp-idf/partition_table/CMakeFiles/partition_table.dir/clean
.PHONY : esp-idf/partition_table/CMakeFiles/partition_table.dir/clean

#=============================================================================
# Target rules for target esp-idf/partition_table/CMakeFiles/partition-table-flash.dir

# All Build rule for target.
esp-idf/partition_table/CMakeFiles/partition-table-flash.dir/all:
	$(MAKE) $(MAKESILENT) -f esp-idf/partition_table/CMakeFiles/partition-table-flash.dir/build.make esp-idf/partition_table/CMakeFiles/partition-table-flash.dir/depend
	$(MAKE) $(MAKESILENT) -f esp-idf/partition_table/CMakeFiles/partition-table-flash.dir/build.make esp-idf/partition_table/CMakeFiles/partition-table-flash.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles --progress-num= "Built target partition-table-flash"
.PHONY : esp-idf/partition_table/CMakeFiles/partition-table-flash.dir/all

# Build rule for subdir invocation for target.
esp-idf/partition_table/CMakeFiles/partition-table-flash.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles 0
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 esp-idf/partition_table/CMakeFiles/partition-table-flash.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles 0
.PHONY : esp-idf/partition_table/CMakeFiles/partition-table-flash.dir/rule

# Convenience name for target.
partition-table-flash: esp-idf/partition_table/CMakeFiles/partition-table-flash.dir/rule
.PHONY : partition-table-flash

# clean rule for target.
esp-idf/partition_table/CMakeFiles/partition-table-flash.dir/clean:
	$(MAKE) $(MAKESILENT) -f esp-idf/partition_table/CMakeFiles/partition-table-flash.dir/build.make esp-idf/partition_table/CMakeFiles/partition-table-flash.dir/clean
.PHONY : esp-idf/partition_table/CMakeFiles/partition-table-flash.dir/clean

#=============================================================================
# Target rules for target esp-idf/partition_table/CMakeFiles/encrypted-partition-table-flash.dir

# All Build rule for target.
esp-idf/partition_table/CMakeFiles/encrypted-partition-table-flash.dir/all:
	$(MAKE) $(MAKESILENT) -f esp-idf/partition_table/CMakeFiles/encrypted-partition-table-flash.dir/build.make esp-idf/partition_table/CMakeFiles/encrypted-partition-table-flash.dir/depend
	$(MAKE) $(MAKESILENT) -f esp-idf/partition_table/CMakeFiles/encrypted-partition-table-flash.dir/build.make esp-idf/partition_table/CMakeFiles/encrypted-partition-table-flash.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles --progress-num= "Built target encrypted-partition-table-flash"
.PHONY : esp-idf/partition_table/CMakeFiles/encrypted-partition-table-flash.dir/all

# Build rule for subdir invocation for target.
esp-idf/partition_table/CMakeFiles/encrypted-partition-table-flash.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles 0
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 esp-idf/partition_table/CMakeFiles/encrypted-partition-table-flash.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles 0
.PHONY : esp-idf/partition_table/CMakeFiles/encrypted-partition-table-flash.dir/rule

# Convenience name for target.
encrypted-partition-table-flash: esp-idf/partition_table/CMakeFiles/encrypted-partition-table-flash.dir/rule
.PHONY : encrypted-partition-table-flash

# clean rule for target.
esp-idf/partition_table/CMakeFiles/encrypted-partition-table-flash.dir/clean:
	$(MAKE) $(MAKESILENT) -f esp-idf/partition_table/CMakeFiles/encrypted-partition-table-flash.dir/build.make esp-idf/partition_table/CMakeFiles/encrypted-partition-table-flash.dir/clean
.PHONY : esp-idf/partition_table/CMakeFiles/encrypted-partition-table-flash.dir/clean

#=============================================================================
# Target rules for target esp-idf/partition_table/CMakeFiles/partition_table-flash.dir

# All Build rule for target.
esp-idf/partition_table/CMakeFiles/partition_table-flash.dir/all: esp-idf/partition_table/CMakeFiles/partition-table-flash.dir/all
	$(MAKE) $(MAKESILENT) -f esp-idf/partition_table/CMakeFiles/partition_table-flash.dir/build.make esp-idf/partition_table/CMakeFiles/partition_table-flash.dir/depend
	$(MAKE) $(MAKESILENT) -f esp-idf/partition_table/CMakeFiles/partition_table-flash.dir/build.make esp-idf/partition_table/CMakeFiles/partition_table-flash.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles --progress-num= "Built target partition_table-flash"
.PHONY : esp-idf/partition_table/CMakeFiles/partition_table-flash.dir/all

# Build rule for subdir invocation for target.
esp-idf/partition_table/CMakeFiles/partition_table-flash.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles 0
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 esp-idf/partition_table/CMakeFiles/partition_table-flash.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles 0
.PHONY : esp-idf/partition_table/CMakeFiles/partition_table-flash.dir/rule

# Convenience name for target.
partition_table-flash: esp-idf/partition_table/CMakeFiles/partition_table-flash.dir/rule
.PHONY : partition_table-flash

# clean rule for target.
esp-idf/partition_table/CMakeFiles/partition_table-flash.dir/clean:
	$(MAKE) $(MAKESILENT) -f esp-idf/partition_table/CMakeFiles/partition_table-flash.dir/build.make esp-idf/partition_table/CMakeFiles/partition_table-flash.dir/clean
.PHONY : esp-idf/partition_table/CMakeFiles/partition_table-flash.dir/clean

#=============================================================================
# Target rules for target esp-idf/esp_app_format/CMakeFiles/__idf_esp_app_format.dir

# All Build rule for target.
esp-idf/esp_app_format/CMakeFiles/__idf_esp_app_format.dir/all: esp-idf/esp_bootloader_format/CMakeFiles/__idf_esp_bootloader_format.dir/all
	$(MAKE) $(MAKESILENT) -f esp-idf/esp_app_format/CMakeFiles/__idf_esp_app_format.dir/build.make esp-idf/esp_app_format/CMakeFiles/__idf_esp_app_format.dir/depend
	$(MAKE) $(MAKESILENT) -f esp-idf/esp_app_format/CMakeFiles/__idf_esp_app_format.dir/build.make esp-idf/esp_app_format/CMakeFiles/__idf_esp_app_format.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles --progress-num= "Built target __idf_esp_app_format"
.PHONY : esp-idf/esp_app_format/CMakeFiles/__idf_esp_app_format.dir/all

# Build rule for subdir invocation for target.
esp-idf/esp_app_format/CMakeFiles/__idf_esp_app_format.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles 40
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 esp-idf/esp_app_format/CMakeFiles/__idf_esp_app_format.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles 0
.PHONY : esp-idf/esp_app_format/CMakeFiles/__idf_esp_app_format.dir/rule

# Convenience name for target.
__idf_esp_app_format: esp-idf/esp_app_format/CMakeFiles/__idf_esp_app_format.dir/rule
.PHONY : __idf_esp_app_format

# clean rule for target.
esp-idf/esp_app_format/CMakeFiles/__idf_esp_app_format.dir/clean:
	$(MAKE) $(MAKESILENT) -f esp-idf/esp_app_format/CMakeFiles/__idf_esp_app_format.dir/build.make esp-idf/esp_app_format/CMakeFiles/__idf_esp_app_format.dir/clean
.PHONY : esp-idf/esp_app_format/CMakeFiles/__idf_esp_app_format.dir/clean

#=============================================================================
# Target rules for target esp-idf/esp_bootloader_format/CMakeFiles/__idf_esp_bootloader_format.dir

# All Build rule for target.
esp-idf/esp_bootloader_format/CMakeFiles/__idf_esp_bootloader_format.dir/all: esp-idf/app_update/CMakeFiles/__idf_app_update.dir/all
	$(MAKE) $(MAKESILENT) -f esp-idf/esp_bootloader_format/CMakeFiles/__idf_esp_bootloader_format.dir/build.make esp-idf/esp_bootloader_format/CMakeFiles/__idf_esp_bootloader_format.dir/depend
	$(MAKE) $(MAKESILENT) -f esp-idf/esp_bootloader_format/CMakeFiles/__idf_esp_bootloader_format.dir/build.make esp-idf/esp_bootloader_format/CMakeFiles/__idf_esp_bootloader_format.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles --progress-num= "Built target __idf_esp_bootloader_format"
.PHONY : esp-idf/esp_bootloader_format/CMakeFiles/__idf_esp_bootloader_format.dir/all

# Build rule for subdir invocation for target.
esp-idf/esp_bootloader_format/CMakeFiles/__idf_esp_bootloader_format.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles 40
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 esp-idf/esp_bootloader_format/CMakeFiles/__idf_esp_bootloader_format.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles 0
.PHONY : esp-idf/esp_bootloader_format/CMakeFiles/__idf_esp_bootloader_format.dir/rule

# Convenience name for target.
__idf_esp_bootloader_format: esp-idf/esp_bootloader_format/CMakeFiles/__idf_esp_bootloader_format.dir/rule
.PHONY : __idf_esp_bootloader_format

# clean rule for target.
esp-idf/esp_bootloader_format/CMakeFiles/__idf_esp_bootloader_format.dir/clean:
	$(MAKE) $(MAKESILENT) -f esp-idf/esp_bootloader_format/CMakeFiles/__idf_esp_bootloader_format.dir/build.make esp-idf/esp_bootloader_format/CMakeFiles/__idf_esp_bootloader_format.dir/clean
.PHONY : esp-idf/esp_bootloader_format/CMakeFiles/__idf_esp_bootloader_format.dir/clean

#=============================================================================
# Target rules for target esp-idf/app_update/CMakeFiles/__idf_app_update.dir

# All Build rule for target.
esp-idf/app_update/CMakeFiles/__idf_app_update.dir/all: esp-idf/esp_partition/CMakeFiles/__idf_esp_partition.dir/all
	$(MAKE) $(MAKESILENT) -f esp-idf/app_update/CMakeFiles/__idf_app_update.dir/build.make esp-idf/app_update/CMakeFiles/__idf_app_update.dir/depend
	$(MAKE) $(MAKESILENT) -f esp-idf/app_update/CMakeFiles/__idf_app_update.dir/build.make esp-idf/app_update/CMakeFiles/__idf_app_update.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles --progress-num=9 "Built target __idf_app_update"
.PHONY : esp-idf/app_update/CMakeFiles/__idf_app_update.dir/all

# Build rule for subdir invocation for target.
esp-idf/app_update/CMakeFiles/__idf_app_update.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles 40
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 esp-idf/app_update/CMakeFiles/__idf_app_update.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles 0
.PHONY : esp-idf/app_update/CMakeFiles/__idf_app_update.dir/rule

# Convenience name for target.
__idf_app_update: esp-idf/app_update/CMakeFiles/__idf_app_update.dir/rule
.PHONY : __idf_app_update

# clean rule for target.
esp-idf/app_update/CMakeFiles/__idf_app_update.dir/clean:
	$(MAKE) $(MAKESILENT) -f esp-idf/app_update/CMakeFiles/__idf_app_update.dir/build.make esp-idf/app_update/CMakeFiles/__idf_app_update.dir/clean
.PHONY : esp-idf/app_update/CMakeFiles/__idf_app_update.dir/clean

#=============================================================================
# Target rules for target esp-idf/app_update/CMakeFiles/blank_ota_data.dir

# All Build rule for target.
esp-idf/app_update/CMakeFiles/blank_ota_data.dir/all:
	$(MAKE) $(MAKESILENT) -f esp-idf/app_update/CMakeFiles/blank_ota_data.dir/build.make esp-idf/app_update/CMakeFiles/blank_ota_data.dir/depend
	$(MAKE) $(MAKESILENT) -f esp-idf/app_update/CMakeFiles/blank_ota_data.dir/build.make esp-idf/app_update/CMakeFiles/blank_ota_data.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles --progress-num= "Built target blank_ota_data"
.PHONY : esp-idf/app_update/CMakeFiles/blank_ota_data.dir/all

# Build rule for subdir invocation for target.
esp-idf/app_update/CMakeFiles/blank_ota_data.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles 0
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 esp-idf/app_update/CMakeFiles/blank_ota_data.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles 0
.PHONY : esp-idf/app_update/CMakeFiles/blank_ota_data.dir/rule

# Convenience name for target.
blank_ota_data: esp-idf/app_update/CMakeFiles/blank_ota_data.dir/rule
.PHONY : blank_ota_data

# clean rule for target.
esp-idf/app_update/CMakeFiles/blank_ota_data.dir/clean:
	$(MAKE) $(MAKESILENT) -f esp-idf/app_update/CMakeFiles/blank_ota_data.dir/build.make esp-idf/app_update/CMakeFiles/blank_ota_data.dir/clean
.PHONY : esp-idf/app_update/CMakeFiles/blank_ota_data.dir/clean

#=============================================================================
# Target rules for target esp-idf/app_update/CMakeFiles/read-otadata.dir

# All Build rule for target.
esp-idf/app_update/CMakeFiles/read-otadata.dir/all:
	$(MAKE) $(MAKESILENT) -f esp-idf/app_update/CMakeFiles/read-otadata.dir/build.make esp-idf/app_update/CMakeFiles/read-otadata.dir/depend
	$(MAKE) $(MAKESILENT) -f esp-idf/app_update/CMakeFiles/read-otadata.dir/build.make esp-idf/app_update/CMakeFiles/read-otadata.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles --progress-num= "Built target read-otadata"
.PHONY : esp-idf/app_update/CMakeFiles/read-otadata.dir/all

# Build rule for subdir invocation for target.
esp-idf/app_update/CMakeFiles/read-otadata.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles 0
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 esp-idf/app_update/CMakeFiles/read-otadata.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles 0
.PHONY : esp-idf/app_update/CMakeFiles/read-otadata.dir/rule

# Convenience name for target.
read-otadata: esp-idf/app_update/CMakeFiles/read-otadata.dir/rule
.PHONY : read-otadata

# clean rule for target.
esp-idf/app_update/CMakeFiles/read-otadata.dir/clean:
	$(MAKE) $(MAKESILENT) -f esp-idf/app_update/CMakeFiles/read-otadata.dir/build.make esp-idf/app_update/CMakeFiles/read-otadata.dir/clean
.PHONY : esp-idf/app_update/CMakeFiles/read-otadata.dir/clean

#=============================================================================
# Target rules for target esp-idf/app_update/CMakeFiles/read_otadata.dir

# All Build rule for target.
esp-idf/app_update/CMakeFiles/read_otadata.dir/all: esp-idf/app_update/CMakeFiles/read-otadata.dir/all
	$(MAKE) $(MAKESILENT) -f esp-idf/app_update/CMakeFiles/read_otadata.dir/build.make esp-idf/app_update/CMakeFiles/read_otadata.dir/depend
	$(MAKE) $(MAKESILENT) -f esp-idf/app_update/CMakeFiles/read_otadata.dir/build.make esp-idf/app_update/CMakeFiles/read_otadata.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles --progress-num= "Built target read_otadata"
.PHONY : esp-idf/app_update/CMakeFiles/read_otadata.dir/all

# Build rule for subdir invocation for target.
esp-idf/app_update/CMakeFiles/read_otadata.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles 0
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 esp-idf/app_update/CMakeFiles/read_otadata.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles 0
.PHONY : esp-idf/app_update/CMakeFiles/read_otadata.dir/rule

# Convenience name for target.
read_otadata: esp-idf/app_update/CMakeFiles/read_otadata.dir/rule
.PHONY : read_otadata

# clean rule for target.
esp-idf/app_update/CMakeFiles/read_otadata.dir/clean:
	$(MAKE) $(MAKESILENT) -f esp-idf/app_update/CMakeFiles/read_otadata.dir/build.make esp-idf/app_update/CMakeFiles/read_otadata.dir/clean
.PHONY : esp-idf/app_update/CMakeFiles/read_otadata.dir/clean

#=============================================================================
# Target rules for target esp-idf/app_update/CMakeFiles/erase-otadata.dir

# All Build rule for target.
esp-idf/app_update/CMakeFiles/erase-otadata.dir/all:
	$(MAKE) $(MAKESILENT) -f esp-idf/app_update/CMakeFiles/erase-otadata.dir/build.make esp-idf/app_update/CMakeFiles/erase-otadata.dir/depend
	$(MAKE) $(MAKESILENT) -f esp-idf/app_update/CMakeFiles/erase-otadata.dir/build.make esp-idf/app_update/CMakeFiles/erase-otadata.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles --progress-num= "Built target erase-otadata"
.PHONY : esp-idf/app_update/CMakeFiles/erase-otadata.dir/all

# Build rule for subdir invocation for target.
esp-idf/app_update/CMakeFiles/erase-otadata.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles 0
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 esp-idf/app_update/CMakeFiles/erase-otadata.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles 0
.PHONY : esp-idf/app_update/CMakeFiles/erase-otadata.dir/rule

# Convenience name for target.
erase-otadata: esp-idf/app_update/CMakeFiles/erase-otadata.dir/rule
.PHONY : erase-otadata

# clean rule for target.
esp-idf/app_update/CMakeFiles/erase-otadata.dir/clean:
	$(MAKE) $(MAKESILENT) -f esp-idf/app_update/CMakeFiles/erase-otadata.dir/build.make esp-idf/app_update/CMakeFiles/erase-otadata.dir/clean
.PHONY : esp-idf/app_update/CMakeFiles/erase-otadata.dir/clean

#=============================================================================
# Target rules for target esp-idf/app_update/CMakeFiles/erase_otadata.dir

# All Build rule for target.
esp-idf/app_update/CMakeFiles/erase_otadata.dir/all: esp-idf/app_update/CMakeFiles/erase-otadata.dir/all
	$(MAKE) $(MAKESILENT) -f esp-idf/app_update/CMakeFiles/erase_otadata.dir/build.make esp-idf/app_update/CMakeFiles/erase_otadata.dir/depend
	$(MAKE) $(MAKESILENT) -f esp-idf/app_update/CMakeFiles/erase_otadata.dir/build.make esp-idf/app_update/CMakeFiles/erase_otadata.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles --progress-num= "Built target erase_otadata"
.PHONY : esp-idf/app_update/CMakeFiles/erase_otadata.dir/all

# Build rule for subdir invocation for target.
esp-idf/app_update/CMakeFiles/erase_otadata.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles 0
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 esp-idf/app_update/CMakeFiles/erase_otadata.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles 0
.PHONY : esp-idf/app_update/CMakeFiles/erase_otadata.dir/rule

# Convenience name for target.
erase_otadata: esp-idf/app_update/CMakeFiles/erase_otadata.dir/rule
.PHONY : erase_otadata

# clean rule for target.
esp-idf/app_update/CMakeFiles/erase_otadata.dir/clean:
	$(MAKE) $(MAKESILENT) -f esp-idf/app_update/CMakeFiles/erase_otadata.dir/build.make esp-idf/app_update/CMakeFiles/erase_otadata.dir/clean
.PHONY : esp-idf/app_update/CMakeFiles/erase_otadata.dir/clean

#=============================================================================
# Target rules for target esp-idf/app_update/CMakeFiles/otadata-flash.dir

# All Build rule for target.
esp-idf/app_update/CMakeFiles/otadata-flash.dir/all:
	$(MAKE) $(MAKESILENT) -f esp-idf/app_update/CMakeFiles/otadata-flash.dir/build.make esp-idf/app_update/CMakeFiles/otadata-flash.dir/depend
	$(MAKE) $(MAKESILENT) -f esp-idf/app_update/CMakeFiles/otadata-flash.dir/build.make esp-idf/app_update/CMakeFiles/otadata-flash.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles --progress-num= "Built target otadata-flash"
.PHONY : esp-idf/app_update/CMakeFiles/otadata-flash.dir/all

# Build rule for subdir invocation for target.
esp-idf/app_update/CMakeFiles/otadata-flash.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles 0
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 esp-idf/app_update/CMakeFiles/otadata-flash.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles 0
.PHONY : esp-idf/app_update/CMakeFiles/otadata-flash.dir/rule

# Convenience name for target.
otadata-flash: esp-idf/app_update/CMakeFiles/otadata-flash.dir/rule
.PHONY : otadata-flash

# clean rule for target.
esp-idf/app_update/CMakeFiles/otadata-flash.dir/clean:
	$(MAKE) $(MAKESILENT) -f esp-idf/app_update/CMakeFiles/otadata-flash.dir/build.make esp-idf/app_update/CMakeFiles/otadata-flash.dir/clean
.PHONY : esp-idf/app_update/CMakeFiles/otadata-flash.dir/clean

#=============================================================================
# Target rules for target esp-idf/app_update/CMakeFiles/encrypted-otadata-flash.dir

# All Build rule for target.
esp-idf/app_update/CMakeFiles/encrypted-otadata-flash.dir/all:
	$(MAKE) $(MAKESILENT) -f esp-idf/app_update/CMakeFiles/encrypted-otadata-flash.dir/build.make esp-idf/app_update/CMakeFiles/encrypted-otadata-flash.dir/depend
	$(MAKE) $(MAKESILENT) -f esp-idf/app_update/CMakeFiles/encrypted-otadata-flash.dir/build.make esp-idf/app_update/CMakeFiles/encrypted-otadata-flash.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles --progress-num= "Built target encrypted-otadata-flash"
.PHONY : esp-idf/app_update/CMakeFiles/encrypted-otadata-flash.dir/all

# Build rule for subdir invocation for target.
esp-idf/app_update/CMakeFiles/encrypted-otadata-flash.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles 0
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 esp-idf/app_update/CMakeFiles/encrypted-otadata-flash.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles 0
.PHONY : esp-idf/app_update/CMakeFiles/encrypted-otadata-flash.dir/rule

# Convenience name for target.
encrypted-otadata-flash: esp-idf/app_update/CMakeFiles/encrypted-otadata-flash.dir/rule
.PHONY : encrypted-otadata-flash

# clean rule for target.
esp-idf/app_update/CMakeFiles/encrypted-otadata-flash.dir/clean:
	$(MAKE) $(MAKESILENT) -f esp-idf/app_update/CMakeFiles/encrypted-otadata-flash.dir/build.make esp-idf/app_update/CMakeFiles/encrypted-otadata-flash.dir/clean
.PHONY : esp-idf/app_update/CMakeFiles/encrypted-otadata-flash.dir/clean

#=============================================================================
# Target rules for target esp-idf/esp_partition/CMakeFiles/__idf_esp_partition.dir

# All Build rule for target.
esp-idf/esp_partition/CMakeFiles/__idf_esp_partition.dir/all: esp-idf/efuse/CMakeFiles/__idf_efuse.dir/all
	$(MAKE) $(MAKESILENT) -f esp-idf/esp_partition/CMakeFiles/__idf_esp_partition.dir/build.make esp-idf/esp_partition/CMakeFiles/__idf_esp_partition.dir/depend
	$(MAKE) $(MAKESILENT) -f esp-idf/esp_partition/CMakeFiles/__idf_esp_partition.dir/build.make esp-idf/esp_partition/CMakeFiles/__idf_esp_partition.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles --progress-num=25 "Built target __idf_esp_partition"
.PHONY : esp-idf/esp_partition/CMakeFiles/__idf_esp_partition.dir/all

# Build rule for subdir invocation for target.
esp-idf/esp_partition/CMakeFiles/__idf_esp_partition.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles 39
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 esp-idf/esp_partition/CMakeFiles/__idf_esp_partition.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles 0
.PHONY : esp-idf/esp_partition/CMakeFiles/__idf_esp_partition.dir/rule

# Convenience name for target.
__idf_esp_partition: esp-idf/esp_partition/CMakeFiles/__idf_esp_partition.dir/rule
.PHONY : __idf_esp_partition

# clean rule for target.
esp-idf/esp_partition/CMakeFiles/__idf_esp_partition.dir/clean:
	$(MAKE) $(MAKESILENT) -f esp-idf/esp_partition/CMakeFiles/__idf_esp_partition.dir/build.make esp-idf/esp_partition/CMakeFiles/__idf_esp_partition.dir/clean
.PHONY : esp-idf/esp_partition/CMakeFiles/__idf_esp_partition.dir/clean

#=============================================================================
# Target rules for target esp-idf/efuse/CMakeFiles/__idf_efuse.dir

# All Build rule for target.
esp-idf/efuse/CMakeFiles/__idf_efuse.dir/all: esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/all
	$(MAKE) $(MAKESILENT) -f esp-idf/efuse/CMakeFiles/__idf_efuse.dir/build.make esp-idf/efuse/CMakeFiles/__idf_efuse.dir/depend
	$(MAKE) $(MAKESILENT) -f esp-idf/efuse/CMakeFiles/__idf_efuse.dir/build.make esp-idf/efuse/CMakeFiles/__idf_efuse.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles --progress-num= "Built target __idf_efuse"
.PHONY : esp-idf/efuse/CMakeFiles/__idf_efuse.dir/all

# Build rule for subdir invocation for target.
esp-idf/efuse/CMakeFiles/__idf_efuse.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles 38
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 esp-idf/efuse/CMakeFiles/__idf_efuse.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles 0
.PHONY : esp-idf/efuse/CMakeFiles/__idf_efuse.dir/rule

# Convenience name for target.
__idf_efuse: esp-idf/efuse/CMakeFiles/__idf_efuse.dir/rule
.PHONY : __idf_efuse

# clean rule for target.
esp-idf/efuse/CMakeFiles/__idf_efuse.dir/clean:
	$(MAKE) $(MAKESILENT) -f esp-idf/efuse/CMakeFiles/__idf_efuse.dir/build.make esp-idf/efuse/CMakeFiles/__idf_efuse.dir/clean
.PHONY : esp-idf/efuse/CMakeFiles/__idf_efuse.dir/clean

#=============================================================================
# Target rules for target esp-idf/efuse/CMakeFiles/efuse-common-table.dir

# All Build rule for target.
esp-idf/efuse/CMakeFiles/efuse-common-table.dir/all:
	$(MAKE) $(MAKESILENT) -f esp-idf/efuse/CMakeFiles/efuse-common-table.dir/build.make esp-idf/efuse/CMakeFiles/efuse-common-table.dir/depend
	$(MAKE) $(MAKESILENT) -f esp-idf/efuse/CMakeFiles/efuse-common-table.dir/build.make esp-idf/efuse/CMakeFiles/efuse-common-table.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles --progress-num= "Built target efuse-common-table"
.PHONY : esp-idf/efuse/CMakeFiles/efuse-common-table.dir/all

# Build rule for subdir invocation for target.
esp-idf/efuse/CMakeFiles/efuse-common-table.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles 0
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 esp-idf/efuse/CMakeFiles/efuse-common-table.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles 0
.PHONY : esp-idf/efuse/CMakeFiles/efuse-common-table.dir/rule

# Convenience name for target.
efuse-common-table: esp-idf/efuse/CMakeFiles/efuse-common-table.dir/rule
.PHONY : efuse-common-table

# clean rule for target.
esp-idf/efuse/CMakeFiles/efuse-common-table.dir/clean:
	$(MAKE) $(MAKESILENT) -f esp-idf/efuse/CMakeFiles/efuse-common-table.dir/build.make esp-idf/efuse/CMakeFiles/efuse-common-table.dir/clean
.PHONY : esp-idf/efuse/CMakeFiles/efuse-common-table.dir/clean

#=============================================================================
# Target rules for target esp-idf/efuse/CMakeFiles/efuse_common_table.dir

# All Build rule for target.
esp-idf/efuse/CMakeFiles/efuse_common_table.dir/all: esp-idf/efuse/CMakeFiles/efuse-common-table.dir/all
	$(MAKE) $(MAKESILENT) -f esp-idf/efuse/CMakeFiles/efuse_common_table.dir/build.make esp-idf/efuse/CMakeFiles/efuse_common_table.dir/depend
	$(MAKE) $(MAKESILENT) -f esp-idf/efuse/CMakeFiles/efuse_common_table.dir/build.make esp-idf/efuse/CMakeFiles/efuse_common_table.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles --progress-num= "Built target efuse_common_table"
.PHONY : esp-idf/efuse/CMakeFiles/efuse_common_table.dir/all

# Build rule for subdir invocation for target.
esp-idf/efuse/CMakeFiles/efuse_common_table.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles 0
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 esp-idf/efuse/CMakeFiles/efuse_common_table.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles 0
.PHONY : esp-idf/efuse/CMakeFiles/efuse_common_table.dir/rule

# Convenience name for target.
efuse_common_table: esp-idf/efuse/CMakeFiles/efuse_common_table.dir/rule
.PHONY : efuse_common_table

# clean rule for target.
esp-idf/efuse/CMakeFiles/efuse_common_table.dir/clean:
	$(MAKE) $(MAKESILENT) -f esp-idf/efuse/CMakeFiles/efuse_common_table.dir/build.make esp-idf/efuse/CMakeFiles/efuse_common_table.dir/clean
.PHONY : esp-idf/efuse/CMakeFiles/efuse_common_table.dir/clean

#=============================================================================
# Target rules for target esp-idf/efuse/CMakeFiles/efuse-custom-table.dir

# All Build rule for target.
esp-idf/efuse/CMakeFiles/efuse-custom-table.dir/all:
	$(MAKE) $(MAKESILENT) -f esp-idf/efuse/CMakeFiles/efuse-custom-table.dir/build.make esp-idf/efuse/CMakeFiles/efuse-custom-table.dir/depend
	$(MAKE) $(MAKESILENT) -f esp-idf/efuse/CMakeFiles/efuse-custom-table.dir/build.make esp-idf/efuse/CMakeFiles/efuse-custom-table.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles --progress-num= "Built target efuse-custom-table"
.PHONY : esp-idf/efuse/CMakeFiles/efuse-custom-table.dir/all

# Build rule for subdir invocation for target.
esp-idf/efuse/CMakeFiles/efuse-custom-table.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles 0
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 esp-idf/efuse/CMakeFiles/efuse-custom-table.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles 0
.PHONY : esp-idf/efuse/CMakeFiles/efuse-custom-table.dir/rule

# Convenience name for target.
efuse-custom-table: esp-idf/efuse/CMakeFiles/efuse-custom-table.dir/rule
.PHONY : efuse-custom-table

# clean rule for target.
esp-idf/efuse/CMakeFiles/efuse-custom-table.dir/clean:
	$(MAKE) $(MAKESILENT) -f esp-idf/efuse/CMakeFiles/efuse-custom-table.dir/build.make esp-idf/efuse/CMakeFiles/efuse-custom-table.dir/clean
.PHONY : esp-idf/efuse/CMakeFiles/efuse-custom-table.dir/clean

#=============================================================================
# Target rules for target esp-idf/efuse/CMakeFiles/efuse_custom_table.dir

# All Build rule for target.
esp-idf/efuse/CMakeFiles/efuse_custom_table.dir/all: esp-idf/efuse/CMakeFiles/efuse-custom-table.dir/all
	$(MAKE) $(MAKESILENT) -f esp-idf/efuse/CMakeFiles/efuse_custom_table.dir/build.make esp-idf/efuse/CMakeFiles/efuse_custom_table.dir/depend
	$(MAKE) $(MAKESILENT) -f esp-idf/efuse/CMakeFiles/efuse_custom_table.dir/build.make esp-idf/efuse/CMakeFiles/efuse_custom_table.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles --progress-num= "Built target efuse_custom_table"
.PHONY : esp-idf/efuse/CMakeFiles/efuse_custom_table.dir/all

# Build rule for subdir invocation for target.
esp-idf/efuse/CMakeFiles/efuse_custom_table.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles 0
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 esp-idf/efuse/CMakeFiles/efuse_custom_table.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles 0
.PHONY : esp-idf/efuse/CMakeFiles/efuse_custom_table.dir/rule

# Convenience name for target.
efuse_custom_table: esp-idf/efuse/CMakeFiles/efuse_custom_table.dir/rule
.PHONY : efuse_custom_table

# clean rule for target.
esp-idf/efuse/CMakeFiles/efuse_custom_table.dir/clean:
	$(MAKE) $(MAKESILENT) -f esp-idf/efuse/CMakeFiles/efuse_custom_table.dir/build.make esp-idf/efuse/CMakeFiles/efuse_custom_table.dir/clean
.PHONY : esp-idf/efuse/CMakeFiles/efuse_custom_table.dir/clean

#=============================================================================
# Target rules for target esp-idf/efuse/CMakeFiles/show-efuse-table.dir

# All Build rule for target.
esp-idf/efuse/CMakeFiles/show-efuse-table.dir/all:
	$(MAKE) $(MAKESILENT) -f esp-idf/efuse/CMakeFiles/show-efuse-table.dir/build.make esp-idf/efuse/CMakeFiles/show-efuse-table.dir/depend
	$(MAKE) $(MAKESILENT) -f esp-idf/efuse/CMakeFiles/show-efuse-table.dir/build.make esp-idf/efuse/CMakeFiles/show-efuse-table.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles --progress-num= "Built target show-efuse-table"
.PHONY : esp-idf/efuse/CMakeFiles/show-efuse-table.dir/all

# Build rule for subdir invocation for target.
esp-idf/efuse/CMakeFiles/show-efuse-table.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles 0
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 esp-idf/efuse/CMakeFiles/show-efuse-table.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles 0
.PHONY : esp-idf/efuse/CMakeFiles/show-efuse-table.dir/rule

# Convenience name for target.
show-efuse-table: esp-idf/efuse/CMakeFiles/show-efuse-table.dir/rule
.PHONY : show-efuse-table

# clean rule for target.
esp-idf/efuse/CMakeFiles/show-efuse-table.dir/clean:
	$(MAKE) $(MAKESILENT) -f esp-idf/efuse/CMakeFiles/show-efuse-table.dir/build.make esp-idf/efuse/CMakeFiles/show-efuse-table.dir/clean
.PHONY : esp-idf/efuse/CMakeFiles/show-efuse-table.dir/clean

#=============================================================================
# Target rules for target esp-idf/efuse/CMakeFiles/show_efuse_table.dir

# All Build rule for target.
esp-idf/efuse/CMakeFiles/show_efuse_table.dir/all: esp-idf/efuse/CMakeFiles/show-efuse-table.dir/all
	$(MAKE) $(MAKESILENT) -f esp-idf/efuse/CMakeFiles/show_efuse_table.dir/build.make esp-idf/efuse/CMakeFiles/show_efuse_table.dir/depend
	$(MAKE) $(MAKESILENT) -f esp-idf/efuse/CMakeFiles/show_efuse_table.dir/build.make esp-idf/efuse/CMakeFiles/show_efuse_table.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles --progress-num= "Built target show_efuse_table"
.PHONY : esp-idf/efuse/CMakeFiles/show_efuse_table.dir/all

# Build rule for subdir invocation for target.
esp-idf/efuse/CMakeFiles/show_efuse_table.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles 0
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 esp-idf/efuse/CMakeFiles/show_efuse_table.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles 0
.PHONY : esp-idf/efuse/CMakeFiles/show_efuse_table.dir/rule

# Convenience name for target.
show_efuse_table: esp-idf/efuse/CMakeFiles/show_efuse_table.dir/rule
.PHONY : show_efuse_table

# clean rule for target.
esp-idf/efuse/CMakeFiles/show_efuse_table.dir/clean:
	$(MAKE) $(MAKESILENT) -f esp-idf/efuse/CMakeFiles/show_efuse_table.dir/build.make esp-idf/efuse/CMakeFiles/show_efuse_table.dir/clean
.PHONY : esp-idf/efuse/CMakeFiles/show_efuse_table.dir/clean

#=============================================================================
# Target rules for target esp-idf/efuse/CMakeFiles/efuse_test_table.dir

# All Build rule for target.
esp-idf/efuse/CMakeFiles/efuse_test_table.dir/all:
	$(MAKE) $(MAKESILENT) -f esp-idf/efuse/CMakeFiles/efuse_test_table.dir/build.make esp-idf/efuse/CMakeFiles/efuse_test_table.dir/depend
	$(MAKE) $(MAKESILENT) -f esp-idf/efuse/CMakeFiles/efuse_test_table.dir/build.make esp-idf/efuse/CMakeFiles/efuse_test_table.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles --progress-num= "Built target efuse_test_table"
.PHONY : esp-idf/efuse/CMakeFiles/efuse_test_table.dir/all

# Build rule for subdir invocation for target.
esp-idf/efuse/CMakeFiles/efuse_test_table.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles 0
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 esp-idf/efuse/CMakeFiles/efuse_test_table.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles 0
.PHONY : esp-idf/efuse/CMakeFiles/efuse_test_table.dir/rule

# Convenience name for target.
efuse_test_table: esp-idf/efuse/CMakeFiles/efuse_test_table.dir/rule
.PHONY : efuse_test_table

# clean rule for target.
esp-idf/efuse/CMakeFiles/efuse_test_table.dir/clean:
	$(MAKE) $(MAKESILENT) -f esp-idf/efuse/CMakeFiles/efuse_test_table.dir/build.make esp-idf/efuse/CMakeFiles/efuse_test_table.dir/clean
.PHONY : esp-idf/efuse/CMakeFiles/efuse_test_table.dir/clean

#=============================================================================
# Target rules for target esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir

# All Build rule for target.
esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/all: esp-idf/esp_mm/CMakeFiles/__idf_esp_mm.dir/all
	$(MAKE) $(MAKESILENT) -f esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/build.make esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/depend
	$(MAKE) $(MAKESILENT) -f esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/build.make esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles --progress-num=10 "Built target __idf_bootloader_support"
.PHONY : esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/all

# Build rule for subdir invocation for target.
esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles 38
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles 0
.PHONY : esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/rule

# Convenience name for target.
__idf_bootloader_support: esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/rule
.PHONY : __idf_bootloader_support

# clean rule for target.
esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/clean:
	$(MAKE) $(MAKESILENT) -f esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/build.make esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/clean
.PHONY : esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/clean

#=============================================================================
# Target rules for target esp-idf/esp_mm/CMakeFiles/__idf_esp_mm.dir

# All Build rule for target.
esp-idf/esp_mm/CMakeFiles/__idf_esp_mm.dir/all: esp-idf/spi_flash/CMakeFiles/__idf_spi_flash.dir/all
	$(MAKE) $(MAKESILENT) -f esp-idf/esp_mm/CMakeFiles/__idf_esp_mm.dir/build.make esp-idf/esp_mm/CMakeFiles/__idf_esp_mm.dir/depend
	$(MAKE) $(MAKESILENT) -f esp-idf/esp_mm/CMakeFiles/__idf_esp_mm.dir/build.make esp-idf/esp_mm/CMakeFiles/__idf_esp_mm.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles --progress-num= "Built target __idf_esp_mm"
.PHONY : esp-idf/esp_mm/CMakeFiles/__idf_esp_mm.dir/all

# Build rule for subdir invocation for target.
esp-idf/esp_mm/CMakeFiles/__idf_esp_mm.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles 37
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 esp-idf/esp_mm/CMakeFiles/__idf_esp_mm.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles 0
.PHONY : esp-idf/esp_mm/CMakeFiles/__idf_esp_mm.dir/rule

# Convenience name for target.
__idf_esp_mm: esp-idf/esp_mm/CMakeFiles/__idf_esp_mm.dir/rule
.PHONY : __idf_esp_mm

# clean rule for target.
esp-idf/esp_mm/CMakeFiles/__idf_esp_mm.dir/clean:
	$(MAKE) $(MAKESILENT) -f esp-idf/esp_mm/CMakeFiles/__idf_esp_mm.dir/build.make esp-idf/esp_mm/CMakeFiles/__idf_esp_mm.dir/clean
.PHONY : esp-idf/esp_mm/CMakeFiles/__idf_esp_mm.dir/clean

#=============================================================================
# Target rules for target esp-idf/spi_flash/CMakeFiles/__idf_spi_flash.dir

# All Build rule for target.
esp-idf/spi_flash/CMakeFiles/__idf_spi_flash.dir/all: esp-idf/esp_system/CMakeFiles/__idf_esp_system.dir/all
	$(MAKE) $(MAKESILENT) -f esp-idf/spi_flash/CMakeFiles/__idf_spi_flash.dir/build.make esp-idf/spi_flash/CMakeFiles/__idf_spi_flash.dir/depend
	$(MAKE) $(MAKESILENT) -f esp-idf/spi_flash/CMakeFiles/__idf_spi_flash.dir/build.make esp-idf/spi_flash/CMakeFiles/__idf_spi_flash.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles --progress-num=81 "Built target __idf_spi_flash"
.PHONY : esp-idf/spi_flash/CMakeFiles/__idf_spi_flash.dir/all

# Build rule for subdir invocation for target.
esp-idf/spi_flash/CMakeFiles/__idf_spi_flash.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles 37
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 esp-idf/spi_flash/CMakeFiles/__idf_spi_flash.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles 0
.PHONY : esp-idf/spi_flash/CMakeFiles/__idf_spi_flash.dir/rule

# Convenience name for target.
__idf_spi_flash: esp-idf/spi_flash/CMakeFiles/__idf_spi_flash.dir/rule
.PHONY : __idf_spi_flash

# clean rule for target.
esp-idf/spi_flash/CMakeFiles/__idf_spi_flash.dir/clean:
	$(MAKE) $(MAKESILENT) -f esp-idf/spi_flash/CMakeFiles/__idf_spi_flash.dir/build.make esp-idf/spi_flash/CMakeFiles/__idf_spi_flash.dir/clean
.PHONY : esp-idf/spi_flash/CMakeFiles/__idf_spi_flash.dir/clean

#=============================================================================
# Target rules for target esp-idf/esp_system/CMakeFiles/__idf_esp_system.dir

# All Build rule for target.
esp-idf/esp_system/CMakeFiles/__idf_esp_system.dir/all: esp-idf/esp_common/CMakeFiles/__idf_esp_common.dir/all
	$(MAKE) $(MAKESILENT) -f esp-idf/esp_system/CMakeFiles/__idf_esp_system.dir/build.make esp-idf/esp_system/CMakeFiles/__idf_esp_system.dir/depend
	$(MAKE) $(MAKESILENT) -f esp-idf/esp_system/CMakeFiles/__idf_esp_system.dir/build.make esp-idf/esp_system/CMakeFiles/__idf_esp_system.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles --progress-num=27,28 "Built target __idf_esp_system"
.PHONY : esp-idf/esp_system/CMakeFiles/__idf_esp_system.dir/all

# Build rule for subdir invocation for target.
esp-idf/esp_system/CMakeFiles/__idf_esp_system.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles 36
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 esp-idf/esp_system/CMakeFiles/__idf_esp_system.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles 0
.PHONY : esp-idf/esp_system/CMakeFiles/__idf_esp_system.dir/rule

# Convenience name for target.
__idf_esp_system: esp-idf/esp_system/CMakeFiles/__idf_esp_system.dir/rule
.PHONY : __idf_esp_system

# clean rule for target.
esp-idf/esp_system/CMakeFiles/__idf_esp_system.dir/clean:
	$(MAKE) $(MAKESILENT) -f esp-idf/esp_system/CMakeFiles/__idf_esp_system.dir/build.make esp-idf/esp_system/CMakeFiles/__idf_esp_system.dir/clean
.PHONY : esp-idf/esp_system/CMakeFiles/__idf_esp_system.dir/clean

#=============================================================================
# Target rules for target esp-idf/esp_system/CMakeFiles/memory.ld.dir

# All Build rule for target.
esp-idf/esp_system/CMakeFiles/memory.ld.dir/all:
	$(MAKE) $(MAKESILENT) -f esp-idf/esp_system/CMakeFiles/memory.ld.dir/build.make esp-idf/esp_system/CMakeFiles/memory.ld.dir/depend
	$(MAKE) $(MAKESILENT) -f esp-idf/esp_system/CMakeFiles/memory.ld.dir/build.make esp-idf/esp_system/CMakeFiles/memory.ld.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles --progress-num= "Built target memory.ld"
.PHONY : esp-idf/esp_system/CMakeFiles/memory.ld.dir/all

# Build rule for subdir invocation for target.
esp-idf/esp_system/CMakeFiles/memory.ld.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles 0
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 esp-idf/esp_system/CMakeFiles/memory.ld.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles 0
.PHONY : esp-idf/esp_system/CMakeFiles/memory.ld.dir/rule

# Convenience name for target.
memory.ld: esp-idf/esp_system/CMakeFiles/memory.ld.dir/rule
.PHONY : memory.ld

# clean rule for target.
esp-idf/esp_system/CMakeFiles/memory.ld.dir/clean:
	$(MAKE) $(MAKESILENT) -f esp-idf/esp_system/CMakeFiles/memory.ld.dir/build.make esp-idf/esp_system/CMakeFiles/memory.ld.dir/clean
.PHONY : esp-idf/esp_system/CMakeFiles/memory.ld.dir/clean

#=============================================================================
# Target rules for target esp-idf/esp_system/CMakeFiles/sections.ld.in.dir

# All Build rule for target.
esp-idf/esp_system/CMakeFiles/sections.ld.in.dir/all:
	$(MAKE) $(MAKESILENT) -f esp-idf/esp_system/CMakeFiles/sections.ld.in.dir/build.make esp-idf/esp_system/CMakeFiles/sections.ld.in.dir/depend
	$(MAKE) $(MAKESILENT) -f esp-idf/esp_system/CMakeFiles/sections.ld.in.dir/build.make esp-idf/esp_system/CMakeFiles/sections.ld.in.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles --progress-num= "Built target sections.ld.in"
.PHONY : esp-idf/esp_system/CMakeFiles/sections.ld.in.dir/all

# Build rule for subdir invocation for target.
esp-idf/esp_system/CMakeFiles/sections.ld.in.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles 0
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 esp-idf/esp_system/CMakeFiles/sections.ld.in.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles 0
.PHONY : esp-idf/esp_system/CMakeFiles/sections.ld.in.dir/rule

# Convenience name for target.
sections.ld.in: esp-idf/esp_system/CMakeFiles/sections.ld.in.dir/rule
.PHONY : sections.ld.in

# clean rule for target.
esp-idf/esp_system/CMakeFiles/sections.ld.in.dir/clean:
	$(MAKE) $(MAKESILENT) -f esp-idf/esp_system/CMakeFiles/sections.ld.in.dir/build.make esp-idf/esp_system/CMakeFiles/sections.ld.in.dir/clean
.PHONY : esp-idf/esp_system/CMakeFiles/sections.ld.in.dir/clean

#=============================================================================
# Target rules for target esp-idf/esp_system/CMakeFiles/__ldgen_output_sections.ld.dir

# All Build rule for target.
esp-idf/esp_system/CMakeFiles/__ldgen_output_sections.ld.dir/all: esp-idf/xtensa/CMakeFiles/__idf_xtensa.dir/all
esp-idf/esp_system/CMakeFiles/__ldgen_output_sections.ld.dir/all: esp-idf/app_trace/CMakeFiles/__idf_app_trace.dir/all
esp-idf/esp_system/CMakeFiles/__ldgen_output_sections.ld.dir/all: esp-idf/unity/CMakeFiles/__idf_unity.dir/all
esp-idf/esp_system/CMakeFiles/__ldgen_output_sections.ld.dir/all: esp-idf/cmock/CMakeFiles/__idf_cmock.dir/all
esp-idf/esp_system/CMakeFiles/__ldgen_output_sections.ld.dir/all: esp-idf/console/CMakeFiles/__idf_console.dir/all
esp-idf/esp_system/CMakeFiles/__ldgen_output_sections.ld.dir/all: esp-idf/esp_driver_cam/CMakeFiles/__idf_esp_driver_cam.dir/all
esp-idf/esp_system/CMakeFiles/__ldgen_output_sections.ld.dir/all: esp-idf/esp_hid/CMakeFiles/__idf_esp_hid.dir/all
esp-idf/esp_system/CMakeFiles/__ldgen_output_sections.ld.dir/all: esp-idf/esp_https_server/CMakeFiles/__idf_esp_https_server.dir/all
esp-idf/esp_system/CMakeFiles/__ldgen_output_sections.ld.dir/all: esp-idf/esp_lcd/CMakeFiles/__idf_esp_lcd.dir/all
esp-idf/esp_system/CMakeFiles/__ldgen_output_sections.ld.dir/all: esp-idf/protobuf-c/CMakeFiles/__idf_protobuf-c.dir/all
esp-idf/esp_system/CMakeFiles/__ldgen_output_sections.ld.dir/all: esp-idf/protocomm/CMakeFiles/__idf_protocomm.dir/all
esp-idf/esp_system/CMakeFiles/__ldgen_output_sections.ld.dir/all: esp-idf/esp_local_ctrl/CMakeFiles/__idf_esp_local_ctrl.dir/all
esp-idf/esp_system/CMakeFiles/__ldgen_output_sections.ld.dir/all: esp-idf/espcoredump/CMakeFiles/__idf_espcoredump.dir/all
esp-idf/esp_system/CMakeFiles/__ldgen_output_sections.ld.dir/all: esp-idf/wear_levelling/CMakeFiles/__idf_wear_levelling.dir/all
esp-idf/esp_system/CMakeFiles/__ldgen_output_sections.ld.dir/all: esp-idf/fatfs/CMakeFiles/__idf_fatfs.dir/all
esp-idf/esp_system/CMakeFiles/__ldgen_output_sections.ld.dir/all: esp-idf/json/CMakeFiles/__idf_json.dir/all
esp-idf/esp_system/CMakeFiles/__ldgen_output_sections.ld.dir/all: esp-idf/mqtt/CMakeFiles/__idf_mqtt.dir/all
esp-idf/esp_system/CMakeFiles/__ldgen_output_sections.ld.dir/all: esp-idf/nvs_sec_provider/CMakeFiles/__idf_nvs_sec_provider.dir/all
esp-idf/esp_system/CMakeFiles/__ldgen_output_sections.ld.dir/all: esp-idf/perfmon/CMakeFiles/__idf_perfmon.dir/all
esp-idf/esp_system/CMakeFiles/__ldgen_output_sections.ld.dir/all: esp-idf/spiffs/CMakeFiles/__idf_spiffs.dir/all
esp-idf/esp_system/CMakeFiles/__ldgen_output_sections.ld.dir/all: esp-idf/touch_element/CMakeFiles/__idf_touch_element.dir/all
esp-idf/esp_system/CMakeFiles/__ldgen_output_sections.ld.dir/all: esp-idf/usb/CMakeFiles/__idf_usb.dir/all
esp-idf/esp_system/CMakeFiles/__ldgen_output_sections.ld.dir/all: esp-idf/wifi_provisioning/CMakeFiles/__idf_wifi_provisioning.dir/all
esp-idf/esp_system/CMakeFiles/__ldgen_output_sections.ld.dir/all: esp-idf/78__esp-ml307/CMakeFiles/__idf_78__esp-ml307.dir/all
esp-idf/esp_system/CMakeFiles/__ldgen_output_sections.ld.dir/all: esp-idf/78__esp-opus/CMakeFiles/__idf_78__esp-opus.dir/all
esp-idf/esp_system/CMakeFiles/__ldgen_output_sections.ld.dir/all: esp-idf/78__esp-opus-encoder/CMakeFiles/__idf_78__esp-opus-encoder.dir/all
esp-idf/esp_system/CMakeFiles/__ldgen_output_sections.ld.dir/all: esp-idf/78__esp-wifi-connect/CMakeFiles/__idf_78__esp-wifi-connect.dir/all
esp-idf/esp_system/CMakeFiles/__ldgen_output_sections.ld.dir/all: esp-idf/espressif__button/CMakeFiles/__idf_espressif__button.dir/all
esp-idf/esp_system/CMakeFiles/__ldgen_output_sections.ld.dir/all: esp-idf/espressif__esp-dsp/CMakeFiles/__idf_espressif__esp-dsp.dir/all
esp-idf/esp_system/CMakeFiles/__ldgen_output_sections.ld.dir/all: esp-idf/espressif__esp-sr/CMakeFiles/__idf_espressif__esp-sr.dir/all
esp-idf/esp_system/CMakeFiles/__ldgen_output_sections.ld.dir/all: esp-idf/espressif__esp_codec_dev/CMakeFiles/__idf_espressif__esp_codec_dev.dir/all
esp-idf/esp_system/CMakeFiles/__ldgen_output_sections.ld.dir/all: esp-idf/lvgl__lvgl/CMakeFiles/__idf_lvgl__lvgl.dir/all
esp-idf/esp_system/CMakeFiles/__ldgen_output_sections.ld.dir/all: esp-idf/espressif__led_strip/CMakeFiles/__idf_espressif__led_strip.dir/all
esp-idf/esp_system/CMakeFiles/__ldgen_output_sections.ld.dir/all: esp-idf/main/CMakeFiles/__idf_main.dir/all
	$(MAKE) $(MAKESILENT) -f esp-idf/esp_system/CMakeFiles/__ldgen_output_sections.ld.dir/build.make esp-idf/esp_system/CMakeFiles/__ldgen_output_sections.ld.dir/depend
	$(MAKE) $(MAKESILENT) -f esp-idf/esp_system/CMakeFiles/__ldgen_output_sections.ld.dir/build.make esp-idf/esp_system/CMakeFiles/__ldgen_output_sections.ld.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles --progress-num= "Built target __ldgen_output_sections.ld"
.PHONY : esp-idf/esp_system/CMakeFiles/__ldgen_output_sections.ld.dir/all

# Build rule for subdir invocation for target.
esp-idf/esp_system/CMakeFiles/__ldgen_output_sections.ld.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles 99
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 esp-idf/esp_system/CMakeFiles/__ldgen_output_sections.ld.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles 0
.PHONY : esp-idf/esp_system/CMakeFiles/__ldgen_output_sections.ld.dir/rule

# Convenience name for target.
__ldgen_output_sections.ld: esp-idf/esp_system/CMakeFiles/__ldgen_output_sections.ld.dir/rule
.PHONY : __ldgen_output_sections.ld

# clean rule for target.
esp-idf/esp_system/CMakeFiles/__ldgen_output_sections.ld.dir/clean:
	$(MAKE) $(MAKESILENT) -f esp-idf/esp_system/CMakeFiles/__ldgen_output_sections.ld.dir/build.make esp-idf/esp_system/CMakeFiles/__ldgen_output_sections.ld.dir/clean
.PHONY : esp-idf/esp_system/CMakeFiles/__ldgen_output_sections.ld.dir/clean

#=============================================================================
# Target rules for target esp-idf/esp_common/CMakeFiles/__idf_esp_common.dir

# All Build rule for target.
esp-idf/esp_common/CMakeFiles/__idf_esp_common.dir/all: esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/all
	$(MAKE) $(MAKESILENT) -f esp-idf/esp_common/CMakeFiles/__idf_esp_common.dir/build.make esp-idf/esp_common/CMakeFiles/__idf_esp_common.dir/depend
	$(MAKE) $(MAKESILENT) -f esp-idf/esp_common/CMakeFiles/__idf_esp_common.dir/build.make esp-idf/esp_common/CMakeFiles/__idf_esp_common.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles --progress-num= "Built target __idf_esp_common"
.PHONY : esp-idf/esp_common/CMakeFiles/__idf_esp_common.dir/all

# Build rule for subdir invocation for target.
esp-idf/esp_common/CMakeFiles/__idf_esp_common.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles 34
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 esp-idf/esp_common/CMakeFiles/__idf_esp_common.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles 0
.PHONY : esp-idf/esp_common/CMakeFiles/__idf_esp_common.dir/rule

# Convenience name for target.
__idf_esp_common: esp-idf/esp_common/CMakeFiles/__idf_esp_common.dir/rule
.PHONY : __idf_esp_common

# clean rule for target.
esp-idf/esp_common/CMakeFiles/__idf_esp_common.dir/clean:
	$(MAKE) $(MAKESILENT) -f esp-idf/esp_common/CMakeFiles/__idf_esp_common.dir/build.make esp-idf/esp_common/CMakeFiles/__idf_esp_common.dir/clean
.PHONY : esp-idf/esp_common/CMakeFiles/__idf_esp_common.dir/clean

#=============================================================================
# Target rules for target esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir

# All Build rule for target.
esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/all: esp-idf/hal/CMakeFiles/__idf_hal.dir/all
	$(MAKE) $(MAKESILENT) -f esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/build.make esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/depend
	$(MAKE) $(MAKESILENT) -f esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/build.make esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles --progress-num= "Built target __idf_esp_rom"
.PHONY : esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/all

# Build rule for subdir invocation for target.
esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles 34
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles 0
.PHONY : esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/rule

# Convenience name for target.
__idf_esp_rom: esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/rule
.PHONY : __idf_esp_rom

# clean rule for target.
esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/clean:
	$(MAKE) $(MAKESILENT) -f esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/build.make esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/clean
.PHONY : esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/clean

#=============================================================================
# Target rules for target esp-idf/hal/CMakeFiles/__idf_hal.dir

# All Build rule for target.
esp-idf/hal/CMakeFiles/__idf_hal.dir/all: esp-idf/log/CMakeFiles/__idf_log.dir/all
	$(MAKE) $(MAKESILENT) -f esp-idf/hal/CMakeFiles/__idf_hal.dir/build.make esp-idf/hal/CMakeFiles/__idf_hal.dir/depend
	$(MAKE) $(MAKESILENT) -f esp-idf/hal/CMakeFiles/__idf_hal.dir/build.make esp-idf/hal/CMakeFiles/__idf_hal.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles --progress-num=42,43,44 "Built target __idf_hal"
.PHONY : esp-idf/hal/CMakeFiles/__idf_hal.dir/all

# Build rule for subdir invocation for target.
esp-idf/hal/CMakeFiles/__idf_hal.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles 34
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 esp-idf/hal/CMakeFiles/__idf_hal.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles 0
.PHONY : esp-idf/hal/CMakeFiles/__idf_hal.dir/rule

# Convenience name for target.
__idf_hal: esp-idf/hal/CMakeFiles/__idf_hal.dir/rule
.PHONY : __idf_hal

# clean rule for target.
esp-idf/hal/CMakeFiles/__idf_hal.dir/clean:
	$(MAKE) $(MAKESILENT) -f esp-idf/hal/CMakeFiles/__idf_hal.dir/build.make esp-idf/hal/CMakeFiles/__idf_hal.dir/clean
.PHONY : esp-idf/hal/CMakeFiles/__idf_hal.dir/clean

#=============================================================================
# Target rules for target esp-idf/log/CMakeFiles/__idf_log.dir

# All Build rule for target.
esp-idf/log/CMakeFiles/__idf_log.dir/all: esp-idf/heap/CMakeFiles/__idf_heap.dir/all
	$(MAKE) $(MAKESILENT) -f esp-idf/log/CMakeFiles/__idf_log.dir/build.make esp-idf/log/CMakeFiles/__idf_log.dir/depend
	$(MAKE) $(MAKESILENT) -f esp-idf/log/CMakeFiles/__idf_log.dir/build.make esp-idf/log/CMakeFiles/__idf_log.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles --progress-num= "Built target __idf_log"
.PHONY : esp-idf/log/CMakeFiles/__idf_log.dir/all

# Build rule for subdir invocation for target.
esp-idf/log/CMakeFiles/__idf_log.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles 31
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 esp-idf/log/CMakeFiles/__idf_log.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles 0
.PHONY : esp-idf/log/CMakeFiles/__idf_log.dir/rule

# Convenience name for target.
__idf_log: esp-idf/log/CMakeFiles/__idf_log.dir/rule
.PHONY : __idf_log

# clean rule for target.
esp-idf/log/CMakeFiles/__idf_log.dir/clean:
	$(MAKE) $(MAKESILENT) -f esp-idf/log/CMakeFiles/__idf_log.dir/build.make esp-idf/log/CMakeFiles/__idf_log.dir/clean
.PHONY : esp-idf/log/CMakeFiles/__idf_log.dir/clean

#=============================================================================
# Target rules for target esp-idf/heap/CMakeFiles/__idf_heap.dir

# All Build rule for target.
esp-idf/heap/CMakeFiles/__idf_heap.dir/all: esp-idf/soc/CMakeFiles/__idf_soc.dir/all
	$(MAKE) $(MAKESILENT) -f esp-idf/heap/CMakeFiles/__idf_heap.dir/build.make esp-idf/heap/CMakeFiles/__idf_heap.dir/depend
	$(MAKE) $(MAKESILENT) -f esp-idf/heap/CMakeFiles/__idf_heap.dir/build.make esp-idf/heap/CMakeFiles/__idf_heap.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles --progress-num=45 "Built target __idf_heap"
.PHONY : esp-idf/heap/CMakeFiles/__idf_heap.dir/all

# Build rule for subdir invocation for target.
esp-idf/heap/CMakeFiles/__idf_heap.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles 31
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 esp-idf/heap/CMakeFiles/__idf_heap.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles 0
.PHONY : esp-idf/heap/CMakeFiles/__idf_heap.dir/rule

# Convenience name for target.
__idf_heap: esp-idf/heap/CMakeFiles/__idf_heap.dir/rule
.PHONY : __idf_heap

# clean rule for target.
esp-idf/heap/CMakeFiles/__idf_heap.dir/clean:
	$(MAKE) $(MAKESILENT) -f esp-idf/heap/CMakeFiles/__idf_heap.dir/build.make esp-idf/heap/CMakeFiles/__idf_heap.dir/clean
.PHONY : esp-idf/heap/CMakeFiles/__idf_heap.dir/clean

#=============================================================================
# Target rules for target esp-idf/soc/CMakeFiles/__idf_soc.dir

# All Build rule for target.
esp-idf/soc/CMakeFiles/__idf_soc.dir/all: esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/all
	$(MAKE) $(MAKESILENT) -f esp-idf/soc/CMakeFiles/__idf_soc.dir/build.make esp-idf/soc/CMakeFiles/__idf_soc.dir/depend
	$(MAKE) $(MAKESILENT) -f esp-idf/soc/CMakeFiles/__idf_soc.dir/build.make esp-idf/soc/CMakeFiles/__idf_soc.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles --progress-num=79,80 "Built target __idf_soc"
.PHONY : esp-idf/soc/CMakeFiles/__idf_soc.dir/all

# Build rule for subdir invocation for target.
esp-idf/soc/CMakeFiles/__idf_soc.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles 30
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 esp-idf/soc/CMakeFiles/__idf_soc.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles 0
.PHONY : esp-idf/soc/CMakeFiles/__idf_soc.dir/rule

# Convenience name for target.
__idf_soc: esp-idf/soc/CMakeFiles/__idf_soc.dir/rule
.PHONY : __idf_soc

# clean rule for target.
esp-idf/soc/CMakeFiles/__idf_soc.dir/clean:
	$(MAKE) $(MAKESILENT) -f esp-idf/soc/CMakeFiles/__idf_soc.dir/build.make esp-idf/soc/CMakeFiles/__idf_soc.dir/clean
.PHONY : esp-idf/soc/CMakeFiles/__idf_soc.dir/clean

#=============================================================================
# Target rules for target esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir

# All Build rule for target.
esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/all: esp-idf/freertos/CMakeFiles/__idf_freertos.dir/all
	$(MAKE) $(MAKESILENT) -f esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/build.make esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/depend
	$(MAKE) $(MAKESILENT) -f esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/build.make esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles --progress-num=21,22,23 "Built target __idf_esp_hw_support"
.PHONY : esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/all

# Build rule for subdir invocation for target.
esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles 28
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles 0
.PHONY : esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/rule

# Convenience name for target.
__idf_esp_hw_support: esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/rule
.PHONY : __idf_esp_hw_support

# clean rule for target.
esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/clean:
	$(MAKE) $(MAKESILENT) -f esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/build.make esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/clean
.PHONY : esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/clean

#=============================================================================
# Target rules for target esp-idf/freertos/CMakeFiles/__idf_freertos.dir

# All Build rule for target.
esp-idf/freertos/CMakeFiles/__idf_freertos.dir/all: esp-idf/newlib/CMakeFiles/__idf_newlib.dir/all
	$(MAKE) $(MAKESILENT) -f esp-idf/freertos/CMakeFiles/__idf_freertos.dir/build.make esp-idf/freertos/CMakeFiles/__idf_freertos.dir/depend
	$(MAKE) $(MAKESILENT) -f esp-idf/freertos/CMakeFiles/__idf_freertos.dir/build.make esp-idf/freertos/CMakeFiles/__idf_freertos.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles --progress-num=41 "Built target __idf_freertos"
.PHONY : esp-idf/freertos/CMakeFiles/__idf_freertos.dir/all

# Build rule for subdir invocation for target.
esp-idf/freertos/CMakeFiles/__idf_freertos.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles 25
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 esp-idf/freertos/CMakeFiles/__idf_freertos.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles 0
.PHONY : esp-idf/freertos/CMakeFiles/__idf_freertos.dir/rule

# Convenience name for target.
__idf_freertos: esp-idf/freertos/CMakeFiles/__idf_freertos.dir/rule
.PHONY : __idf_freertos

# clean rule for target.
esp-idf/freertos/CMakeFiles/__idf_freertos.dir/clean:
	$(MAKE) $(MAKESILENT) -f esp-idf/freertos/CMakeFiles/__idf_freertos.dir/build.make esp-idf/freertos/CMakeFiles/__idf_freertos.dir/clean
.PHONY : esp-idf/freertos/CMakeFiles/__idf_freertos.dir/clean

#=============================================================================
# Target rules for target esp-idf/newlib/CMakeFiles/__idf_newlib.dir

# All Build rule for target.
esp-idf/newlib/CMakeFiles/__idf_newlib.dir/all: esp-idf/pthread/CMakeFiles/__idf_pthread.dir/all
	$(MAKE) $(MAKESILENT) -f esp-idf/newlib/CMakeFiles/__idf_newlib.dir/build.make esp-idf/newlib/CMakeFiles/__idf_newlib.dir/depend
	$(MAKE) $(MAKESILENT) -f esp-idf/newlib/CMakeFiles/__idf_newlib.dir/build.make esp-idf/newlib/CMakeFiles/__idf_newlib.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles --progress-num=75 "Built target __idf_newlib"
.PHONY : esp-idf/newlib/CMakeFiles/__idf_newlib.dir/all

# Build rule for subdir invocation for target.
esp-idf/newlib/CMakeFiles/__idf_newlib.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles 24
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 esp-idf/newlib/CMakeFiles/__idf_newlib.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles 0
.PHONY : esp-idf/newlib/CMakeFiles/__idf_newlib.dir/rule

# Convenience name for target.
__idf_newlib: esp-idf/newlib/CMakeFiles/__idf_newlib.dir/rule
.PHONY : __idf_newlib

# clean rule for target.
esp-idf/newlib/CMakeFiles/__idf_newlib.dir/clean:
	$(MAKE) $(MAKESILENT) -f esp-idf/newlib/CMakeFiles/__idf_newlib.dir/build.make esp-idf/newlib/CMakeFiles/__idf_newlib.dir/clean
.PHONY : esp-idf/newlib/CMakeFiles/__idf_newlib.dir/clean

#=============================================================================
# Target rules for target esp-idf/pthread/CMakeFiles/__idf_pthread.dir

# All Build rule for target.
esp-idf/pthread/CMakeFiles/__idf_pthread.dir/all: esp-idf/cxx/CMakeFiles/__idf_cxx.dir/all
	$(MAKE) $(MAKESILENT) -f esp-idf/pthread/CMakeFiles/__idf_pthread.dir/build.make esp-idf/pthread/CMakeFiles/__idf_pthread.dir/depend
	$(MAKE) $(MAKESILENT) -f esp-idf/pthread/CMakeFiles/__idf_pthread.dir/build.make esp-idf/pthread/CMakeFiles/__idf_pthread.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles --progress-num=78 "Built target __idf_pthread"
.PHONY : esp-idf/pthread/CMakeFiles/__idf_pthread.dir/all

# Build rule for subdir invocation for target.
esp-idf/pthread/CMakeFiles/__idf_pthread.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles 23
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 esp-idf/pthread/CMakeFiles/__idf_pthread.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles 0
.PHONY : esp-idf/pthread/CMakeFiles/__idf_pthread.dir/rule

# Convenience name for target.
__idf_pthread: esp-idf/pthread/CMakeFiles/__idf_pthread.dir/rule
.PHONY : __idf_pthread

# clean rule for target.
esp-idf/pthread/CMakeFiles/__idf_pthread.dir/clean:
	$(MAKE) $(MAKESILENT) -f esp-idf/pthread/CMakeFiles/__idf_pthread.dir/build.make esp-idf/pthread/CMakeFiles/__idf_pthread.dir/clean
.PHONY : esp-idf/pthread/CMakeFiles/__idf_pthread.dir/clean

#=============================================================================
# Target rules for target esp-idf/cxx/CMakeFiles/__idf_cxx.dir

# All Build rule for target.
esp-idf/cxx/CMakeFiles/__idf_cxx.dir/all: esp-idf/esp_timer/CMakeFiles/__idf_esp_timer.dir/all
	$(MAKE) $(MAKESILENT) -f esp-idf/cxx/CMakeFiles/__idf_cxx.dir/build.make esp-idf/cxx/CMakeFiles/__idf_cxx.dir/depend
	$(MAKE) $(MAKESILENT) -f esp-idf/cxx/CMakeFiles/__idf_cxx.dir/build.make esp-idf/cxx/CMakeFiles/__idf_cxx.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles --progress-num= "Built target __idf_cxx"
.PHONY : esp-idf/cxx/CMakeFiles/__idf_cxx.dir/all

# Build rule for subdir invocation for target.
esp-idf/cxx/CMakeFiles/__idf_cxx.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles 22
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 esp-idf/cxx/CMakeFiles/__idf_cxx.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles 0
.PHONY : esp-idf/cxx/CMakeFiles/__idf_cxx.dir/rule

# Convenience name for target.
__idf_cxx: esp-idf/cxx/CMakeFiles/__idf_cxx.dir/rule
.PHONY : __idf_cxx

# clean rule for target.
esp-idf/cxx/CMakeFiles/__idf_cxx.dir/clean:
	$(MAKE) $(MAKESILENT) -f esp-idf/cxx/CMakeFiles/__idf_cxx.dir/build.make esp-idf/cxx/CMakeFiles/__idf_cxx.dir/clean
.PHONY : esp-idf/cxx/CMakeFiles/__idf_cxx.dir/clean

#=============================================================================
# Target rules for target esp-idf/esp_timer/CMakeFiles/__idf_esp_timer.dir

# All Build rule for target.
esp-idf/esp_timer/CMakeFiles/__idf_esp_timer.dir/all: esp-idf/esp_driver_gptimer/CMakeFiles/__idf_esp_driver_gptimer.dir/all
	$(MAKE) $(MAKESILENT) -f esp-idf/esp_timer/CMakeFiles/__idf_esp_timer.dir/build.make esp-idf/esp_timer/CMakeFiles/__idf_esp_timer.dir/depend
	$(MAKE) $(MAKESILENT) -f esp-idf/esp_timer/CMakeFiles/__idf_esp_timer.dir/build.make esp-idf/esp_timer/CMakeFiles/__idf_esp_timer.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles --progress-num=29 "Built target __idf_esp_timer"
.PHONY : esp-idf/esp_timer/CMakeFiles/__idf_esp_timer.dir/all

# Build rule for subdir invocation for target.
esp-idf/esp_timer/CMakeFiles/__idf_esp_timer.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles 22
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 esp-idf/esp_timer/CMakeFiles/__idf_esp_timer.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles 0
.PHONY : esp-idf/esp_timer/CMakeFiles/__idf_esp_timer.dir/rule

# Convenience name for target.
__idf_esp_timer: esp-idf/esp_timer/CMakeFiles/__idf_esp_timer.dir/rule
.PHONY : __idf_esp_timer

# clean rule for target.
esp-idf/esp_timer/CMakeFiles/__idf_esp_timer.dir/clean:
	$(MAKE) $(MAKESILENT) -f esp-idf/esp_timer/CMakeFiles/__idf_esp_timer.dir/build.make esp-idf/esp_timer/CMakeFiles/__idf_esp_timer.dir/clean
.PHONY : esp-idf/esp_timer/CMakeFiles/__idf_esp_timer.dir/clean

#=============================================================================
# Target rules for target esp-idf/esp_driver_gptimer/CMakeFiles/__idf_esp_driver_gptimer.dir

# All Build rule for target.
esp-idf/esp_driver_gptimer/CMakeFiles/__idf_esp_driver_gptimer.dir/all: esp-idf/esp_ringbuf/CMakeFiles/__idf_esp_ringbuf.dir/all
	$(MAKE) $(MAKESILENT) -f esp-idf/esp_driver_gptimer/CMakeFiles/__idf_esp_driver_gptimer.dir/build.make esp-idf/esp_driver_gptimer/CMakeFiles/__idf_esp_driver_gptimer.dir/depend
	$(MAKE) $(MAKESILENT) -f esp-idf/esp_driver_gptimer/CMakeFiles/__idf_esp_driver_gptimer.dir/build.make esp-idf/esp_driver_gptimer/CMakeFiles/__idf_esp_driver_gptimer.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles --progress-num=15 "Built target __idf_esp_driver_gptimer"
.PHONY : esp-idf/esp_driver_gptimer/CMakeFiles/__idf_esp_driver_gptimer.dir/all

# Build rule for subdir invocation for target.
esp-idf/esp_driver_gptimer/CMakeFiles/__idf_esp_driver_gptimer.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles 21
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 esp-idf/esp_driver_gptimer/CMakeFiles/__idf_esp_driver_gptimer.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles 0
.PHONY : esp-idf/esp_driver_gptimer/CMakeFiles/__idf_esp_driver_gptimer.dir/rule

# Convenience name for target.
__idf_esp_driver_gptimer: esp-idf/esp_driver_gptimer/CMakeFiles/__idf_esp_driver_gptimer.dir/rule
.PHONY : __idf_esp_driver_gptimer

# clean rule for target.
esp-idf/esp_driver_gptimer/CMakeFiles/__idf_esp_driver_gptimer.dir/clean:
	$(MAKE) $(MAKESILENT) -f esp-idf/esp_driver_gptimer/CMakeFiles/__idf_esp_driver_gptimer.dir/build.make esp-idf/esp_driver_gptimer/CMakeFiles/__idf_esp_driver_gptimer.dir/clean
.PHONY : esp-idf/esp_driver_gptimer/CMakeFiles/__idf_esp_driver_gptimer.dir/clean

#=============================================================================
# Target rules for target esp-idf/esp_ringbuf/CMakeFiles/__idf_esp_ringbuf.dir

# All Build rule for target.
esp-idf/esp_ringbuf/CMakeFiles/__idf_esp_ringbuf.dir/all: esp-idf/esp_driver_uart/CMakeFiles/__idf_esp_driver_uart.dir/all
	$(MAKE) $(MAKESILENT) -f esp-idf/esp_ringbuf/CMakeFiles/__idf_esp_ringbuf.dir/build.make esp-idf/esp_ringbuf/CMakeFiles/__idf_esp_ringbuf.dir/depend
	$(MAKE) $(MAKESILENT) -f esp-idf/esp_ringbuf/CMakeFiles/__idf_esp_ringbuf.dir/build.make esp-idf/esp_ringbuf/CMakeFiles/__idf_esp_ringbuf.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles --progress-num=26 "Built target __idf_esp_ringbuf"
.PHONY : esp-idf/esp_ringbuf/CMakeFiles/__idf_esp_ringbuf.dir/all

# Build rule for subdir invocation for target.
esp-idf/esp_ringbuf/CMakeFiles/__idf_esp_ringbuf.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles 20
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 esp-idf/esp_ringbuf/CMakeFiles/__idf_esp_ringbuf.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles 0
.PHONY : esp-idf/esp_ringbuf/CMakeFiles/__idf_esp_ringbuf.dir/rule

# Convenience name for target.
__idf_esp_ringbuf: esp-idf/esp_ringbuf/CMakeFiles/__idf_esp_ringbuf.dir/rule
.PHONY : __idf_esp_ringbuf

# clean rule for target.
esp-idf/esp_ringbuf/CMakeFiles/__idf_esp_ringbuf.dir/clean:
	$(MAKE) $(MAKESILENT) -f esp-idf/esp_ringbuf/CMakeFiles/__idf_esp_ringbuf.dir/build.make esp-idf/esp_ringbuf/CMakeFiles/__idf_esp_ringbuf.dir/clean
.PHONY : esp-idf/esp_ringbuf/CMakeFiles/__idf_esp_ringbuf.dir/clean

#=============================================================================
# Target rules for target esp-idf/esp_driver_uart/CMakeFiles/__idf_esp_driver_uart.dir

# All Build rule for target.
esp-idf/esp_driver_uart/CMakeFiles/__idf_esp_driver_uart.dir/all: esp-idf/esp_event/CMakeFiles/__idf_esp_event.dir/all
	$(MAKE) $(MAKESILENT) -f esp-idf/esp_driver_uart/CMakeFiles/__idf_esp_driver_uart.dir/build.make esp-idf/esp_driver_uart/CMakeFiles/__idf_esp_driver_uart.dir/depend
	$(MAKE) $(MAKESILENT) -f esp-idf/esp_driver_uart/CMakeFiles/__idf_esp_driver_uart.dir/build.make esp-idf/esp_driver_uart/CMakeFiles/__idf_esp_driver_uart.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles --progress-num= "Built target __idf_esp_driver_uart"
.PHONY : esp-idf/esp_driver_uart/CMakeFiles/__idf_esp_driver_uart.dir/all

# Build rule for subdir invocation for target.
esp-idf/esp_driver_uart/CMakeFiles/__idf_esp_driver_uart.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles 19
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 esp-idf/esp_driver_uart/CMakeFiles/__idf_esp_driver_uart.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles 0
.PHONY : esp-idf/esp_driver_uart/CMakeFiles/__idf_esp_driver_uart.dir/rule

# Convenience name for target.
__idf_esp_driver_uart: esp-idf/esp_driver_uart/CMakeFiles/__idf_esp_driver_uart.dir/rule
.PHONY : __idf_esp_driver_uart

# clean rule for target.
esp-idf/esp_driver_uart/CMakeFiles/__idf_esp_driver_uart.dir/clean:
	$(MAKE) $(MAKESILENT) -f esp-idf/esp_driver_uart/CMakeFiles/__idf_esp_driver_uart.dir/build.make esp-idf/esp_driver_uart/CMakeFiles/__idf_esp_driver_uart.dir/clean
.PHONY : esp-idf/esp_driver_uart/CMakeFiles/__idf_esp_driver_uart.dir/clean

#=============================================================================
# Target rules for target esp-idf/app_trace/CMakeFiles/__idf_app_trace.dir

# All Build rule for target.
esp-idf/app_trace/CMakeFiles/__idf_app_trace.dir/all: esp-idf/xtensa/CMakeFiles/__idf_xtensa.dir/all
	$(MAKE) $(MAKESILENT) -f esp-idf/app_trace/CMakeFiles/__idf_app_trace.dir/build.make esp-idf/app_trace/CMakeFiles/__idf_app_trace.dir/depend
	$(MAKE) $(MAKESILENT) -f esp-idf/app_trace/CMakeFiles/__idf_app_trace.dir/build.make esp-idf/app_trace/CMakeFiles/__idf_app_trace.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles --progress-num= "Built target __idf_app_trace"
.PHONY : esp-idf/app_trace/CMakeFiles/__idf_app_trace.dir/all

# Build rule for subdir invocation for target.
esp-idf/app_trace/CMakeFiles/__idf_app_trace.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles 50
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 esp-idf/app_trace/CMakeFiles/__idf_app_trace.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles 0
.PHONY : esp-idf/app_trace/CMakeFiles/__idf_app_trace.dir/rule

# Convenience name for target.
__idf_app_trace: esp-idf/app_trace/CMakeFiles/__idf_app_trace.dir/rule
.PHONY : __idf_app_trace

# clean rule for target.
esp-idf/app_trace/CMakeFiles/__idf_app_trace.dir/clean:
	$(MAKE) $(MAKESILENT) -f esp-idf/app_trace/CMakeFiles/__idf_app_trace.dir/build.make esp-idf/app_trace/CMakeFiles/__idf_app_trace.dir/clean
.PHONY : esp-idf/app_trace/CMakeFiles/__idf_app_trace.dir/clean

#=============================================================================
# Target rules for target esp-idf/esp_event/CMakeFiles/__idf_esp_event.dir

# All Build rule for target.
esp-idf/esp_event/CMakeFiles/__idf_esp_event.dir/all: esp-idf/nvs_flash/CMakeFiles/__idf_nvs_flash.dir/all
	$(MAKE) $(MAKESILENT) -f esp-idf/esp_event/CMakeFiles/__idf_esp_event.dir/build.make esp-idf/esp_event/CMakeFiles/__idf_esp_event.dir/depend
	$(MAKE) $(MAKESILENT) -f esp-idf/esp_event/CMakeFiles/__idf_esp_event.dir/build.make esp-idf/esp_event/CMakeFiles/__idf_esp_event.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles --progress-num= "Built target __idf_esp_event"
.PHONY : esp-idf/esp_event/CMakeFiles/__idf_esp_event.dir/all

# Build rule for subdir invocation for target.
esp-idf/esp_event/CMakeFiles/__idf_esp_event.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles 19
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 esp-idf/esp_event/CMakeFiles/__idf_esp_event.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles 0
.PHONY : esp-idf/esp_event/CMakeFiles/__idf_esp_event.dir/rule

# Convenience name for target.
__idf_esp_event: esp-idf/esp_event/CMakeFiles/__idf_esp_event.dir/rule
.PHONY : __idf_esp_event

# clean rule for target.
esp-idf/esp_event/CMakeFiles/__idf_esp_event.dir/clean:
	$(MAKE) $(MAKESILENT) -f esp-idf/esp_event/CMakeFiles/__idf_esp_event.dir/build.make esp-idf/esp_event/CMakeFiles/__idf_esp_event.dir/clean
.PHONY : esp-idf/esp_event/CMakeFiles/__idf_esp_event.dir/clean

#=============================================================================
# Target rules for target esp-idf/nvs_flash/CMakeFiles/__idf_nvs_flash.dir

# All Build rule for target.
esp-idf/nvs_flash/CMakeFiles/__idf_nvs_flash.dir/all: esp-idf/esp_driver_pcnt/CMakeFiles/__idf_esp_driver_pcnt.dir/all
	$(MAKE) $(MAKESILENT) -f esp-idf/nvs_flash/CMakeFiles/__idf_nvs_flash.dir/build.make esp-idf/nvs_flash/CMakeFiles/__idf_nvs_flash.dir/depend
	$(MAKE) $(MAKESILENT) -f esp-idf/nvs_flash/CMakeFiles/__idf_nvs_flash.dir/build.make esp-idf/nvs_flash/CMakeFiles/__idf_nvs_flash.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles --progress-num=76 "Built target __idf_nvs_flash"
.PHONY : esp-idf/nvs_flash/CMakeFiles/__idf_nvs_flash.dir/all

# Build rule for subdir invocation for target.
esp-idf/nvs_flash/CMakeFiles/__idf_nvs_flash.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles 19
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 esp-idf/nvs_flash/CMakeFiles/__idf_nvs_flash.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles 0
.PHONY : esp-idf/nvs_flash/CMakeFiles/__idf_nvs_flash.dir/rule

# Convenience name for target.
__idf_nvs_flash: esp-idf/nvs_flash/CMakeFiles/__idf_nvs_flash.dir/rule
.PHONY : __idf_nvs_flash

# clean rule for target.
esp-idf/nvs_flash/CMakeFiles/__idf_nvs_flash.dir/clean:
	$(MAKE) $(MAKESILENT) -f esp-idf/nvs_flash/CMakeFiles/__idf_nvs_flash.dir/build.make esp-idf/nvs_flash/CMakeFiles/__idf_nvs_flash.dir/clean
.PHONY : esp-idf/nvs_flash/CMakeFiles/__idf_nvs_flash.dir/clean

#=============================================================================
# Target rules for target esp-idf/esp_driver_pcnt/CMakeFiles/__idf_esp_driver_pcnt.dir

# All Build rule for target.
esp-idf/esp_driver_pcnt/CMakeFiles/__idf_esp_driver_pcnt.dir/all: esp-idf/esp_driver_spi/CMakeFiles/__idf_esp_driver_spi.dir/all
	$(MAKE) $(MAKESILENT) -f esp-idf/esp_driver_pcnt/CMakeFiles/__idf_esp_driver_pcnt.dir/build.make esp-idf/esp_driver_pcnt/CMakeFiles/__idf_esp_driver_pcnt.dir/depend
	$(MAKE) $(MAKESILENT) -f esp-idf/esp_driver_pcnt/CMakeFiles/__idf_esp_driver_pcnt.dir/build.make esp-idf/esp_driver_pcnt/CMakeFiles/__idf_esp_driver_pcnt.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles --progress-num= "Built target __idf_esp_driver_pcnt"
.PHONY : esp-idf/esp_driver_pcnt/CMakeFiles/__idf_esp_driver_pcnt.dir/all

# Build rule for subdir invocation for target.
esp-idf/esp_driver_pcnt/CMakeFiles/__idf_esp_driver_pcnt.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles 18
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 esp-idf/esp_driver_pcnt/CMakeFiles/__idf_esp_driver_pcnt.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles 0
.PHONY : esp-idf/esp_driver_pcnt/CMakeFiles/__idf_esp_driver_pcnt.dir/rule

# Convenience name for target.
__idf_esp_driver_pcnt: esp-idf/esp_driver_pcnt/CMakeFiles/__idf_esp_driver_pcnt.dir/rule
.PHONY : __idf_esp_driver_pcnt

# clean rule for target.
esp-idf/esp_driver_pcnt/CMakeFiles/__idf_esp_driver_pcnt.dir/clean:
	$(MAKE) $(MAKESILENT) -f esp-idf/esp_driver_pcnt/CMakeFiles/__idf_esp_driver_pcnt.dir/build.make esp-idf/esp_driver_pcnt/CMakeFiles/__idf_esp_driver_pcnt.dir/clean
.PHONY : esp-idf/esp_driver_pcnt/CMakeFiles/__idf_esp_driver_pcnt.dir/clean

#=============================================================================
# Target rules for target esp-idf/esp_driver_spi/CMakeFiles/__idf_esp_driver_spi.dir

# All Build rule for target.
esp-idf/esp_driver_spi/CMakeFiles/__idf_esp_driver_spi.dir/all: esp-idf/esp_driver_mcpwm/CMakeFiles/__idf_esp_driver_mcpwm.dir/all
	$(MAKE) $(MAKESILENT) -f esp-idf/esp_driver_spi/CMakeFiles/__idf_esp_driver_spi.dir/build.make esp-idf/esp_driver_spi/CMakeFiles/__idf_esp_driver_spi.dir/depend
	$(MAKE) $(MAKESILENT) -f esp-idf/esp_driver_spi/CMakeFiles/__idf_esp_driver_spi.dir/build.make esp-idf/esp_driver_spi/CMakeFiles/__idf_esp_driver_spi.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles --progress-num= "Built target __idf_esp_driver_spi"
.PHONY : esp-idf/esp_driver_spi/CMakeFiles/__idf_esp_driver_spi.dir/all

# Build rule for subdir invocation for target.
esp-idf/esp_driver_spi/CMakeFiles/__idf_esp_driver_spi.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles 18
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 esp-idf/esp_driver_spi/CMakeFiles/__idf_esp_driver_spi.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles 0
.PHONY : esp-idf/esp_driver_spi/CMakeFiles/__idf_esp_driver_spi.dir/rule

# Convenience name for target.
__idf_esp_driver_spi: esp-idf/esp_driver_spi/CMakeFiles/__idf_esp_driver_spi.dir/rule
.PHONY : __idf_esp_driver_spi

# clean rule for target.
esp-idf/esp_driver_spi/CMakeFiles/__idf_esp_driver_spi.dir/clean:
	$(MAKE) $(MAKESILENT) -f esp-idf/esp_driver_spi/CMakeFiles/__idf_esp_driver_spi.dir/build.make esp-idf/esp_driver_spi/CMakeFiles/__idf_esp_driver_spi.dir/clean
.PHONY : esp-idf/esp_driver_spi/CMakeFiles/__idf_esp_driver_spi.dir/clean

#=============================================================================
# Target rules for target esp-idf/esp_driver_mcpwm/CMakeFiles/__idf_esp_driver_mcpwm.dir

# All Build rule for target.
esp-idf/esp_driver_mcpwm/CMakeFiles/__idf_esp_driver_mcpwm.dir/all: esp-idf/esp_driver_i2s/CMakeFiles/__idf_esp_driver_i2s.dir/all
	$(MAKE) $(MAKESILENT) -f esp-idf/esp_driver_mcpwm/CMakeFiles/__idf_esp_driver_mcpwm.dir/build.make esp-idf/esp_driver_mcpwm/CMakeFiles/__idf_esp_driver_mcpwm.dir/depend
	$(MAKE) $(MAKESILENT) -f esp-idf/esp_driver_mcpwm/CMakeFiles/__idf_esp_driver_mcpwm.dir/build.make esp-idf/esp_driver_mcpwm/CMakeFiles/__idf_esp_driver_mcpwm.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles --progress-num=16 "Built target __idf_esp_driver_mcpwm"
.PHONY : esp-idf/esp_driver_mcpwm/CMakeFiles/__idf_esp_driver_mcpwm.dir/all

# Build rule for subdir invocation for target.
esp-idf/esp_driver_mcpwm/CMakeFiles/__idf_esp_driver_mcpwm.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles 18
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 esp-idf/esp_driver_mcpwm/CMakeFiles/__idf_esp_driver_mcpwm.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles 0
.PHONY : esp-idf/esp_driver_mcpwm/CMakeFiles/__idf_esp_driver_mcpwm.dir/rule

# Convenience name for target.
__idf_esp_driver_mcpwm: esp-idf/esp_driver_mcpwm/CMakeFiles/__idf_esp_driver_mcpwm.dir/rule
.PHONY : __idf_esp_driver_mcpwm

# clean rule for target.
esp-idf/esp_driver_mcpwm/CMakeFiles/__idf_esp_driver_mcpwm.dir/clean:
	$(MAKE) $(MAKESILENT) -f esp-idf/esp_driver_mcpwm/CMakeFiles/__idf_esp_driver_mcpwm.dir/build.make esp-idf/esp_driver_mcpwm/CMakeFiles/__idf_esp_driver_mcpwm.dir/clean
.PHONY : esp-idf/esp_driver_mcpwm/CMakeFiles/__idf_esp_driver_mcpwm.dir/clean

#=============================================================================
# Target rules for target esp-idf/esp_driver_i2s/CMakeFiles/__idf_esp_driver_i2s.dir

# All Build rule for target.
esp-idf/esp_driver_i2s/CMakeFiles/__idf_esp_driver_i2s.dir/all: esp-idf/sdmmc/CMakeFiles/__idf_sdmmc.dir/all
	$(MAKE) $(MAKESILENT) -f esp-idf/esp_driver_i2s/CMakeFiles/__idf_esp_driver_i2s.dir/build.make esp-idf/esp_driver_i2s/CMakeFiles/__idf_esp_driver_i2s.dir/depend
	$(MAKE) $(MAKESILENT) -f esp-idf/esp_driver_i2s/CMakeFiles/__idf_esp_driver_i2s.dir/build.make esp-idf/esp_driver_i2s/CMakeFiles/__idf_esp_driver_i2s.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles --progress-num= "Built target __idf_esp_driver_i2s"
.PHONY : esp-idf/esp_driver_i2s/CMakeFiles/__idf_esp_driver_i2s.dir/all

# Build rule for subdir invocation for target.
esp-idf/esp_driver_i2s/CMakeFiles/__idf_esp_driver_i2s.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles 17
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 esp-idf/esp_driver_i2s/CMakeFiles/__idf_esp_driver_i2s.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles 0
.PHONY : esp-idf/esp_driver_i2s/CMakeFiles/__idf_esp_driver_i2s.dir/rule

# Convenience name for target.
__idf_esp_driver_i2s: esp-idf/esp_driver_i2s/CMakeFiles/__idf_esp_driver_i2s.dir/rule
.PHONY : __idf_esp_driver_i2s

# clean rule for target.
esp-idf/esp_driver_i2s/CMakeFiles/__idf_esp_driver_i2s.dir/clean:
	$(MAKE) $(MAKESILENT) -f esp-idf/esp_driver_i2s/CMakeFiles/__idf_esp_driver_i2s.dir/build.make esp-idf/esp_driver_i2s/CMakeFiles/__idf_esp_driver_i2s.dir/clean
.PHONY : esp-idf/esp_driver_i2s/CMakeFiles/__idf_esp_driver_i2s.dir/clean

#=============================================================================
# Target rules for target esp-idf/sdmmc/CMakeFiles/__idf_sdmmc.dir

# All Build rule for target.
esp-idf/sdmmc/CMakeFiles/__idf_sdmmc.dir/all: esp-idf/esp_driver_sdmmc/CMakeFiles/__idf_esp_driver_sdmmc.dir/all
	$(MAKE) $(MAKESILENT) -f esp-idf/sdmmc/CMakeFiles/__idf_sdmmc.dir/build.make esp-idf/sdmmc/CMakeFiles/__idf_sdmmc.dir/depend
	$(MAKE) $(MAKESILENT) -f esp-idf/sdmmc/CMakeFiles/__idf_sdmmc.dir/build.make esp-idf/sdmmc/CMakeFiles/__idf_sdmmc.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles --progress-num= "Built target __idf_sdmmc"
.PHONY : esp-idf/sdmmc/CMakeFiles/__idf_sdmmc.dir/all

# Build rule for subdir invocation for target.
esp-idf/sdmmc/CMakeFiles/__idf_sdmmc.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles 17
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 esp-idf/sdmmc/CMakeFiles/__idf_sdmmc.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles 0
.PHONY : esp-idf/sdmmc/CMakeFiles/__idf_sdmmc.dir/rule

# Convenience name for target.
__idf_sdmmc: esp-idf/sdmmc/CMakeFiles/__idf_sdmmc.dir/rule
.PHONY : __idf_sdmmc

# clean rule for target.
esp-idf/sdmmc/CMakeFiles/__idf_sdmmc.dir/clean:
	$(MAKE) $(MAKESILENT) -f esp-idf/sdmmc/CMakeFiles/__idf_sdmmc.dir/build.make esp-idf/sdmmc/CMakeFiles/__idf_sdmmc.dir/clean
.PHONY : esp-idf/sdmmc/CMakeFiles/__idf_sdmmc.dir/clean

#=============================================================================
# Target rules for target esp-idf/esp_driver_sdmmc/CMakeFiles/__idf_esp_driver_sdmmc.dir

# All Build rule for target.
esp-idf/esp_driver_sdmmc/CMakeFiles/__idf_esp_driver_sdmmc.dir/all: esp-idf/esp_driver_sdspi/CMakeFiles/__idf_esp_driver_sdspi.dir/all
	$(MAKE) $(MAKESILENT) -f esp-idf/esp_driver_sdmmc/CMakeFiles/__idf_esp_driver_sdmmc.dir/build.make esp-idf/esp_driver_sdmmc/CMakeFiles/__idf_esp_driver_sdmmc.dir/depend
	$(MAKE) $(MAKESILENT) -f esp-idf/esp_driver_sdmmc/CMakeFiles/__idf_esp_driver_sdmmc.dir/build.make esp-idf/esp_driver_sdmmc/CMakeFiles/__idf_esp_driver_sdmmc.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles --progress-num=17 "Built target __idf_esp_driver_sdmmc"
.PHONY : esp-idf/esp_driver_sdmmc/CMakeFiles/__idf_esp_driver_sdmmc.dir/all

# Build rule for subdir invocation for target.
esp-idf/esp_driver_sdmmc/CMakeFiles/__idf_esp_driver_sdmmc.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles 17
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 esp-idf/esp_driver_sdmmc/CMakeFiles/__idf_esp_driver_sdmmc.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles 0
.PHONY : esp-idf/esp_driver_sdmmc/CMakeFiles/__idf_esp_driver_sdmmc.dir/rule

# Convenience name for target.
__idf_esp_driver_sdmmc: esp-idf/esp_driver_sdmmc/CMakeFiles/__idf_esp_driver_sdmmc.dir/rule
.PHONY : __idf_esp_driver_sdmmc

# clean rule for target.
esp-idf/esp_driver_sdmmc/CMakeFiles/__idf_esp_driver_sdmmc.dir/clean:
	$(MAKE) $(MAKESILENT) -f esp-idf/esp_driver_sdmmc/CMakeFiles/__idf_esp_driver_sdmmc.dir/build.make esp-idf/esp_driver_sdmmc/CMakeFiles/__idf_esp_driver_sdmmc.dir/clean
.PHONY : esp-idf/esp_driver_sdmmc/CMakeFiles/__idf_esp_driver_sdmmc.dir/clean

#=============================================================================
# Target rules for target esp-idf/esp_driver_sdspi/CMakeFiles/__idf_esp_driver_sdspi.dir

# All Build rule for target.
esp-idf/esp_driver_sdspi/CMakeFiles/__idf_esp_driver_sdspi.dir/all: esp-idf/esp_driver_rmt/CMakeFiles/__idf_esp_driver_rmt.dir/all
	$(MAKE) $(MAKESILENT) -f esp-idf/esp_driver_sdspi/CMakeFiles/__idf_esp_driver_sdspi.dir/build.make esp-idf/esp_driver_sdspi/CMakeFiles/__idf_esp_driver_sdspi.dir/depend
	$(MAKE) $(MAKESILENT) -f esp-idf/esp_driver_sdspi/CMakeFiles/__idf_esp_driver_sdspi.dir/build.make esp-idf/esp_driver_sdspi/CMakeFiles/__idf_esp_driver_sdspi.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles --progress-num= "Built target __idf_esp_driver_sdspi"
.PHONY : esp-idf/esp_driver_sdspi/CMakeFiles/__idf_esp_driver_sdspi.dir/all

# Build rule for subdir invocation for target.
esp-idf/esp_driver_sdspi/CMakeFiles/__idf_esp_driver_sdspi.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles 16
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 esp-idf/esp_driver_sdspi/CMakeFiles/__idf_esp_driver_sdspi.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles 0
.PHONY : esp-idf/esp_driver_sdspi/CMakeFiles/__idf_esp_driver_sdspi.dir/rule

# Convenience name for target.
__idf_esp_driver_sdspi: esp-idf/esp_driver_sdspi/CMakeFiles/__idf_esp_driver_sdspi.dir/rule
.PHONY : __idf_esp_driver_sdspi

# clean rule for target.
esp-idf/esp_driver_sdspi/CMakeFiles/__idf_esp_driver_sdspi.dir/clean:
	$(MAKE) $(MAKESILENT) -f esp-idf/esp_driver_sdspi/CMakeFiles/__idf_esp_driver_sdspi.dir/build.make esp-idf/esp_driver_sdspi/CMakeFiles/__idf_esp_driver_sdspi.dir/clean
.PHONY : esp-idf/esp_driver_sdspi/CMakeFiles/__idf_esp_driver_sdspi.dir/clean

#=============================================================================
# Target rules for target esp-idf/esp_driver_rmt/CMakeFiles/__idf_esp_driver_rmt.dir

# All Build rule for target.
esp-idf/esp_driver_rmt/CMakeFiles/__idf_esp_driver_rmt.dir/all: esp-idf/esp_driver_tsens/CMakeFiles/__idf_esp_driver_tsens.dir/all
	$(MAKE) $(MAKESILENT) -f esp-idf/esp_driver_rmt/CMakeFiles/__idf_esp_driver_rmt.dir/build.make esp-idf/esp_driver_rmt/CMakeFiles/__idf_esp_driver_rmt.dir/depend
	$(MAKE) $(MAKESILENT) -f esp-idf/esp_driver_rmt/CMakeFiles/__idf_esp_driver_rmt.dir/build.make esp-idf/esp_driver_rmt/CMakeFiles/__idf_esp_driver_rmt.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles --progress-num= "Built target __idf_esp_driver_rmt"
.PHONY : esp-idf/esp_driver_rmt/CMakeFiles/__idf_esp_driver_rmt.dir/all

# Build rule for subdir invocation for target.
esp-idf/esp_driver_rmt/CMakeFiles/__idf_esp_driver_rmt.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles 16
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 esp-idf/esp_driver_rmt/CMakeFiles/__idf_esp_driver_rmt.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles 0
.PHONY : esp-idf/esp_driver_rmt/CMakeFiles/__idf_esp_driver_rmt.dir/rule

# Convenience name for target.
__idf_esp_driver_rmt: esp-idf/esp_driver_rmt/CMakeFiles/__idf_esp_driver_rmt.dir/rule
.PHONY : __idf_esp_driver_rmt

# clean rule for target.
esp-idf/esp_driver_rmt/CMakeFiles/__idf_esp_driver_rmt.dir/clean:
	$(MAKE) $(MAKESILENT) -f esp-idf/esp_driver_rmt/CMakeFiles/__idf_esp_driver_rmt.dir/build.make esp-idf/esp_driver_rmt/CMakeFiles/__idf_esp_driver_rmt.dir/clean
.PHONY : esp-idf/esp_driver_rmt/CMakeFiles/__idf_esp_driver_rmt.dir/clean

#=============================================================================
# Target rules for target esp-idf/esp_driver_tsens/CMakeFiles/__idf_esp_driver_tsens.dir

# All Build rule for target.
esp-idf/esp_driver_tsens/CMakeFiles/__idf_esp_driver_tsens.dir/all: esp-idf/esp_driver_sdm/CMakeFiles/__idf_esp_driver_sdm.dir/all
	$(MAKE) $(MAKESILENT) -f esp-idf/esp_driver_tsens/CMakeFiles/__idf_esp_driver_tsens.dir/build.make esp-idf/esp_driver_tsens/CMakeFiles/__idf_esp_driver_tsens.dir/depend
	$(MAKE) $(MAKESILENT) -f esp-idf/esp_driver_tsens/CMakeFiles/__idf_esp_driver_tsens.dir/build.make esp-idf/esp_driver_tsens/CMakeFiles/__idf_esp_driver_tsens.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles --progress-num= "Built target __idf_esp_driver_tsens"
.PHONY : esp-idf/esp_driver_tsens/CMakeFiles/__idf_esp_driver_tsens.dir/all

# Build rule for subdir invocation for target.
esp-idf/esp_driver_tsens/CMakeFiles/__idf_esp_driver_tsens.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles 16
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 esp-idf/esp_driver_tsens/CMakeFiles/__idf_esp_driver_tsens.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles 0
.PHONY : esp-idf/esp_driver_tsens/CMakeFiles/__idf_esp_driver_tsens.dir/rule

# Convenience name for target.
__idf_esp_driver_tsens: esp-idf/esp_driver_tsens/CMakeFiles/__idf_esp_driver_tsens.dir/rule
.PHONY : __idf_esp_driver_tsens

# clean rule for target.
esp-idf/esp_driver_tsens/CMakeFiles/__idf_esp_driver_tsens.dir/clean:
	$(MAKE) $(MAKESILENT) -f esp-idf/esp_driver_tsens/CMakeFiles/__idf_esp_driver_tsens.dir/build.make esp-idf/esp_driver_tsens/CMakeFiles/__idf_esp_driver_tsens.dir/clean
.PHONY : esp-idf/esp_driver_tsens/CMakeFiles/__idf_esp_driver_tsens.dir/clean

#=============================================================================
# Target rules for target esp-idf/esp_driver_sdm/CMakeFiles/__idf_esp_driver_sdm.dir

# All Build rule for target.
esp-idf/esp_driver_sdm/CMakeFiles/__idf_esp_driver_sdm.dir/all: esp-idf/esp_driver_i2c/CMakeFiles/__idf_esp_driver_i2c.dir/all
	$(MAKE) $(MAKESILENT) -f esp-idf/esp_driver_sdm/CMakeFiles/__idf_esp_driver_sdm.dir/build.make esp-idf/esp_driver_sdm/CMakeFiles/__idf_esp_driver_sdm.dir/depend
	$(MAKE) $(MAKESILENT) -f esp-idf/esp_driver_sdm/CMakeFiles/__idf_esp_driver_sdm.dir/build.make esp-idf/esp_driver_sdm/CMakeFiles/__idf_esp_driver_sdm.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles --progress-num= "Built target __idf_esp_driver_sdm"
.PHONY : esp-idf/esp_driver_sdm/CMakeFiles/__idf_esp_driver_sdm.dir/all

# Build rule for subdir invocation for target.
esp-idf/esp_driver_sdm/CMakeFiles/__idf_esp_driver_sdm.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles 16
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 esp-idf/esp_driver_sdm/CMakeFiles/__idf_esp_driver_sdm.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles 0
.PHONY : esp-idf/esp_driver_sdm/CMakeFiles/__idf_esp_driver_sdm.dir/rule

# Convenience name for target.
__idf_esp_driver_sdm: esp-idf/esp_driver_sdm/CMakeFiles/__idf_esp_driver_sdm.dir/rule
.PHONY : __idf_esp_driver_sdm

# clean rule for target.
esp-idf/esp_driver_sdm/CMakeFiles/__idf_esp_driver_sdm.dir/clean:
	$(MAKE) $(MAKESILENT) -f esp-idf/esp_driver_sdm/CMakeFiles/__idf_esp_driver_sdm.dir/build.make esp-idf/esp_driver_sdm/CMakeFiles/__idf_esp_driver_sdm.dir/clean
.PHONY : esp-idf/esp_driver_sdm/CMakeFiles/__idf_esp_driver_sdm.dir/clean

#=============================================================================
# Target rules for target esp-idf/esp_driver_i2c/CMakeFiles/__idf_esp_driver_i2c.dir

# All Build rule for target.
esp-idf/esp_driver_i2c/CMakeFiles/__idf_esp_driver_i2c.dir/all: esp-idf/esp_driver_ledc/CMakeFiles/__idf_esp_driver_ledc.dir/all
	$(MAKE) $(MAKESILENT) -f esp-idf/esp_driver_i2c/CMakeFiles/__idf_esp_driver_i2c.dir/build.make esp-idf/esp_driver_i2c/CMakeFiles/__idf_esp_driver_i2c.dir/depend
	$(MAKE) $(MAKESILENT) -f esp-idf/esp_driver_i2c/CMakeFiles/__idf_esp_driver_i2c.dir/build.make esp-idf/esp_driver_i2c/CMakeFiles/__idf_esp_driver_i2c.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles --progress-num= "Built target __idf_esp_driver_i2c"
.PHONY : esp-idf/esp_driver_i2c/CMakeFiles/__idf_esp_driver_i2c.dir/all

# Build rule for subdir invocation for target.
esp-idf/esp_driver_i2c/CMakeFiles/__idf_esp_driver_i2c.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles 16
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 esp-idf/esp_driver_i2c/CMakeFiles/__idf_esp_driver_i2c.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles 0
.PHONY : esp-idf/esp_driver_i2c/CMakeFiles/__idf_esp_driver_i2c.dir/rule

# Convenience name for target.
__idf_esp_driver_i2c: esp-idf/esp_driver_i2c/CMakeFiles/__idf_esp_driver_i2c.dir/rule
.PHONY : __idf_esp_driver_i2c

# clean rule for target.
esp-idf/esp_driver_i2c/CMakeFiles/__idf_esp_driver_i2c.dir/clean:
	$(MAKE) $(MAKESILENT) -f esp-idf/esp_driver_i2c/CMakeFiles/__idf_esp_driver_i2c.dir/build.make esp-idf/esp_driver_i2c/CMakeFiles/__idf_esp_driver_i2c.dir/clean
.PHONY : esp-idf/esp_driver_i2c/CMakeFiles/__idf_esp_driver_i2c.dir/clean

#=============================================================================
# Target rules for target esp-idf/esp_driver_ledc/CMakeFiles/__idf_esp_driver_ledc.dir

# All Build rule for target.
esp-idf/esp_driver_ledc/CMakeFiles/__idf_esp_driver_ledc.dir/all: esp-idf/esp_driver_usb_serial_jtag/CMakeFiles/__idf_esp_driver_usb_serial_jtag.dir/all
	$(MAKE) $(MAKESILENT) -f esp-idf/esp_driver_ledc/CMakeFiles/__idf_esp_driver_ledc.dir/build.make esp-idf/esp_driver_ledc/CMakeFiles/__idf_esp_driver_ledc.dir/depend
	$(MAKE) $(MAKESILENT) -f esp-idf/esp_driver_ledc/CMakeFiles/__idf_esp_driver_ledc.dir/build.make esp-idf/esp_driver_ledc/CMakeFiles/__idf_esp_driver_ledc.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles --progress-num= "Built target __idf_esp_driver_ledc"
.PHONY : esp-idf/esp_driver_ledc/CMakeFiles/__idf_esp_driver_ledc.dir/all

# Build rule for subdir invocation for target.
esp-idf/esp_driver_ledc/CMakeFiles/__idf_esp_driver_ledc.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles 16
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 esp-idf/esp_driver_ledc/CMakeFiles/__idf_esp_driver_ledc.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles 0
.PHONY : esp-idf/esp_driver_ledc/CMakeFiles/__idf_esp_driver_ledc.dir/rule

# Convenience name for target.
__idf_esp_driver_ledc: esp-idf/esp_driver_ledc/CMakeFiles/__idf_esp_driver_ledc.dir/rule
.PHONY : __idf_esp_driver_ledc

# clean rule for target.
esp-idf/esp_driver_ledc/CMakeFiles/__idf_esp_driver_ledc.dir/clean:
	$(MAKE) $(MAKESILENT) -f esp-idf/esp_driver_ledc/CMakeFiles/__idf_esp_driver_ledc.dir/build.make esp-idf/esp_driver_ledc/CMakeFiles/__idf_esp_driver_ledc.dir/clean
.PHONY : esp-idf/esp_driver_ledc/CMakeFiles/__idf_esp_driver_ledc.dir/clean

#=============================================================================
# Target rules for target esp-idf/esp_driver_usb_serial_jtag/CMakeFiles/__idf_esp_driver_usb_serial_jtag.dir

# All Build rule for target.
esp-idf/esp_driver_usb_serial_jtag/CMakeFiles/__idf_esp_driver_usb_serial_jtag.dir/all: esp-idf/driver/CMakeFiles/__idf_driver.dir/all
	$(MAKE) $(MAKESILENT) -f esp-idf/esp_driver_usb_serial_jtag/CMakeFiles/__idf_esp_driver_usb_serial_jtag.dir/build.make esp-idf/esp_driver_usb_serial_jtag/CMakeFiles/__idf_esp_driver_usb_serial_jtag.dir/depend
	$(MAKE) $(MAKESILENT) -f esp-idf/esp_driver_usb_serial_jtag/CMakeFiles/__idf_esp_driver_usb_serial_jtag.dir/build.make esp-idf/esp_driver_usb_serial_jtag/CMakeFiles/__idf_esp_driver_usb_serial_jtag.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles --progress-num=18 "Built target __idf_esp_driver_usb_serial_jtag"
.PHONY : esp-idf/esp_driver_usb_serial_jtag/CMakeFiles/__idf_esp_driver_usb_serial_jtag.dir/all

# Build rule for subdir invocation for target.
esp-idf/esp_driver_usb_serial_jtag/CMakeFiles/__idf_esp_driver_usb_serial_jtag.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles 16
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 esp-idf/esp_driver_usb_serial_jtag/CMakeFiles/__idf_esp_driver_usb_serial_jtag.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles 0
.PHONY : esp-idf/esp_driver_usb_serial_jtag/CMakeFiles/__idf_esp_driver_usb_serial_jtag.dir/rule

# Convenience name for target.
__idf_esp_driver_usb_serial_jtag: esp-idf/esp_driver_usb_serial_jtag/CMakeFiles/__idf_esp_driver_usb_serial_jtag.dir/rule
.PHONY : __idf_esp_driver_usb_serial_jtag

# clean rule for target.
esp-idf/esp_driver_usb_serial_jtag/CMakeFiles/__idf_esp_driver_usb_serial_jtag.dir/clean:
	$(MAKE) $(MAKESILENT) -f esp-idf/esp_driver_usb_serial_jtag/CMakeFiles/__idf_esp_driver_usb_serial_jtag.dir/build.make esp-idf/esp_driver_usb_serial_jtag/CMakeFiles/__idf_esp_driver_usb_serial_jtag.dir/clean
.PHONY : esp-idf/esp_driver_usb_serial_jtag/CMakeFiles/__idf_esp_driver_usb_serial_jtag.dir/clean

#=============================================================================
# Target rules for target esp-idf/driver/CMakeFiles/__idf_driver.dir

# All Build rule for target.
esp-idf/driver/CMakeFiles/__idf_driver.dir/all: esp-idf/esp_phy/CMakeFiles/__idf_esp_phy.dir/all
	$(MAKE) $(MAKESILENT) -f esp-idf/driver/CMakeFiles/__idf_driver.dir/build.make esp-idf/driver/CMakeFiles/__idf_driver.dir/depend
	$(MAKE) $(MAKESILENT) -f esp-idf/driver/CMakeFiles/__idf_driver.dir/build.make esp-idf/driver/CMakeFiles/__idf_driver.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles --progress-num=12 "Built target __idf_driver"
.PHONY : esp-idf/driver/CMakeFiles/__idf_driver.dir/all

# Build rule for subdir invocation for target.
esp-idf/driver/CMakeFiles/__idf_driver.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles 15
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 esp-idf/driver/CMakeFiles/__idf_driver.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles 0
.PHONY : esp-idf/driver/CMakeFiles/__idf_driver.dir/rule

# Convenience name for target.
__idf_driver: esp-idf/driver/CMakeFiles/__idf_driver.dir/rule
.PHONY : __idf_driver

# clean rule for target.
esp-idf/driver/CMakeFiles/__idf_driver.dir/clean:
	$(MAKE) $(MAKESILENT) -f esp-idf/driver/CMakeFiles/__idf_driver.dir/build.make esp-idf/driver/CMakeFiles/__idf_driver.dir/clean
.PHONY : esp-idf/driver/CMakeFiles/__idf_driver.dir/clean

#=============================================================================
# Target rules for target esp-idf/esp_phy/CMakeFiles/__idf_esp_phy.dir

# All Build rule for target.
esp-idf/esp_phy/CMakeFiles/__idf_esp_phy.dir/all: esp-idf/esp_vfs_console/CMakeFiles/__idf_esp_vfs_console.dir/all
	$(MAKE) $(MAKESILENT) -f esp-idf/esp_phy/CMakeFiles/__idf_esp_phy.dir/build.make esp-idf/esp_phy/CMakeFiles/__idf_esp_phy.dir/depend
	$(MAKE) $(MAKESILENT) -f esp-idf/esp_phy/CMakeFiles/__idf_esp_phy.dir/build.make esp-idf/esp_phy/CMakeFiles/__idf_esp_phy.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles --progress-num= "Built target __idf_esp_phy"
.PHONY : esp-idf/esp_phy/CMakeFiles/__idf_esp_phy.dir/all

# Build rule for subdir invocation for target.
esp-idf/esp_phy/CMakeFiles/__idf_esp_phy.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles 14
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 esp-idf/esp_phy/CMakeFiles/__idf_esp_phy.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles 0
.PHONY : esp-idf/esp_phy/CMakeFiles/__idf_esp_phy.dir/rule

# Convenience name for target.
__idf_esp_phy: esp-idf/esp_phy/CMakeFiles/__idf_esp_phy.dir/rule
.PHONY : __idf_esp_phy

# clean rule for target.
esp-idf/esp_phy/CMakeFiles/__idf_esp_phy.dir/clean:
	$(MAKE) $(MAKESILENT) -f esp-idf/esp_phy/CMakeFiles/__idf_esp_phy.dir/build.make esp-idf/esp_phy/CMakeFiles/__idf_esp_phy.dir/clean
.PHONY : esp-idf/esp_phy/CMakeFiles/__idf_esp_phy.dir/clean

#=============================================================================
# Target rules for target esp-idf/esp_vfs_console/CMakeFiles/__idf_esp_vfs_console.dir

# All Build rule for target.
esp-idf/esp_vfs_console/CMakeFiles/__idf_esp_vfs_console.dir/all: esp-idf/vfs/CMakeFiles/__idf_vfs.dir/all
	$(MAKE) $(MAKESILENT) -f esp-idf/esp_vfs_console/CMakeFiles/__idf_esp_vfs_console.dir/build.make esp-idf/esp_vfs_console/CMakeFiles/__idf_esp_vfs_console.dir/depend
	$(MAKE) $(MAKESILENT) -f esp-idf/esp_vfs_console/CMakeFiles/__idf_esp_vfs_console.dir/build.make esp-idf/esp_vfs_console/CMakeFiles/__idf_esp_vfs_console.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles --progress-num= "Built target __idf_esp_vfs_console"
.PHONY : esp-idf/esp_vfs_console/CMakeFiles/__idf_esp_vfs_console.dir/all

# Build rule for subdir invocation for target.
esp-idf/esp_vfs_console/CMakeFiles/__idf_esp_vfs_console.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles 14
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 esp-idf/esp_vfs_console/CMakeFiles/__idf_esp_vfs_console.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles 0
.PHONY : esp-idf/esp_vfs_console/CMakeFiles/__idf_esp_vfs_console.dir/rule

# Convenience name for target.
__idf_esp_vfs_console: esp-idf/esp_vfs_console/CMakeFiles/__idf_esp_vfs_console.dir/rule
.PHONY : __idf_esp_vfs_console

# clean rule for target.
esp-idf/esp_vfs_console/CMakeFiles/__idf_esp_vfs_console.dir/clean:
	$(MAKE) $(MAKESILENT) -f esp-idf/esp_vfs_console/CMakeFiles/__idf_esp_vfs_console.dir/build.make esp-idf/esp_vfs_console/CMakeFiles/__idf_esp_vfs_console.dir/clean
.PHONY : esp-idf/esp_vfs_console/CMakeFiles/__idf_esp_vfs_console.dir/clean

#=============================================================================
# Target rules for target esp-idf/vfs/CMakeFiles/__idf_vfs.dir

# All Build rule for target.
esp-idf/vfs/CMakeFiles/__idf_vfs.dir/all: esp-idf/lwip/CMakeFiles/__idf_lwip.dir/all
	$(MAKE) $(MAKESILENT) -f esp-idf/vfs/CMakeFiles/__idf_vfs.dir/build.make esp-idf/vfs/CMakeFiles/__idf_vfs.dir/depend
	$(MAKE) $(MAKESILENT) -f esp-idf/vfs/CMakeFiles/__idf_vfs.dir/build.make esp-idf/vfs/CMakeFiles/__idf_vfs.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles --progress-num= "Built target __idf_vfs"
.PHONY : esp-idf/vfs/CMakeFiles/__idf_vfs.dir/all

# Build rule for subdir invocation for target.
esp-idf/vfs/CMakeFiles/__idf_vfs.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles 14
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 esp-idf/vfs/CMakeFiles/__idf_vfs.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles 0
.PHONY : esp-idf/vfs/CMakeFiles/__idf_vfs.dir/rule

# Convenience name for target.
__idf_vfs: esp-idf/vfs/CMakeFiles/__idf_vfs.dir/rule
.PHONY : __idf_vfs

# clean rule for target.
esp-idf/vfs/CMakeFiles/__idf_vfs.dir/clean:
	$(MAKE) $(MAKESILENT) -f esp-idf/vfs/CMakeFiles/__idf_vfs.dir/build.make esp-idf/vfs/CMakeFiles/__idf_vfs.dir/clean
.PHONY : esp-idf/vfs/CMakeFiles/__idf_vfs.dir/clean

#=============================================================================
# Target rules for target esp-idf/lwip/CMakeFiles/__idf_lwip.dir

# All Build rule for target.
esp-idf/lwip/CMakeFiles/__idf_lwip.dir/all: esp-idf/esp_netif/CMakeFiles/__idf_esp_netif.dir/all
	$(MAKE) $(MAKESILENT) -f esp-idf/lwip/CMakeFiles/__idf_lwip.dir/build.make esp-idf/lwip/CMakeFiles/__idf_lwip.dir/depend
	$(MAKE) $(MAKESILENT) -f esp-idf/lwip/CMakeFiles/__idf_lwip.dir/build.make esp-idf/lwip/CMakeFiles/__idf_lwip.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles --progress-num=67,68,69,70,71 "Built target __idf_lwip"
.PHONY : esp-idf/lwip/CMakeFiles/__idf_lwip.dir/all

# Build rule for subdir invocation for target.
esp-idf/lwip/CMakeFiles/__idf_lwip.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles 14
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 esp-idf/lwip/CMakeFiles/__idf_lwip.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles 0
.PHONY : esp-idf/lwip/CMakeFiles/__idf_lwip.dir/rule

# Convenience name for target.
__idf_lwip: esp-idf/lwip/CMakeFiles/__idf_lwip.dir/rule
.PHONY : __idf_lwip

# clean rule for target.
esp-idf/lwip/CMakeFiles/__idf_lwip.dir/clean:
	$(MAKE) $(MAKESILENT) -f esp-idf/lwip/CMakeFiles/__idf_lwip.dir/build.make esp-idf/lwip/CMakeFiles/__idf_lwip.dir/clean
.PHONY : esp-idf/lwip/CMakeFiles/__idf_lwip.dir/clean

#=============================================================================
# Target rules for target esp-idf/esp_netif/CMakeFiles/__idf_esp_netif.dir

# All Build rule for target.
esp-idf/esp_netif/CMakeFiles/__idf_esp_netif.dir/all: esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/all
	$(MAKE) $(MAKESILENT) -f esp-idf/esp_netif/CMakeFiles/__idf_esp_netif.dir/build.make esp-idf/esp_netif/CMakeFiles/__idf_esp_netif.dir/depend
	$(MAKE) $(MAKESILENT) -f esp-idf/esp_netif/CMakeFiles/__idf_esp_netif.dir/build.make esp-idf/esp_netif/CMakeFiles/__idf_esp_netif.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles --progress-num= "Built target __idf_esp_netif"
.PHONY : esp-idf/esp_netif/CMakeFiles/__idf_esp_netif.dir/all

# Build rule for subdir invocation for target.
esp-idf/esp_netif/CMakeFiles/__idf_esp_netif.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles 9
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 esp-idf/esp_netif/CMakeFiles/__idf_esp_netif.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles 0
.PHONY : esp-idf/esp_netif/CMakeFiles/__idf_esp_netif.dir/rule

# Convenience name for target.
__idf_esp_netif: esp-idf/esp_netif/CMakeFiles/__idf_esp_netif.dir/rule
.PHONY : __idf_esp_netif

# clean rule for target.
esp-idf/esp_netif/CMakeFiles/__idf_esp_netif.dir/clean:
	$(MAKE) $(MAKESILENT) -f esp-idf/esp_netif/CMakeFiles/__idf_esp_netif.dir/build.make esp-idf/esp_netif/CMakeFiles/__idf_esp_netif.dir/clean
.PHONY : esp-idf/esp_netif/CMakeFiles/__idf_esp_netif.dir/clean

#=============================================================================
# Target rules for target esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir

# All Build rule for target.
esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/all: esp-idf/esp_coex/CMakeFiles/__idf_esp_coex.dir/all
	$(MAKE) $(MAKESILENT) -f esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/build.make esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/depend
	$(MAKE) $(MAKESILENT) -f esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/build.make esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles --progress-num=85,86,87,88,89 "Built target __idf_wpa_supplicant"
.PHONY : esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/all

# Build rule for subdir invocation for target.
esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles 9
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles 0
.PHONY : esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/rule

# Convenience name for target.
__idf_wpa_supplicant: esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/rule
.PHONY : __idf_wpa_supplicant

# clean rule for target.
esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/clean:
	$(MAKE) $(MAKESILENT) -f esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/build.make esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/clean
.PHONY : esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/clean

#=============================================================================
# Target rules for target esp-idf/esp_coex/CMakeFiles/__idf_esp_coex.dir

# All Build rule for target.
esp-idf/esp_coex/CMakeFiles/__idf_esp_coex.dir/all: esp-idf/esp_wifi/CMakeFiles/__idf_esp_wifi.dir/all
	$(MAKE) $(MAKESILENT) -f esp-idf/esp_coex/CMakeFiles/__idf_esp_coex.dir/build.make esp-idf/esp_coex/CMakeFiles/__idf_esp_coex.dir/depend
	$(MAKE) $(MAKESILENT) -f esp-idf/esp_coex/CMakeFiles/__idf_esp_coex.dir/build.make esp-idf/esp_coex/CMakeFiles/__idf_esp_coex.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles --progress-num= "Built target __idf_esp_coex"
.PHONY : esp-idf/esp_coex/CMakeFiles/__idf_esp_coex.dir/all

# Build rule for subdir invocation for target.
esp-idf/esp_coex/CMakeFiles/__idf_esp_coex.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles 4
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 esp-idf/esp_coex/CMakeFiles/__idf_esp_coex.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles 0
.PHONY : esp-idf/esp_coex/CMakeFiles/__idf_esp_coex.dir/rule

# Convenience name for target.
__idf_esp_coex: esp-idf/esp_coex/CMakeFiles/__idf_esp_coex.dir/rule
.PHONY : __idf_esp_coex

# clean rule for target.
esp-idf/esp_coex/CMakeFiles/__idf_esp_coex.dir/clean:
	$(MAKE) $(MAKESILENT) -f esp-idf/esp_coex/CMakeFiles/__idf_esp_coex.dir/build.make esp-idf/esp_coex/CMakeFiles/__idf_esp_coex.dir/clean
.PHONY : esp-idf/esp_coex/CMakeFiles/__idf_esp_coex.dir/clean

#=============================================================================
# Target rules for target esp-idf/esp_wifi/CMakeFiles/__idf_esp_wifi.dir

# All Build rule for target.
esp-idf/esp_wifi/CMakeFiles/__idf_esp_wifi.dir/all: esp-idf/http_parser/CMakeFiles/__idf_http_parser.dir/all
	$(MAKE) $(MAKESILENT) -f esp-idf/esp_wifi/CMakeFiles/__idf_esp_wifi.dir/build.make esp-idf/esp_wifi/CMakeFiles/__idf_esp_wifi.dir/depend
	$(MAKE) $(MAKESILENT) -f esp-idf/esp_wifi/CMakeFiles/__idf_esp_wifi.dir/build.make esp-idf/esp_wifi/CMakeFiles/__idf_esp_wifi.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles --progress-num= "Built target __idf_esp_wifi"
.PHONY : esp-idf/esp_wifi/CMakeFiles/__idf_esp_wifi.dir/all

# Build rule for subdir invocation for target.
esp-idf/esp_wifi/CMakeFiles/__idf_esp_wifi.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles 4
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 esp-idf/esp_wifi/CMakeFiles/__idf_esp_wifi.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles 0
.PHONY : esp-idf/esp_wifi/CMakeFiles/__idf_esp_wifi.dir/rule

# Convenience name for target.
__idf_esp_wifi: esp-idf/esp_wifi/CMakeFiles/__idf_esp_wifi.dir/rule
.PHONY : __idf_esp_wifi

# clean rule for target.
esp-idf/esp_wifi/CMakeFiles/__idf_esp_wifi.dir/clean:
	$(MAKE) $(MAKESILENT) -f esp-idf/esp_wifi/CMakeFiles/__idf_esp_wifi.dir/build.make esp-idf/esp_wifi/CMakeFiles/__idf_esp_wifi.dir/clean
.PHONY : esp-idf/esp_wifi/CMakeFiles/__idf_esp_wifi.dir/clean

#=============================================================================
# Target rules for target esp-idf/unity/CMakeFiles/__idf_unity.dir

# All Build rule for target.
esp-idf/unity/CMakeFiles/__idf_unity.dir/all: esp-idf/xtensa/CMakeFiles/__idf_xtensa.dir/all
	$(MAKE) $(MAKESILENT) -f esp-idf/unity/CMakeFiles/__idf_unity.dir/build.make esp-idf/unity/CMakeFiles/__idf_unity.dir/depend
	$(MAKE) $(MAKESILENT) -f esp-idf/unity/CMakeFiles/__idf_unity.dir/build.make esp-idf/unity/CMakeFiles/__idf_unity.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles --progress-num=83 "Built target __idf_unity"
.PHONY : esp-idf/unity/CMakeFiles/__idf_unity.dir/all

# Build rule for subdir invocation for target.
esp-idf/unity/CMakeFiles/__idf_unity.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles 51
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 esp-idf/unity/CMakeFiles/__idf_unity.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles 0
.PHONY : esp-idf/unity/CMakeFiles/__idf_unity.dir/rule

# Convenience name for target.
__idf_unity: esp-idf/unity/CMakeFiles/__idf_unity.dir/rule
.PHONY : __idf_unity

# clean rule for target.
esp-idf/unity/CMakeFiles/__idf_unity.dir/clean:
	$(MAKE) $(MAKESILENT) -f esp-idf/unity/CMakeFiles/__idf_unity.dir/build.make esp-idf/unity/CMakeFiles/__idf_unity.dir/clean
.PHONY : esp-idf/unity/CMakeFiles/__idf_unity.dir/clean

#=============================================================================
# Target rules for target esp-idf/cmock/CMakeFiles/__idf_cmock.dir

# All Build rule for target.
esp-idf/cmock/CMakeFiles/__idf_cmock.dir/all: esp-idf/xtensa/CMakeFiles/__idf_xtensa.dir/all
esp-idf/cmock/CMakeFiles/__idf_cmock.dir/all: esp-idf/unity/CMakeFiles/__idf_unity.dir/all
	$(MAKE) $(MAKESILENT) -f esp-idf/cmock/CMakeFiles/__idf_cmock.dir/build.make esp-idf/cmock/CMakeFiles/__idf_cmock.dir/depend
	$(MAKE) $(MAKESILENT) -f esp-idf/cmock/CMakeFiles/__idf_cmock.dir/build.make esp-idf/cmock/CMakeFiles/__idf_cmock.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles --progress-num= "Built target __idf_cmock"
.PHONY : esp-idf/cmock/CMakeFiles/__idf_cmock.dir/all

# Build rule for subdir invocation for target.
esp-idf/cmock/CMakeFiles/__idf_cmock.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles 51
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 esp-idf/cmock/CMakeFiles/__idf_cmock.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles 0
.PHONY : esp-idf/cmock/CMakeFiles/__idf_cmock.dir/rule

# Convenience name for target.
__idf_cmock: esp-idf/cmock/CMakeFiles/__idf_cmock.dir/rule
.PHONY : __idf_cmock

# clean rule for target.
esp-idf/cmock/CMakeFiles/__idf_cmock.dir/clean:
	$(MAKE) $(MAKESILENT) -f esp-idf/cmock/CMakeFiles/__idf_cmock.dir/build.make esp-idf/cmock/CMakeFiles/__idf_cmock.dir/clean
.PHONY : esp-idf/cmock/CMakeFiles/__idf_cmock.dir/clean

#=============================================================================
# Target rules for target esp-idf/console/CMakeFiles/__idf_console.dir

# All Build rule for target.
esp-idf/console/CMakeFiles/__idf_console.dir/all: esp-idf/xtensa/CMakeFiles/__idf_xtensa.dir/all
	$(MAKE) $(MAKESILENT) -f esp-idf/console/CMakeFiles/__idf_console.dir/build.make esp-idf/console/CMakeFiles/__idf_console.dir/depend
	$(MAKE) $(MAKESILENT) -f esp-idf/console/CMakeFiles/__idf_console.dir/build.make esp-idf/console/CMakeFiles/__idf_console.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles --progress-num=11 "Built target __idf_console"
.PHONY : esp-idf/console/CMakeFiles/__idf_console.dir/all

# Build rule for subdir invocation for target.
esp-idf/console/CMakeFiles/__idf_console.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles 51
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 esp-idf/console/CMakeFiles/__idf_console.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles 0
.PHONY : esp-idf/console/CMakeFiles/__idf_console.dir/rule

# Convenience name for target.
__idf_console: esp-idf/console/CMakeFiles/__idf_console.dir/rule
.PHONY : __idf_console

# clean rule for target.
esp-idf/console/CMakeFiles/__idf_console.dir/clean:
	$(MAKE) $(MAKESILENT) -f esp-idf/console/CMakeFiles/__idf_console.dir/build.make esp-idf/console/CMakeFiles/__idf_console.dir/clean
.PHONY : esp-idf/console/CMakeFiles/__idf_console.dir/clean

#=============================================================================
# Target rules for target esp-idf/http_parser/CMakeFiles/__idf_http_parser.dir

# All Build rule for target.
esp-idf/http_parser/CMakeFiles/__idf_http_parser.dir/all: esp-idf/esp-tls/CMakeFiles/__idf_esp-tls.dir/all
	$(MAKE) $(MAKESILENT) -f esp-idf/http_parser/CMakeFiles/__idf_http_parser.dir/build.make esp-idf/http_parser/CMakeFiles/__idf_http_parser.dir/depend
	$(MAKE) $(MAKESILENT) -f esp-idf/http_parser/CMakeFiles/__idf_http_parser.dir/build.make esp-idf/http_parser/CMakeFiles/__idf_http_parser.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles --progress-num= "Built target __idf_http_parser"
.PHONY : esp-idf/http_parser/CMakeFiles/__idf_http_parser.dir/all

# Build rule for subdir invocation for target.
esp-idf/http_parser/CMakeFiles/__idf_http_parser.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles 4
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 esp-idf/http_parser/CMakeFiles/__idf_http_parser.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles 0
.PHONY : esp-idf/http_parser/CMakeFiles/__idf_http_parser.dir/rule

# Convenience name for target.
__idf_http_parser: esp-idf/http_parser/CMakeFiles/__idf_http_parser.dir/rule
.PHONY : __idf_http_parser

# clean rule for target.
esp-idf/http_parser/CMakeFiles/__idf_http_parser.dir/clean:
	$(MAKE) $(MAKESILENT) -f esp-idf/http_parser/CMakeFiles/__idf_http_parser.dir/build.make esp-idf/http_parser/CMakeFiles/__idf_http_parser.dir/clean
.PHONY : esp-idf/http_parser/CMakeFiles/__idf_http_parser.dir/clean

#=============================================================================
# Target rules for target esp-idf/esp-tls/CMakeFiles/__idf_esp-tls.dir

# All Build rule for target.
esp-idf/esp-tls/CMakeFiles/__idf_esp-tls.dir/all: esp-idf/esp_adc/CMakeFiles/__idf_esp_adc.dir/all
	$(MAKE) $(MAKESILENT) -f esp-idf/esp-tls/CMakeFiles/__idf_esp-tls.dir/build.make esp-idf/esp-tls/CMakeFiles/__idf_esp-tls.dir/depend
	$(MAKE) $(MAKESILENT) -f esp-idf/esp-tls/CMakeFiles/__idf_esp-tls.dir/build.make esp-idf/esp-tls/CMakeFiles/__idf_esp-tls.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles --progress-num=13 "Built target __idf_esp-tls"
.PHONY : esp-idf/esp-tls/CMakeFiles/__idf_esp-tls.dir/all

# Build rule for subdir invocation for target.
esp-idf/esp-tls/CMakeFiles/__idf_esp-tls.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles 4
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 esp-idf/esp-tls/CMakeFiles/__idf_esp-tls.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles 0
.PHONY : esp-idf/esp-tls/CMakeFiles/__idf_esp-tls.dir/rule

# Convenience name for target.
__idf_esp-tls: esp-idf/esp-tls/CMakeFiles/__idf_esp-tls.dir/rule
.PHONY : __idf_esp-tls

# clean rule for target.
esp-idf/esp-tls/CMakeFiles/__idf_esp-tls.dir/clean:
	$(MAKE) $(MAKESILENT) -f esp-idf/esp-tls/CMakeFiles/__idf_esp-tls.dir/build.make esp-idf/esp-tls/CMakeFiles/__idf_esp-tls.dir/clean
.PHONY : esp-idf/esp-tls/CMakeFiles/__idf_esp-tls.dir/clean

#=============================================================================
# Target rules for target esp-idf/esp_adc/CMakeFiles/__idf_esp_adc.dir

# All Build rule for target.
esp-idf/esp_adc/CMakeFiles/__idf_esp_adc.dir/all: esp-idf/esp_eth/CMakeFiles/__idf_esp_eth.dir/all
	$(MAKE) $(MAKESILENT) -f esp-idf/esp_adc/CMakeFiles/__idf_esp_adc.dir/build.make esp-idf/esp_adc/CMakeFiles/__idf_esp_adc.dir/depend
	$(MAKE) $(MAKESILENT) -f esp-idf/esp_adc/CMakeFiles/__idf_esp_adc.dir/build.make esp-idf/esp_adc/CMakeFiles/__idf_esp_adc.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles --progress-num=14 "Built target __idf_esp_adc"
.PHONY : esp-idf/esp_adc/CMakeFiles/__idf_esp_adc.dir/all

# Build rule for subdir invocation for target.
esp-idf/esp_adc/CMakeFiles/__idf_esp_adc.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles 3
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 esp-idf/esp_adc/CMakeFiles/__idf_esp_adc.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles 0
.PHONY : esp-idf/esp_adc/CMakeFiles/__idf_esp_adc.dir/rule

# Convenience name for target.
__idf_esp_adc: esp-idf/esp_adc/CMakeFiles/__idf_esp_adc.dir/rule
.PHONY : __idf_esp_adc

# clean rule for target.
esp-idf/esp_adc/CMakeFiles/__idf_esp_adc.dir/clean:
	$(MAKE) $(MAKESILENT) -f esp-idf/esp_adc/CMakeFiles/__idf_esp_adc.dir/build.make esp-idf/esp_adc/CMakeFiles/__idf_esp_adc.dir/clean
.PHONY : esp-idf/esp_adc/CMakeFiles/__idf_esp_adc.dir/clean

#=============================================================================
# Target rules for target esp-idf/esp_driver_cam/CMakeFiles/__idf_esp_driver_cam.dir

# All Build rule for target.
esp-idf/esp_driver_cam/CMakeFiles/__idf_esp_driver_cam.dir/all: esp-idf/xtensa/CMakeFiles/__idf_xtensa.dir/all
	$(MAKE) $(MAKESILENT) -f esp-idf/esp_driver_cam/CMakeFiles/__idf_esp_driver_cam.dir/build.make esp-idf/esp_driver_cam/CMakeFiles/__idf_esp_driver_cam.dir/depend
	$(MAKE) $(MAKESILENT) -f esp-idf/esp_driver_cam/CMakeFiles/__idf_esp_driver_cam.dir/build.make esp-idf/esp_driver_cam/CMakeFiles/__idf_esp_driver_cam.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles --progress-num= "Built target __idf_esp_driver_cam"
.PHONY : esp-idf/esp_driver_cam/CMakeFiles/__idf_esp_driver_cam.dir/all

# Build rule for subdir invocation for target.
esp-idf/esp_driver_cam/CMakeFiles/__idf_esp_driver_cam.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles 50
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 esp-idf/esp_driver_cam/CMakeFiles/__idf_esp_driver_cam.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles 0
.PHONY : esp-idf/esp_driver_cam/CMakeFiles/__idf_esp_driver_cam.dir/rule

# Convenience name for target.
__idf_esp_driver_cam: esp-idf/esp_driver_cam/CMakeFiles/__idf_esp_driver_cam.dir/rule
.PHONY : __idf_esp_driver_cam

# clean rule for target.
esp-idf/esp_driver_cam/CMakeFiles/__idf_esp_driver_cam.dir/clean:
	$(MAKE) $(MAKESILENT) -f esp-idf/esp_driver_cam/CMakeFiles/__idf_esp_driver_cam.dir/build.make esp-idf/esp_driver_cam/CMakeFiles/__idf_esp_driver_cam.dir/clean
.PHONY : esp-idf/esp_driver_cam/CMakeFiles/__idf_esp_driver_cam.dir/clean

#=============================================================================
# Target rules for target esp-idf/esp_eth/CMakeFiles/__idf_esp_eth.dir

# All Build rule for target.
esp-idf/esp_eth/CMakeFiles/__idf_esp_eth.dir/all: esp-idf/esp_gdbstub/CMakeFiles/__idf_esp_gdbstub.dir/all
	$(MAKE) $(MAKESILENT) -f esp-idf/esp_eth/CMakeFiles/__idf_esp_eth.dir/build.make esp-idf/esp_eth/CMakeFiles/__idf_esp_eth.dir/depend
	$(MAKE) $(MAKESILENT) -f esp-idf/esp_eth/CMakeFiles/__idf_esp_eth.dir/build.make esp-idf/esp_eth/CMakeFiles/__idf_esp_eth.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles --progress-num= "Built target __idf_esp_eth"
.PHONY : esp-idf/esp_eth/CMakeFiles/__idf_esp_eth.dir/all

# Build rule for subdir invocation for target.
esp-idf/esp_eth/CMakeFiles/__idf_esp_eth.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles 2
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 esp-idf/esp_eth/CMakeFiles/__idf_esp_eth.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles 0
.PHONY : esp-idf/esp_eth/CMakeFiles/__idf_esp_eth.dir/rule

# Convenience name for target.
__idf_esp_eth: esp-idf/esp_eth/CMakeFiles/__idf_esp_eth.dir/rule
.PHONY : __idf_esp_eth

# clean rule for target.
esp-idf/esp_eth/CMakeFiles/__idf_esp_eth.dir/clean:
	$(MAKE) $(MAKESILENT) -f esp-idf/esp_eth/CMakeFiles/__idf_esp_eth.dir/build.make esp-idf/esp_eth/CMakeFiles/__idf_esp_eth.dir/clean
.PHONY : esp-idf/esp_eth/CMakeFiles/__idf_esp_eth.dir/clean

#=============================================================================
# Target rules for target esp-idf/esp_gdbstub/CMakeFiles/__idf_esp_gdbstub.dir

# All Build rule for target.
esp-idf/esp_gdbstub/CMakeFiles/__idf_esp_gdbstub.dir/all: esp-idf/tcp_transport/CMakeFiles/__idf_tcp_transport.dir/all
	$(MAKE) $(MAKESILENT) -f esp-idf/esp_gdbstub/CMakeFiles/__idf_esp_gdbstub.dir/build.make esp-idf/esp_gdbstub/CMakeFiles/__idf_esp_gdbstub.dir/depend
	$(MAKE) $(MAKESILENT) -f esp-idf/esp_gdbstub/CMakeFiles/__idf_esp_gdbstub.dir/build.make esp-idf/esp_gdbstub/CMakeFiles/__idf_esp_gdbstub.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles --progress-num= "Built target __idf_esp_gdbstub"
.PHONY : esp-idf/esp_gdbstub/CMakeFiles/__idf_esp_gdbstub.dir/all

# Build rule for subdir invocation for target.
esp-idf/esp_gdbstub/CMakeFiles/__idf_esp_gdbstub.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles 2
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 esp-idf/esp_gdbstub/CMakeFiles/__idf_esp_gdbstub.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles 0
.PHONY : esp-idf/esp_gdbstub/CMakeFiles/__idf_esp_gdbstub.dir/rule

# Convenience name for target.
__idf_esp_gdbstub: esp-idf/esp_gdbstub/CMakeFiles/__idf_esp_gdbstub.dir/rule
.PHONY : __idf_esp_gdbstub

# clean rule for target.
esp-idf/esp_gdbstub/CMakeFiles/__idf_esp_gdbstub.dir/clean:
	$(MAKE) $(MAKESILENT) -f esp-idf/esp_gdbstub/CMakeFiles/__idf_esp_gdbstub.dir/build.make esp-idf/esp_gdbstub/CMakeFiles/__idf_esp_gdbstub.dir/clean
.PHONY : esp-idf/esp_gdbstub/CMakeFiles/__idf_esp_gdbstub.dir/clean

#=============================================================================
# Target rules for target esp-idf/esp_hid/CMakeFiles/__idf_esp_hid.dir

# All Build rule for target.
esp-idf/esp_hid/CMakeFiles/__idf_esp_hid.dir/all: esp-idf/xtensa/CMakeFiles/__idf_xtensa.dir/all
	$(MAKE) $(MAKESILENT) -f esp-idf/esp_hid/CMakeFiles/__idf_esp_hid.dir/build.make esp-idf/esp_hid/CMakeFiles/__idf_esp_hid.dir/depend
	$(MAKE) $(MAKESILENT) -f esp-idf/esp_hid/CMakeFiles/__idf_esp_hid.dir/build.make esp-idf/esp_hid/CMakeFiles/__idf_esp_hid.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles --progress-num=19 "Built target __idf_esp_hid"
.PHONY : esp-idf/esp_hid/CMakeFiles/__idf_esp_hid.dir/all

# Build rule for subdir invocation for target.
esp-idf/esp_hid/CMakeFiles/__idf_esp_hid.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles 51
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 esp-idf/esp_hid/CMakeFiles/__idf_esp_hid.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles 0
.PHONY : esp-idf/esp_hid/CMakeFiles/__idf_esp_hid.dir/rule

# Convenience name for target.
__idf_esp_hid: esp-idf/esp_hid/CMakeFiles/__idf_esp_hid.dir/rule
.PHONY : __idf_esp_hid

# clean rule for target.
esp-idf/esp_hid/CMakeFiles/__idf_esp_hid.dir/clean:
	$(MAKE) $(MAKESILENT) -f esp-idf/esp_hid/CMakeFiles/__idf_esp_hid.dir/build.make esp-idf/esp_hid/CMakeFiles/__idf_esp_hid.dir/clean
.PHONY : esp-idf/esp_hid/CMakeFiles/__idf_esp_hid.dir/clean

#=============================================================================
# Target rules for target esp-idf/tcp_transport/CMakeFiles/__idf_tcp_transport.dir

# All Build rule for target.
esp-idf/tcp_transport/CMakeFiles/__idf_tcp_transport.dir/all: esp-idf/esp_http_client/CMakeFiles/__idf_esp_http_client.dir/all
	$(MAKE) $(MAKESILENT) -f esp-idf/tcp_transport/CMakeFiles/__idf_tcp_transport.dir/build.make esp-idf/tcp_transport/CMakeFiles/__idf_tcp_transport.dir/depend
	$(MAKE) $(MAKESILENT) -f esp-idf/tcp_transport/CMakeFiles/__idf_tcp_transport.dir/build.make esp-idf/tcp_transport/CMakeFiles/__idf_tcp_transport.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles --progress-num=82 "Built target __idf_tcp_transport"
.PHONY : esp-idf/tcp_transport/CMakeFiles/__idf_tcp_transport.dir/all

# Build rule for subdir invocation for target.
esp-idf/tcp_transport/CMakeFiles/__idf_tcp_transport.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles 2
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 esp-idf/tcp_transport/CMakeFiles/__idf_tcp_transport.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles 0
.PHONY : esp-idf/tcp_transport/CMakeFiles/__idf_tcp_transport.dir/rule

# Convenience name for target.
__idf_tcp_transport: esp-idf/tcp_transport/CMakeFiles/__idf_tcp_transport.dir/rule
.PHONY : __idf_tcp_transport

# clean rule for target.
esp-idf/tcp_transport/CMakeFiles/__idf_tcp_transport.dir/clean:
	$(MAKE) $(MAKESILENT) -f esp-idf/tcp_transport/CMakeFiles/__idf_tcp_transport.dir/build.make esp-idf/tcp_transport/CMakeFiles/__idf_tcp_transport.dir/clean
.PHONY : esp-idf/tcp_transport/CMakeFiles/__idf_tcp_transport.dir/clean

#=============================================================================
# Target rules for target esp-idf/esp_http_client/CMakeFiles/__idf_esp_http_client.dir

# All Build rule for target.
esp-idf/esp_http_client/CMakeFiles/__idf_esp_http_client.dir/all: esp-idf/esp_http_server/CMakeFiles/__idf_esp_http_server.dir/all
	$(MAKE) $(MAKESILENT) -f esp-idf/esp_http_client/CMakeFiles/__idf_esp_http_client.dir/build.make esp-idf/esp_http_client/CMakeFiles/__idf_esp_http_client.dir/depend
	$(MAKE) $(MAKESILENT) -f esp-idf/esp_http_client/CMakeFiles/__idf_esp_http_client.dir/build.make esp-idf/esp_http_client/CMakeFiles/__idf_esp_http_client.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles --progress-num= "Built target __idf_esp_http_client"
.PHONY : esp-idf/esp_http_client/CMakeFiles/__idf_esp_http_client.dir/all

# Build rule for subdir invocation for target.
esp-idf/esp_http_client/CMakeFiles/__idf_esp_http_client.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles 1
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 esp-idf/esp_http_client/CMakeFiles/__idf_esp_http_client.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles 0
.PHONY : esp-idf/esp_http_client/CMakeFiles/__idf_esp_http_client.dir/rule

# Convenience name for target.
__idf_esp_http_client: esp-idf/esp_http_client/CMakeFiles/__idf_esp_http_client.dir/rule
.PHONY : __idf_esp_http_client

# clean rule for target.
esp-idf/esp_http_client/CMakeFiles/__idf_esp_http_client.dir/clean:
	$(MAKE) $(MAKESILENT) -f esp-idf/esp_http_client/CMakeFiles/__idf_esp_http_client.dir/build.make esp-idf/esp_http_client/CMakeFiles/__idf_esp_http_client.dir/clean
.PHONY : esp-idf/esp_http_client/CMakeFiles/__idf_esp_http_client.dir/clean

#=============================================================================
# Target rules for target esp-idf/esp_http_server/CMakeFiles/__idf_esp_http_server.dir

# All Build rule for target.
esp-idf/esp_http_server/CMakeFiles/__idf_esp_http_server.dir/all: esp-idf/esp_https_ota/CMakeFiles/__idf_esp_https_ota.dir/all
	$(MAKE) $(MAKESILENT) -f esp-idf/esp_http_server/CMakeFiles/__idf_esp_http_server.dir/build.make esp-idf/esp_http_server/CMakeFiles/__idf_esp_http_server.dir/depend
	$(MAKE) $(MAKESILENT) -f esp-idf/esp_http_server/CMakeFiles/__idf_esp_http_server.dir/build.make esp-idf/esp_http_server/CMakeFiles/__idf_esp_http_server.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles --progress-num= "Built target __idf_esp_http_server"
.PHONY : esp-idf/esp_http_server/CMakeFiles/__idf_esp_http_server.dir/all

# Build rule for subdir invocation for target.
esp-idf/esp_http_server/CMakeFiles/__idf_esp_http_server.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles 1
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 esp-idf/esp_http_server/CMakeFiles/__idf_esp_http_server.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles 0
.PHONY : esp-idf/esp_http_server/CMakeFiles/__idf_esp_http_server.dir/rule

# Convenience name for target.
__idf_esp_http_server: esp-idf/esp_http_server/CMakeFiles/__idf_esp_http_server.dir/rule
.PHONY : __idf_esp_http_server

# clean rule for target.
esp-idf/esp_http_server/CMakeFiles/__idf_esp_http_server.dir/clean:
	$(MAKE) $(MAKESILENT) -f esp-idf/esp_http_server/CMakeFiles/__idf_esp_http_server.dir/build.make esp-idf/esp_http_server/CMakeFiles/__idf_esp_http_server.dir/clean
.PHONY : esp-idf/esp_http_server/CMakeFiles/__idf_esp_http_server.dir/clean

#=============================================================================
# Target rules for target esp-idf/esp_https_ota/CMakeFiles/__idf_esp_https_ota.dir

# All Build rule for target.
esp-idf/esp_https_ota/CMakeFiles/__idf_esp_https_ota.dir/all: esp-idf/esp_psram/CMakeFiles/__idf_esp_psram.dir/all
	$(MAKE) $(MAKESILENT) -f esp-idf/esp_https_ota/CMakeFiles/__idf_esp_https_ota.dir/build.make esp-idf/esp_https_ota/CMakeFiles/__idf_esp_https_ota.dir/depend
	$(MAKE) $(MAKESILENT) -f esp-idf/esp_https_ota/CMakeFiles/__idf_esp_https_ota.dir/build.make esp-idf/esp_https_ota/CMakeFiles/__idf_esp_https_ota.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles --progress-num=20 "Built target __idf_esp_https_ota"
.PHONY : esp-idf/esp_https_ota/CMakeFiles/__idf_esp_https_ota.dir/all

# Build rule for subdir invocation for target.
esp-idf/esp_https_ota/CMakeFiles/__idf_esp_https_ota.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles 1
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 esp-idf/esp_https_ota/CMakeFiles/__idf_esp_https_ota.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles 0
.PHONY : esp-idf/esp_https_ota/CMakeFiles/__idf_esp_https_ota.dir/rule

# Convenience name for target.
__idf_esp_https_ota: esp-idf/esp_https_ota/CMakeFiles/__idf_esp_https_ota.dir/rule
.PHONY : __idf_esp_https_ota

# clean rule for target.
esp-idf/esp_https_ota/CMakeFiles/__idf_esp_https_ota.dir/clean:
	$(MAKE) $(MAKESILENT) -f esp-idf/esp_https_ota/CMakeFiles/__idf_esp_https_ota.dir/build.make esp-idf/esp_https_ota/CMakeFiles/__idf_esp_https_ota.dir/clean
.PHONY : esp-idf/esp_https_ota/CMakeFiles/__idf_esp_https_ota.dir/clean

#=============================================================================
# Target rules for target esp-idf/esp_https_server/CMakeFiles/__idf_esp_https_server.dir

# All Build rule for target.
esp-idf/esp_https_server/CMakeFiles/__idf_esp_https_server.dir/all: esp-idf/xtensa/CMakeFiles/__idf_xtensa.dir/all
	$(MAKE) $(MAKESILENT) -f esp-idf/esp_https_server/CMakeFiles/__idf_esp_https_server.dir/build.make esp-idf/esp_https_server/CMakeFiles/__idf_esp_https_server.dir/depend
	$(MAKE) $(MAKESILENT) -f esp-idf/esp_https_server/CMakeFiles/__idf_esp_https_server.dir/build.make esp-idf/esp_https_server/CMakeFiles/__idf_esp_https_server.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles --progress-num= "Built target __idf_esp_https_server"
.PHONY : esp-idf/esp_https_server/CMakeFiles/__idf_esp_https_server.dir/all

# Build rule for subdir invocation for target.
esp-idf/esp_https_server/CMakeFiles/__idf_esp_https_server.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles 50
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 esp-idf/esp_https_server/CMakeFiles/__idf_esp_https_server.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles 0
.PHONY : esp-idf/esp_https_server/CMakeFiles/__idf_esp_https_server.dir/rule

# Convenience name for target.
__idf_esp_https_server: esp-idf/esp_https_server/CMakeFiles/__idf_esp_https_server.dir/rule
.PHONY : __idf_esp_https_server

# clean rule for target.
esp-idf/esp_https_server/CMakeFiles/__idf_esp_https_server.dir/clean:
	$(MAKE) $(MAKESILENT) -f esp-idf/esp_https_server/CMakeFiles/__idf_esp_https_server.dir/build.make esp-idf/esp_https_server/CMakeFiles/__idf_esp_https_server.dir/clean
.PHONY : esp-idf/esp_https_server/CMakeFiles/__idf_esp_https_server.dir/clean

#=============================================================================
# Target rules for target esp-idf/esp_psram/CMakeFiles/__idf_esp_psram.dir

# All Build rule for target.
esp-idf/esp_psram/CMakeFiles/__idf_esp_psram.dir/all: esp-idf/mbedtls/CMakeFiles/custom_bundle.dir/all
esp-idf/esp_psram/CMakeFiles/__idf_esp_psram.dir/all: esp-idf/esp_system/CMakeFiles/memory.ld.dir/all
esp-idf/esp_psram/CMakeFiles/__idf_esp_psram.dir/all: esp-idf/esp_system/CMakeFiles/sections.ld.in.dir/all
	$(MAKE) $(MAKESILENT) -f esp-idf/esp_psram/CMakeFiles/__idf_esp_psram.dir/build.make esp-idf/esp_psram/CMakeFiles/__idf_esp_psram.dir/depend
	$(MAKE) $(MAKESILENT) -f esp-idf/esp_psram/CMakeFiles/__idf_esp_psram.dir/build.make esp-idf/esp_psram/CMakeFiles/__idf_esp_psram.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles --progress-num= "Built target __idf_esp_psram"
.PHONY : esp-idf/esp_psram/CMakeFiles/__idf_esp_psram.dir/all

# Build rule for subdir invocation for target.
esp-idf/esp_psram/CMakeFiles/__idf_esp_psram.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles 0
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 esp-idf/esp_psram/CMakeFiles/__idf_esp_psram.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles 0
.PHONY : esp-idf/esp_psram/CMakeFiles/__idf_esp_psram.dir/rule

# Convenience name for target.
__idf_esp_psram: esp-idf/esp_psram/CMakeFiles/__idf_esp_psram.dir/rule
.PHONY : __idf_esp_psram

# clean rule for target.
esp-idf/esp_psram/CMakeFiles/__idf_esp_psram.dir/clean:
	$(MAKE) $(MAKESILENT) -f esp-idf/esp_psram/CMakeFiles/__idf_esp_psram.dir/build.make esp-idf/esp_psram/CMakeFiles/__idf_esp_psram.dir/clean
.PHONY : esp-idf/esp_psram/CMakeFiles/__idf_esp_psram.dir/clean

#=============================================================================
# Target rules for target esp-idf/esp_lcd/CMakeFiles/__idf_esp_lcd.dir

# All Build rule for target.
esp-idf/esp_lcd/CMakeFiles/__idf_esp_lcd.dir/all: esp-idf/xtensa/CMakeFiles/__idf_xtensa.dir/all
	$(MAKE) $(MAKESILENT) -f esp-idf/esp_lcd/CMakeFiles/__idf_esp_lcd.dir/build.make esp-idf/esp_lcd/CMakeFiles/__idf_esp_lcd.dir/depend
	$(MAKE) $(MAKESILENT) -f esp-idf/esp_lcd/CMakeFiles/__idf_esp_lcd.dir/build.make esp-idf/esp_lcd/CMakeFiles/__idf_esp_lcd.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles --progress-num= "Built target __idf_esp_lcd"
.PHONY : esp-idf/esp_lcd/CMakeFiles/__idf_esp_lcd.dir/all

# Build rule for subdir invocation for target.
esp-idf/esp_lcd/CMakeFiles/__idf_esp_lcd.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles 50
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 esp-idf/esp_lcd/CMakeFiles/__idf_esp_lcd.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles 0
.PHONY : esp-idf/esp_lcd/CMakeFiles/__idf_esp_lcd.dir/rule

# Convenience name for target.
__idf_esp_lcd: esp-idf/esp_lcd/CMakeFiles/__idf_esp_lcd.dir/rule
.PHONY : __idf_esp_lcd

# clean rule for target.
esp-idf/esp_lcd/CMakeFiles/__idf_esp_lcd.dir/clean:
	$(MAKE) $(MAKESILENT) -f esp-idf/esp_lcd/CMakeFiles/__idf_esp_lcd.dir/build.make esp-idf/esp_lcd/CMakeFiles/__idf_esp_lcd.dir/clean
.PHONY : esp-idf/esp_lcd/CMakeFiles/__idf_esp_lcd.dir/clean

#=============================================================================
# Target rules for target esp-idf/protobuf-c/CMakeFiles/__idf_protobuf-c.dir

# All Build rule for target.
esp-idf/protobuf-c/CMakeFiles/__idf_protobuf-c.dir/all: esp-idf/xtensa/CMakeFiles/__idf_xtensa.dir/all
	$(MAKE) $(MAKESILENT) -f esp-idf/protobuf-c/CMakeFiles/__idf_protobuf-c.dir/build.make esp-idf/protobuf-c/CMakeFiles/__idf_protobuf-c.dir/depend
	$(MAKE) $(MAKESILENT) -f esp-idf/protobuf-c/CMakeFiles/__idf_protobuf-c.dir/build.make esp-idf/protobuf-c/CMakeFiles/__idf_protobuf-c.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles --progress-num=77 "Built target __idf_protobuf-c"
.PHONY : esp-idf/protobuf-c/CMakeFiles/__idf_protobuf-c.dir/all

# Build rule for subdir invocation for target.
esp-idf/protobuf-c/CMakeFiles/__idf_protobuf-c.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles 51
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 esp-idf/protobuf-c/CMakeFiles/__idf_protobuf-c.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles 0
.PHONY : esp-idf/protobuf-c/CMakeFiles/__idf_protobuf-c.dir/rule

# Convenience name for target.
__idf_protobuf-c: esp-idf/protobuf-c/CMakeFiles/__idf_protobuf-c.dir/rule
.PHONY : __idf_protobuf-c

# clean rule for target.
esp-idf/protobuf-c/CMakeFiles/__idf_protobuf-c.dir/clean:
	$(MAKE) $(MAKESILENT) -f esp-idf/protobuf-c/CMakeFiles/__idf_protobuf-c.dir/build.make esp-idf/protobuf-c/CMakeFiles/__idf_protobuf-c.dir/clean
.PHONY : esp-idf/protobuf-c/CMakeFiles/__idf_protobuf-c.dir/clean

#=============================================================================
# Target rules for target esp-idf/protocomm/CMakeFiles/__idf_protocomm.dir

# All Build rule for target.
esp-idf/protocomm/CMakeFiles/__idf_protocomm.dir/all: esp-idf/xtensa/CMakeFiles/__idf_xtensa.dir/all
esp-idf/protocomm/CMakeFiles/__idf_protocomm.dir/all: esp-idf/console/CMakeFiles/__idf_console.dir/all
esp-idf/protocomm/CMakeFiles/__idf_protocomm.dir/all: esp-idf/protobuf-c/CMakeFiles/__idf_protobuf-c.dir/all
	$(MAKE) $(MAKESILENT) -f esp-idf/protocomm/CMakeFiles/__idf_protocomm.dir/build.make esp-idf/protocomm/CMakeFiles/__idf_protocomm.dir/depend
	$(MAKE) $(MAKESILENT) -f esp-idf/protocomm/CMakeFiles/__idf_protocomm.dir/build.make esp-idf/protocomm/CMakeFiles/__idf_protocomm.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles --progress-num= "Built target __idf_protocomm"
.PHONY : esp-idf/protocomm/CMakeFiles/__idf_protocomm.dir/all

# Build rule for subdir invocation for target.
esp-idf/protocomm/CMakeFiles/__idf_protocomm.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles 52
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 esp-idf/protocomm/CMakeFiles/__idf_protocomm.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles 0
.PHONY : esp-idf/protocomm/CMakeFiles/__idf_protocomm.dir/rule

# Convenience name for target.
__idf_protocomm: esp-idf/protocomm/CMakeFiles/__idf_protocomm.dir/rule
.PHONY : __idf_protocomm

# clean rule for target.
esp-idf/protocomm/CMakeFiles/__idf_protocomm.dir/clean:
	$(MAKE) $(MAKESILENT) -f esp-idf/protocomm/CMakeFiles/__idf_protocomm.dir/build.make esp-idf/protocomm/CMakeFiles/__idf_protocomm.dir/clean
.PHONY : esp-idf/protocomm/CMakeFiles/__idf_protocomm.dir/clean

#=============================================================================
# Target rules for target esp-idf/esp_local_ctrl/CMakeFiles/__idf_esp_local_ctrl.dir

# All Build rule for target.
esp-idf/esp_local_ctrl/CMakeFiles/__idf_esp_local_ctrl.dir/all: esp-idf/xtensa/CMakeFiles/__idf_xtensa.dir/all
esp-idf/esp_local_ctrl/CMakeFiles/__idf_esp_local_ctrl.dir/all: esp-idf/console/CMakeFiles/__idf_console.dir/all
esp-idf/esp_local_ctrl/CMakeFiles/__idf_esp_local_ctrl.dir/all: esp-idf/esp_https_server/CMakeFiles/__idf_esp_https_server.dir/all
esp-idf/esp_local_ctrl/CMakeFiles/__idf_esp_local_ctrl.dir/all: esp-idf/protobuf-c/CMakeFiles/__idf_protobuf-c.dir/all
esp-idf/esp_local_ctrl/CMakeFiles/__idf_esp_local_ctrl.dir/all: esp-idf/protocomm/CMakeFiles/__idf_protocomm.dir/all
	$(MAKE) $(MAKESILENT) -f esp-idf/esp_local_ctrl/CMakeFiles/__idf_esp_local_ctrl.dir/build.make esp-idf/esp_local_ctrl/CMakeFiles/__idf_esp_local_ctrl.dir/depend
	$(MAKE) $(MAKESILENT) -f esp-idf/esp_local_ctrl/CMakeFiles/__idf_esp_local_ctrl.dir/build.make esp-idf/esp_local_ctrl/CMakeFiles/__idf_esp_local_ctrl.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles --progress-num=24 "Built target __idf_esp_local_ctrl"
.PHONY : esp-idf/esp_local_ctrl/CMakeFiles/__idf_esp_local_ctrl.dir/all

# Build rule for subdir invocation for target.
esp-idf/esp_local_ctrl/CMakeFiles/__idf_esp_local_ctrl.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles 53
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 esp-idf/esp_local_ctrl/CMakeFiles/__idf_esp_local_ctrl.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles 0
.PHONY : esp-idf/esp_local_ctrl/CMakeFiles/__idf_esp_local_ctrl.dir/rule

# Convenience name for target.
__idf_esp_local_ctrl: esp-idf/esp_local_ctrl/CMakeFiles/__idf_esp_local_ctrl.dir/rule
.PHONY : __idf_esp_local_ctrl

# clean rule for target.
esp-idf/esp_local_ctrl/CMakeFiles/__idf_esp_local_ctrl.dir/clean:
	$(MAKE) $(MAKESILENT) -f esp-idf/esp_local_ctrl/CMakeFiles/__idf_esp_local_ctrl.dir/build.make esp-idf/esp_local_ctrl/CMakeFiles/__idf_esp_local_ctrl.dir/clean
.PHONY : esp-idf/esp_local_ctrl/CMakeFiles/__idf_esp_local_ctrl.dir/clean

#=============================================================================
# Target rules for target esp-idf/espcoredump/CMakeFiles/__idf_espcoredump.dir

# All Build rule for target.
esp-idf/espcoredump/CMakeFiles/__idf_espcoredump.dir/all: esp-idf/xtensa/CMakeFiles/__idf_xtensa.dir/all
	$(MAKE) $(MAKESILENT) -f esp-idf/espcoredump/CMakeFiles/__idf_espcoredump.dir/build.make esp-idf/espcoredump/CMakeFiles/__idf_espcoredump.dir/depend
	$(MAKE) $(MAKESILENT) -f esp-idf/espcoredump/CMakeFiles/__idf_espcoredump.dir/build.make esp-idf/espcoredump/CMakeFiles/__idf_espcoredump.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles --progress-num=30 "Built target __idf_espcoredump"
.PHONY : esp-idf/espcoredump/CMakeFiles/__idf_espcoredump.dir/all

# Build rule for subdir invocation for target.
esp-idf/espcoredump/CMakeFiles/__idf_espcoredump.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles 51
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 esp-idf/espcoredump/CMakeFiles/__idf_espcoredump.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles 0
.PHONY : esp-idf/espcoredump/CMakeFiles/__idf_espcoredump.dir/rule

# Convenience name for target.
__idf_espcoredump: esp-idf/espcoredump/CMakeFiles/__idf_espcoredump.dir/rule
.PHONY : __idf_espcoredump

# clean rule for target.
esp-idf/espcoredump/CMakeFiles/__idf_espcoredump.dir/clean:
	$(MAKE) $(MAKESILENT) -f esp-idf/espcoredump/CMakeFiles/__idf_espcoredump.dir/build.make esp-idf/espcoredump/CMakeFiles/__idf_espcoredump.dir/clean
.PHONY : esp-idf/espcoredump/CMakeFiles/__idf_espcoredump.dir/clean

#=============================================================================
# Target rules for target esp-idf/wear_levelling/CMakeFiles/__idf_wear_levelling.dir

# All Build rule for target.
esp-idf/wear_levelling/CMakeFiles/__idf_wear_levelling.dir/all: esp-idf/xtensa/CMakeFiles/__idf_xtensa.dir/all
	$(MAKE) $(MAKESILENT) -f esp-idf/wear_levelling/CMakeFiles/__idf_wear_levelling.dir/build.make esp-idf/wear_levelling/CMakeFiles/__idf_wear_levelling.dir/depend
	$(MAKE) $(MAKESILENT) -f esp-idf/wear_levelling/CMakeFiles/__idf_wear_levelling.dir/build.make esp-idf/wear_levelling/CMakeFiles/__idf_wear_levelling.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles --progress-num=84 "Built target __idf_wear_levelling"
.PHONY : esp-idf/wear_levelling/CMakeFiles/__idf_wear_levelling.dir/all

# Build rule for subdir invocation for target.
esp-idf/wear_levelling/CMakeFiles/__idf_wear_levelling.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles 51
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 esp-idf/wear_levelling/CMakeFiles/__idf_wear_levelling.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles 0
.PHONY : esp-idf/wear_levelling/CMakeFiles/__idf_wear_levelling.dir/rule

# Convenience name for target.
__idf_wear_levelling: esp-idf/wear_levelling/CMakeFiles/__idf_wear_levelling.dir/rule
.PHONY : __idf_wear_levelling

# clean rule for target.
esp-idf/wear_levelling/CMakeFiles/__idf_wear_levelling.dir/clean:
	$(MAKE) $(MAKESILENT) -f esp-idf/wear_levelling/CMakeFiles/__idf_wear_levelling.dir/build.make esp-idf/wear_levelling/CMakeFiles/__idf_wear_levelling.dir/clean
.PHONY : esp-idf/wear_levelling/CMakeFiles/__idf_wear_levelling.dir/clean

#=============================================================================
# Target rules for target esp-idf/fatfs/CMakeFiles/__idf_fatfs.dir

# All Build rule for target.
esp-idf/fatfs/CMakeFiles/__idf_fatfs.dir/all: esp-idf/xtensa/CMakeFiles/__idf_xtensa.dir/all
esp-idf/fatfs/CMakeFiles/__idf_fatfs.dir/all: esp-idf/wear_levelling/CMakeFiles/__idf_wear_levelling.dir/all
	$(MAKE) $(MAKESILENT) -f esp-idf/fatfs/CMakeFiles/__idf_fatfs.dir/build.make esp-idf/fatfs/CMakeFiles/__idf_fatfs.dir/depend
	$(MAKE) $(MAKESILENT) -f esp-idf/fatfs/CMakeFiles/__idf_fatfs.dir/build.make esp-idf/fatfs/CMakeFiles/__idf_fatfs.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles --progress-num= "Built target __idf_fatfs"
.PHONY : esp-idf/fatfs/CMakeFiles/__idf_fatfs.dir/all

# Build rule for subdir invocation for target.
esp-idf/fatfs/CMakeFiles/__idf_fatfs.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles 51
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 esp-idf/fatfs/CMakeFiles/__idf_fatfs.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles 0
.PHONY : esp-idf/fatfs/CMakeFiles/__idf_fatfs.dir/rule

# Convenience name for target.
__idf_fatfs: esp-idf/fatfs/CMakeFiles/__idf_fatfs.dir/rule
.PHONY : __idf_fatfs

# clean rule for target.
esp-idf/fatfs/CMakeFiles/__idf_fatfs.dir/clean:
	$(MAKE) $(MAKESILENT) -f esp-idf/fatfs/CMakeFiles/__idf_fatfs.dir/build.make esp-idf/fatfs/CMakeFiles/__idf_fatfs.dir/clean
.PHONY : esp-idf/fatfs/CMakeFiles/__idf_fatfs.dir/clean

#=============================================================================
# Target rules for target esp-idf/json/CMakeFiles/__idf_json.dir

# All Build rule for target.
esp-idf/json/CMakeFiles/__idf_json.dir/all: esp-idf/xtensa/CMakeFiles/__idf_xtensa.dir/all
	$(MAKE) $(MAKESILENT) -f esp-idf/json/CMakeFiles/__idf_json.dir/build.make esp-idf/json/CMakeFiles/__idf_json.dir/depend
	$(MAKE) $(MAKESILENT) -f esp-idf/json/CMakeFiles/__idf_json.dir/build.make esp-idf/json/CMakeFiles/__idf_json.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles --progress-num= "Built target __idf_json"
.PHONY : esp-idf/json/CMakeFiles/__idf_json.dir/all

# Build rule for subdir invocation for target.
esp-idf/json/CMakeFiles/__idf_json.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles 50
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 esp-idf/json/CMakeFiles/__idf_json.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles 0
.PHONY : esp-idf/json/CMakeFiles/__idf_json.dir/rule

# Convenience name for target.
__idf_json: esp-idf/json/CMakeFiles/__idf_json.dir/rule
.PHONY : __idf_json

# clean rule for target.
esp-idf/json/CMakeFiles/__idf_json.dir/clean:
	$(MAKE) $(MAKESILENT) -f esp-idf/json/CMakeFiles/__idf_json.dir/build.make esp-idf/json/CMakeFiles/__idf_json.dir/clean
.PHONY : esp-idf/json/CMakeFiles/__idf_json.dir/clean

#=============================================================================
# Target rules for target esp-idf/mqtt/CMakeFiles/__idf_mqtt.dir

# All Build rule for target.
esp-idf/mqtt/CMakeFiles/__idf_mqtt.dir/all: esp-idf/xtensa/CMakeFiles/__idf_xtensa.dir/all
	$(MAKE) $(MAKESILENT) -f esp-idf/mqtt/CMakeFiles/__idf_mqtt.dir/build.make esp-idf/mqtt/CMakeFiles/__idf_mqtt.dir/depend
	$(MAKE) $(MAKESILENT) -f esp-idf/mqtt/CMakeFiles/__idf_mqtt.dir/build.make esp-idf/mqtt/CMakeFiles/__idf_mqtt.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles --progress-num= "Built target __idf_mqtt"
.PHONY : esp-idf/mqtt/CMakeFiles/__idf_mqtt.dir/all

# Build rule for subdir invocation for target.
esp-idf/mqtt/CMakeFiles/__idf_mqtt.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles 50
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 esp-idf/mqtt/CMakeFiles/__idf_mqtt.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles 0
.PHONY : esp-idf/mqtt/CMakeFiles/__idf_mqtt.dir/rule

# Convenience name for target.
__idf_mqtt: esp-idf/mqtt/CMakeFiles/__idf_mqtt.dir/rule
.PHONY : __idf_mqtt

# clean rule for target.
esp-idf/mqtt/CMakeFiles/__idf_mqtt.dir/clean:
	$(MAKE) $(MAKESILENT) -f esp-idf/mqtt/CMakeFiles/__idf_mqtt.dir/build.make esp-idf/mqtt/CMakeFiles/__idf_mqtt.dir/clean
.PHONY : esp-idf/mqtt/CMakeFiles/__idf_mqtt.dir/clean

#=============================================================================
# Target rules for target esp-idf/nvs_sec_provider/CMakeFiles/__idf_nvs_sec_provider.dir

# All Build rule for target.
esp-idf/nvs_sec_provider/CMakeFiles/__idf_nvs_sec_provider.dir/all: esp-idf/xtensa/CMakeFiles/__idf_xtensa.dir/all
	$(MAKE) $(MAKESILENT) -f esp-idf/nvs_sec_provider/CMakeFiles/__idf_nvs_sec_provider.dir/build.make esp-idf/nvs_sec_provider/CMakeFiles/__idf_nvs_sec_provider.dir/depend
	$(MAKE) $(MAKESILENT) -f esp-idf/nvs_sec_provider/CMakeFiles/__idf_nvs_sec_provider.dir/build.make esp-idf/nvs_sec_provider/CMakeFiles/__idf_nvs_sec_provider.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles --progress-num= "Built target __idf_nvs_sec_provider"
.PHONY : esp-idf/nvs_sec_provider/CMakeFiles/__idf_nvs_sec_provider.dir/all

# Build rule for subdir invocation for target.
esp-idf/nvs_sec_provider/CMakeFiles/__idf_nvs_sec_provider.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles 50
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 esp-idf/nvs_sec_provider/CMakeFiles/__idf_nvs_sec_provider.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles 0
.PHONY : esp-idf/nvs_sec_provider/CMakeFiles/__idf_nvs_sec_provider.dir/rule

# Convenience name for target.
__idf_nvs_sec_provider: esp-idf/nvs_sec_provider/CMakeFiles/__idf_nvs_sec_provider.dir/rule
.PHONY : __idf_nvs_sec_provider

# clean rule for target.
esp-idf/nvs_sec_provider/CMakeFiles/__idf_nvs_sec_provider.dir/clean:
	$(MAKE) $(MAKESILENT) -f esp-idf/nvs_sec_provider/CMakeFiles/__idf_nvs_sec_provider.dir/build.make esp-idf/nvs_sec_provider/CMakeFiles/__idf_nvs_sec_provider.dir/clean
.PHONY : esp-idf/nvs_sec_provider/CMakeFiles/__idf_nvs_sec_provider.dir/clean

#=============================================================================
# Target rules for target esp-idf/perfmon/CMakeFiles/__idf_perfmon.dir

# All Build rule for target.
esp-idf/perfmon/CMakeFiles/__idf_perfmon.dir/all: esp-idf/xtensa/CMakeFiles/__idf_xtensa.dir/all
	$(MAKE) $(MAKESILENT) -f esp-idf/perfmon/CMakeFiles/__idf_perfmon.dir/build.make esp-idf/perfmon/CMakeFiles/__idf_perfmon.dir/depend
	$(MAKE) $(MAKESILENT) -f esp-idf/perfmon/CMakeFiles/__idf_perfmon.dir/build.make esp-idf/perfmon/CMakeFiles/__idf_perfmon.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles --progress-num= "Built target __idf_perfmon"
.PHONY : esp-idf/perfmon/CMakeFiles/__idf_perfmon.dir/all

# Build rule for subdir invocation for target.
esp-idf/perfmon/CMakeFiles/__idf_perfmon.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles 50
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 esp-idf/perfmon/CMakeFiles/__idf_perfmon.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles 0
.PHONY : esp-idf/perfmon/CMakeFiles/__idf_perfmon.dir/rule

# Convenience name for target.
__idf_perfmon: esp-idf/perfmon/CMakeFiles/__idf_perfmon.dir/rule
.PHONY : __idf_perfmon

# clean rule for target.
esp-idf/perfmon/CMakeFiles/__idf_perfmon.dir/clean:
	$(MAKE) $(MAKESILENT) -f esp-idf/perfmon/CMakeFiles/__idf_perfmon.dir/build.make esp-idf/perfmon/CMakeFiles/__idf_perfmon.dir/clean
.PHONY : esp-idf/perfmon/CMakeFiles/__idf_perfmon.dir/clean

#=============================================================================
# Target rules for target esp-idf/spiffs/CMakeFiles/__idf_spiffs.dir

# All Build rule for target.
esp-idf/spiffs/CMakeFiles/__idf_spiffs.dir/all: esp-idf/xtensa/CMakeFiles/__idf_xtensa.dir/all
	$(MAKE) $(MAKESILENT) -f esp-idf/spiffs/CMakeFiles/__idf_spiffs.dir/build.make esp-idf/spiffs/CMakeFiles/__idf_spiffs.dir/depend
	$(MAKE) $(MAKESILENT) -f esp-idf/spiffs/CMakeFiles/__idf_spiffs.dir/build.make esp-idf/spiffs/CMakeFiles/__idf_spiffs.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles --progress-num= "Built target __idf_spiffs"
.PHONY : esp-idf/spiffs/CMakeFiles/__idf_spiffs.dir/all

# Build rule for subdir invocation for target.
esp-idf/spiffs/CMakeFiles/__idf_spiffs.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles 50
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 esp-idf/spiffs/CMakeFiles/__idf_spiffs.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles 0
.PHONY : esp-idf/spiffs/CMakeFiles/__idf_spiffs.dir/rule

# Convenience name for target.
__idf_spiffs: esp-idf/spiffs/CMakeFiles/__idf_spiffs.dir/rule
.PHONY : __idf_spiffs

# clean rule for target.
esp-idf/spiffs/CMakeFiles/__idf_spiffs.dir/clean:
	$(MAKE) $(MAKESILENT) -f esp-idf/spiffs/CMakeFiles/__idf_spiffs.dir/build.make esp-idf/spiffs/CMakeFiles/__idf_spiffs.dir/clean
.PHONY : esp-idf/spiffs/CMakeFiles/__idf_spiffs.dir/clean

#=============================================================================
# Target rules for target esp-idf/touch_element/CMakeFiles/__idf_touch_element.dir

# All Build rule for target.
esp-idf/touch_element/CMakeFiles/__idf_touch_element.dir/all: esp-idf/xtensa/CMakeFiles/__idf_xtensa.dir/all
	$(MAKE) $(MAKESILENT) -f esp-idf/touch_element/CMakeFiles/__idf_touch_element.dir/build.make esp-idf/touch_element/CMakeFiles/__idf_touch_element.dir/depend
	$(MAKE) $(MAKESILENT) -f esp-idf/touch_element/CMakeFiles/__idf_touch_element.dir/build.make esp-idf/touch_element/CMakeFiles/__idf_touch_element.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles --progress-num= "Built target __idf_touch_element"
.PHONY : esp-idf/touch_element/CMakeFiles/__idf_touch_element.dir/all

# Build rule for subdir invocation for target.
esp-idf/touch_element/CMakeFiles/__idf_touch_element.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles 50
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 esp-idf/touch_element/CMakeFiles/__idf_touch_element.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles 0
.PHONY : esp-idf/touch_element/CMakeFiles/__idf_touch_element.dir/rule

# Convenience name for target.
__idf_touch_element: esp-idf/touch_element/CMakeFiles/__idf_touch_element.dir/rule
.PHONY : __idf_touch_element

# clean rule for target.
esp-idf/touch_element/CMakeFiles/__idf_touch_element.dir/clean:
	$(MAKE) $(MAKESILENT) -f esp-idf/touch_element/CMakeFiles/__idf_touch_element.dir/build.make esp-idf/touch_element/CMakeFiles/__idf_touch_element.dir/clean
.PHONY : esp-idf/touch_element/CMakeFiles/__idf_touch_element.dir/clean

#=============================================================================
# Target rules for target esp-idf/usb/CMakeFiles/__idf_usb.dir

# All Build rule for target.
esp-idf/usb/CMakeFiles/__idf_usb.dir/all: esp-idf/xtensa/CMakeFiles/__idf_xtensa.dir/all
	$(MAKE) $(MAKESILENT) -f esp-idf/usb/CMakeFiles/__idf_usb.dir/build.make esp-idf/usb/CMakeFiles/__idf_usb.dir/depend
	$(MAKE) $(MAKESILENT) -f esp-idf/usb/CMakeFiles/__idf_usb.dir/build.make esp-idf/usb/CMakeFiles/__idf_usb.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles --progress-num= "Built target __idf_usb"
.PHONY : esp-idf/usb/CMakeFiles/__idf_usb.dir/all

# Build rule for subdir invocation for target.
esp-idf/usb/CMakeFiles/__idf_usb.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles 50
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 esp-idf/usb/CMakeFiles/__idf_usb.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles 0
.PHONY : esp-idf/usb/CMakeFiles/__idf_usb.dir/rule

# Convenience name for target.
__idf_usb: esp-idf/usb/CMakeFiles/__idf_usb.dir/rule
.PHONY : __idf_usb

# clean rule for target.
esp-idf/usb/CMakeFiles/__idf_usb.dir/clean:
	$(MAKE) $(MAKESILENT) -f esp-idf/usb/CMakeFiles/__idf_usb.dir/build.make esp-idf/usb/CMakeFiles/__idf_usb.dir/clean
.PHONY : esp-idf/usb/CMakeFiles/__idf_usb.dir/clean

#=============================================================================
# Target rules for target esp-idf/wifi_provisioning/CMakeFiles/__idf_wifi_provisioning.dir

# All Build rule for target.
esp-idf/wifi_provisioning/CMakeFiles/__idf_wifi_provisioning.dir/all: esp-idf/xtensa/CMakeFiles/__idf_xtensa.dir/all
esp-idf/wifi_provisioning/CMakeFiles/__idf_wifi_provisioning.dir/all: esp-idf/console/CMakeFiles/__idf_console.dir/all
esp-idf/wifi_provisioning/CMakeFiles/__idf_wifi_provisioning.dir/all: esp-idf/protobuf-c/CMakeFiles/__idf_protobuf-c.dir/all
esp-idf/wifi_provisioning/CMakeFiles/__idf_wifi_provisioning.dir/all: esp-idf/protocomm/CMakeFiles/__idf_protocomm.dir/all
esp-idf/wifi_provisioning/CMakeFiles/__idf_wifi_provisioning.dir/all: esp-idf/json/CMakeFiles/__idf_json.dir/all
	$(MAKE) $(MAKESILENT) -f esp-idf/wifi_provisioning/CMakeFiles/__idf_wifi_provisioning.dir/build.make esp-idf/wifi_provisioning/CMakeFiles/__idf_wifi_provisioning.dir/depend
	$(MAKE) $(MAKESILENT) -f esp-idf/wifi_provisioning/CMakeFiles/__idf_wifi_provisioning.dir/build.make esp-idf/wifi_provisioning/CMakeFiles/__idf_wifi_provisioning.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles --progress-num= "Built target __idf_wifi_provisioning"
.PHONY : esp-idf/wifi_provisioning/CMakeFiles/__idf_wifi_provisioning.dir/all

# Build rule for subdir invocation for target.
esp-idf/wifi_provisioning/CMakeFiles/__idf_wifi_provisioning.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles 52
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 esp-idf/wifi_provisioning/CMakeFiles/__idf_wifi_provisioning.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles 0
.PHONY : esp-idf/wifi_provisioning/CMakeFiles/__idf_wifi_provisioning.dir/rule

# Convenience name for target.
__idf_wifi_provisioning: esp-idf/wifi_provisioning/CMakeFiles/__idf_wifi_provisioning.dir/rule
.PHONY : __idf_wifi_provisioning

# clean rule for target.
esp-idf/wifi_provisioning/CMakeFiles/__idf_wifi_provisioning.dir/clean:
	$(MAKE) $(MAKESILENT) -f esp-idf/wifi_provisioning/CMakeFiles/__idf_wifi_provisioning.dir/build.make esp-idf/wifi_provisioning/CMakeFiles/__idf_wifi_provisioning.dir/clean
.PHONY : esp-idf/wifi_provisioning/CMakeFiles/__idf_wifi_provisioning.dir/clean

#=============================================================================
# Target rules for target esp-idf/78__esp-ml307/CMakeFiles/__idf_78__esp-ml307.dir

# All Build rule for target.
esp-idf/78__esp-ml307/CMakeFiles/__idf_78__esp-ml307.dir/all: esp-idf/xtensa/CMakeFiles/__idf_xtensa.dir/all
esp-idf/78__esp-ml307/CMakeFiles/__idf_78__esp-ml307.dir/all: esp-idf/mqtt/CMakeFiles/__idf_mqtt.dir/all
	$(MAKE) $(MAKESILENT) -f esp-idf/78__esp-ml307/CMakeFiles/__idf_78__esp-ml307.dir/build.make esp-idf/78__esp-ml307/CMakeFiles/__idf_78__esp-ml307.dir/depend
	$(MAKE) $(MAKESILENT) -f esp-idf/78__esp-ml307/CMakeFiles/__idf_78__esp-ml307.dir/build.make esp-idf/78__esp-ml307/CMakeFiles/__idf_78__esp-ml307.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles --progress-num= "Built target __idf_78__esp-ml307"
.PHONY : esp-idf/78__esp-ml307/CMakeFiles/__idf_78__esp-ml307.dir/all

# Build rule for subdir invocation for target.
esp-idf/78__esp-ml307/CMakeFiles/__idf_78__esp-ml307.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles 50
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 esp-idf/78__esp-ml307/CMakeFiles/__idf_78__esp-ml307.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles 0
.PHONY : esp-idf/78__esp-ml307/CMakeFiles/__idf_78__esp-ml307.dir/rule

# Convenience name for target.
__idf_78__esp-ml307: esp-idf/78__esp-ml307/CMakeFiles/__idf_78__esp-ml307.dir/rule
.PHONY : __idf_78__esp-ml307

# clean rule for target.
esp-idf/78__esp-ml307/CMakeFiles/__idf_78__esp-ml307.dir/clean:
	$(MAKE) $(MAKESILENT) -f esp-idf/78__esp-ml307/CMakeFiles/__idf_78__esp-ml307.dir/build.make esp-idf/78__esp-ml307/CMakeFiles/__idf_78__esp-ml307.dir/clean
.PHONY : esp-idf/78__esp-ml307/CMakeFiles/__idf_78__esp-ml307.dir/clean

#=============================================================================
# Target rules for target esp-idf/78__esp-opus/CMakeFiles/__idf_78__esp-opus.dir

# All Build rule for target.
esp-idf/78__esp-opus/CMakeFiles/__idf_78__esp-opus.dir/all: esp-idf/xtensa/CMakeFiles/__idf_xtensa.dir/all
	$(MAKE) $(MAKESILENT) -f esp-idf/78__esp-opus/CMakeFiles/__idf_78__esp-opus.dir/build.make esp-idf/78__esp-opus/CMakeFiles/__idf_78__esp-opus.dir/depend
	$(MAKE) $(MAKESILENT) -f esp-idf/78__esp-opus/CMakeFiles/__idf_78__esp-opus.dir/build.make esp-idf/78__esp-opus/CMakeFiles/__idf_78__esp-opus.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles --progress-num=1,2,3,4,5,6,7,8 "Built target __idf_78__esp-opus"
.PHONY : esp-idf/78__esp-opus/CMakeFiles/__idf_78__esp-opus.dir/all

# Build rule for subdir invocation for target.
esp-idf/78__esp-opus/CMakeFiles/__idf_78__esp-opus.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles 58
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 esp-idf/78__esp-opus/CMakeFiles/__idf_78__esp-opus.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles 0
.PHONY : esp-idf/78__esp-opus/CMakeFiles/__idf_78__esp-opus.dir/rule

# Convenience name for target.
__idf_78__esp-opus: esp-idf/78__esp-opus/CMakeFiles/__idf_78__esp-opus.dir/rule
.PHONY : __idf_78__esp-opus

# clean rule for target.
esp-idf/78__esp-opus/CMakeFiles/__idf_78__esp-opus.dir/clean:
	$(MAKE) $(MAKESILENT) -f esp-idf/78__esp-opus/CMakeFiles/__idf_78__esp-opus.dir/build.make esp-idf/78__esp-opus/CMakeFiles/__idf_78__esp-opus.dir/clean
.PHONY : esp-idf/78__esp-opus/CMakeFiles/__idf_78__esp-opus.dir/clean

#=============================================================================
# Target rules for target esp-idf/78__esp-opus-encoder/CMakeFiles/__idf_78__esp-opus-encoder.dir

# All Build rule for target.
esp-idf/78__esp-opus-encoder/CMakeFiles/__idf_78__esp-opus-encoder.dir/all: esp-idf/xtensa/CMakeFiles/__idf_xtensa.dir/all
esp-idf/78__esp-opus-encoder/CMakeFiles/__idf_78__esp-opus-encoder.dir/all: esp-idf/78__esp-opus/CMakeFiles/__idf_78__esp-opus.dir/all
	$(MAKE) $(MAKESILENT) -f esp-idf/78__esp-opus-encoder/CMakeFiles/__idf_78__esp-opus-encoder.dir/build.make esp-idf/78__esp-opus-encoder/CMakeFiles/__idf_78__esp-opus-encoder.dir/depend
	$(MAKE) $(MAKESILENT) -f esp-idf/78__esp-opus-encoder/CMakeFiles/__idf_78__esp-opus-encoder.dir/build.make esp-idf/78__esp-opus-encoder/CMakeFiles/__idf_78__esp-opus-encoder.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles --progress-num= "Built target __idf_78__esp-opus-encoder"
.PHONY : esp-idf/78__esp-opus-encoder/CMakeFiles/__idf_78__esp-opus-encoder.dir/all

# Build rule for subdir invocation for target.
esp-idf/78__esp-opus-encoder/CMakeFiles/__idf_78__esp-opus-encoder.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles 58
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 esp-idf/78__esp-opus-encoder/CMakeFiles/__idf_78__esp-opus-encoder.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles 0
.PHONY : esp-idf/78__esp-opus-encoder/CMakeFiles/__idf_78__esp-opus-encoder.dir/rule

# Convenience name for target.
__idf_78__esp-opus-encoder: esp-idf/78__esp-opus-encoder/CMakeFiles/__idf_78__esp-opus-encoder.dir/rule
.PHONY : __idf_78__esp-opus-encoder

# clean rule for target.
esp-idf/78__esp-opus-encoder/CMakeFiles/__idf_78__esp-opus-encoder.dir/clean:
	$(MAKE) $(MAKESILENT) -f esp-idf/78__esp-opus-encoder/CMakeFiles/__idf_78__esp-opus-encoder.dir/build.make esp-idf/78__esp-opus-encoder/CMakeFiles/__idf_78__esp-opus-encoder.dir/clean
.PHONY : esp-idf/78__esp-opus-encoder/CMakeFiles/__idf_78__esp-opus-encoder.dir/clean

#=============================================================================
# Target rules for target esp-idf/78__esp-wifi-connect/CMakeFiles/__idf_78__esp-wifi-connect.dir

# All Build rule for target.
esp-idf/78__esp-wifi-connect/CMakeFiles/__idf_78__esp-wifi-connect.dir/all: esp-idf/xtensa/CMakeFiles/__idf_xtensa.dir/all
	$(MAKE) $(MAKESILENT) -f esp-idf/78__esp-wifi-connect/CMakeFiles/__idf_78__esp-wifi-connect.dir/build.make esp-idf/78__esp-wifi-connect/CMakeFiles/__idf_78__esp-wifi-connect.dir/depend
	$(MAKE) $(MAKESILENT) -f esp-idf/78__esp-wifi-connect/CMakeFiles/__idf_78__esp-wifi-connect.dir/build.make esp-idf/78__esp-wifi-connect/CMakeFiles/__idf_78__esp-wifi-connect.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles --progress-num= "Built target __idf_78__esp-wifi-connect"
.PHONY : esp-idf/78__esp-wifi-connect/CMakeFiles/__idf_78__esp-wifi-connect.dir/all

# Build rule for subdir invocation for target.
esp-idf/78__esp-wifi-connect/CMakeFiles/__idf_78__esp-wifi-connect.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles 50
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 esp-idf/78__esp-wifi-connect/CMakeFiles/__idf_78__esp-wifi-connect.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles 0
.PHONY : esp-idf/78__esp-wifi-connect/CMakeFiles/__idf_78__esp-wifi-connect.dir/rule

# Convenience name for target.
__idf_78__esp-wifi-connect: esp-idf/78__esp-wifi-connect/CMakeFiles/__idf_78__esp-wifi-connect.dir/rule
.PHONY : __idf_78__esp-wifi-connect

# clean rule for target.
esp-idf/78__esp-wifi-connect/CMakeFiles/__idf_78__esp-wifi-connect.dir/clean:
	$(MAKE) $(MAKESILENT) -f esp-idf/78__esp-wifi-connect/CMakeFiles/__idf_78__esp-wifi-connect.dir/build.make esp-idf/78__esp-wifi-connect/CMakeFiles/__idf_78__esp-wifi-connect.dir/clean
.PHONY : esp-idf/78__esp-wifi-connect/CMakeFiles/__idf_78__esp-wifi-connect.dir/clean

#=============================================================================
# Target rules for target esp-idf/espressif__button/CMakeFiles/__idf_espressif__button.dir

# All Build rule for target.
esp-idf/espressif__button/CMakeFiles/__idf_espressif__button.dir/all: esp-idf/xtensa/CMakeFiles/__idf_xtensa.dir/all
	$(MAKE) $(MAKESILENT) -f esp-idf/espressif__button/CMakeFiles/__idf_espressif__button.dir/build.make esp-idf/espressif__button/CMakeFiles/__idf_espressif__button.dir/depend
	$(MAKE) $(MAKESILENT) -f esp-idf/espressif__button/CMakeFiles/__idf_espressif__button.dir/build.make esp-idf/espressif__button/CMakeFiles/__idf_espressif__button.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles --progress-num= "Built target __idf_espressif__button"
.PHONY : esp-idf/espressif__button/CMakeFiles/__idf_espressif__button.dir/all

# Build rule for subdir invocation for target.
esp-idf/espressif__button/CMakeFiles/__idf_espressif__button.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles 50
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 esp-idf/espressif__button/CMakeFiles/__idf_espressif__button.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles 0
.PHONY : esp-idf/espressif__button/CMakeFiles/__idf_espressif__button.dir/rule

# Convenience name for target.
__idf_espressif__button: esp-idf/espressif__button/CMakeFiles/__idf_espressif__button.dir/rule
.PHONY : __idf_espressif__button

# clean rule for target.
esp-idf/espressif__button/CMakeFiles/__idf_espressif__button.dir/clean:
	$(MAKE) $(MAKESILENT) -f esp-idf/espressif__button/CMakeFiles/__idf_espressif__button.dir/build.make esp-idf/espressif__button/CMakeFiles/__idf_espressif__button.dir/clean
.PHONY : esp-idf/espressif__button/CMakeFiles/__idf_espressif__button.dir/clean

#=============================================================================
# Target rules for target esp-idf/espressif__esp-dsp/CMakeFiles/__idf_espressif__esp-dsp.dir

# All Build rule for target.
esp-idf/espressif__esp-dsp/CMakeFiles/__idf_espressif__esp-dsp.dir/all: esp-idf/xtensa/CMakeFiles/__idf_xtensa.dir/all
	$(MAKE) $(MAKESILENT) -f esp-idf/espressif__esp-dsp/CMakeFiles/__idf_espressif__esp-dsp.dir/build.make esp-idf/espressif__esp-dsp/CMakeFiles/__idf_espressif__esp-dsp.dir/depend
	$(MAKE) $(MAKESILENT) -f esp-idf/espressif__esp-dsp/CMakeFiles/__idf_espressif__esp-dsp.dir/build.make esp-idf/espressif__esp-dsp/CMakeFiles/__idf_espressif__esp-dsp.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles --progress-num=31,32,33,34,35,36,37,38 "Built target __idf_espressif__esp-dsp"
.PHONY : esp-idf/espressif__esp-dsp/CMakeFiles/__idf_espressif__esp-dsp.dir/all

# Build rule for subdir invocation for target.
esp-idf/espressif__esp-dsp/CMakeFiles/__idf_espressif__esp-dsp.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles 58
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 esp-idf/espressif__esp-dsp/CMakeFiles/__idf_espressif__esp-dsp.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles 0
.PHONY : esp-idf/espressif__esp-dsp/CMakeFiles/__idf_espressif__esp-dsp.dir/rule

# Convenience name for target.
__idf_espressif__esp-dsp: esp-idf/espressif__esp-dsp/CMakeFiles/__idf_espressif__esp-dsp.dir/rule
.PHONY : __idf_espressif__esp-dsp

# clean rule for target.
esp-idf/espressif__esp-dsp/CMakeFiles/__idf_espressif__esp-dsp.dir/clean:
	$(MAKE) $(MAKESILENT) -f esp-idf/espressif__esp-dsp/CMakeFiles/__idf_espressif__esp-dsp.dir/build.make esp-idf/espressif__esp-dsp/CMakeFiles/__idf_espressif__esp-dsp.dir/clean
.PHONY : esp-idf/espressif__esp-dsp/CMakeFiles/__idf_espressif__esp-dsp.dir/clean

#=============================================================================
# Target rules for target esp-idf/espressif__esp-sr/CMakeFiles/__idf_espressif__esp-sr.dir

# All Build rule for target.
esp-idf/espressif__esp-sr/CMakeFiles/__idf_espressif__esp-sr.dir/all: esp-idf/xtensa/CMakeFiles/__idf_xtensa.dir/all
esp-idf/espressif__esp-sr/CMakeFiles/__idf_espressif__esp-sr.dir/all: esp-idf/json/CMakeFiles/__idf_json.dir/all
esp-idf/espressif__esp-sr/CMakeFiles/__idf_espressif__esp-sr.dir/all: esp-idf/spiffs/CMakeFiles/__idf_spiffs.dir/all
esp-idf/espressif__esp-sr/CMakeFiles/__idf_espressif__esp-sr.dir/all: esp-idf/espressif__esp-dsp/CMakeFiles/__idf_espressif__esp-dsp.dir/all
	$(MAKE) $(MAKESILENT) -f esp-idf/espressif__esp-sr/CMakeFiles/__idf_espressif__esp-sr.dir/build.make esp-idf/espressif__esp-sr/CMakeFiles/__idf_espressif__esp-sr.dir/depend
	$(MAKE) $(MAKESILENT) -f esp-idf/espressif__esp-sr/CMakeFiles/__idf_espressif__esp-sr.dir/build.make esp-idf/espressif__esp-sr/CMakeFiles/__idf_espressif__esp-sr.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles --progress-num= "Built target __idf_espressif__esp-sr"
.PHONY : esp-idf/espressif__esp-sr/CMakeFiles/__idf_espressif__esp-sr.dir/all

# Build rule for subdir invocation for target.
esp-idf/espressif__esp-sr/CMakeFiles/__idf_espressif__esp-sr.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles 58
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 esp-idf/espressif__esp-sr/CMakeFiles/__idf_espressif__esp-sr.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles 0
.PHONY : esp-idf/espressif__esp-sr/CMakeFiles/__idf_espressif__esp-sr.dir/rule

# Convenience name for target.
__idf_espressif__esp-sr: esp-idf/espressif__esp-sr/CMakeFiles/__idf_espressif__esp-sr.dir/rule
.PHONY : __idf_espressif__esp-sr

# clean rule for target.
esp-idf/espressif__esp-sr/CMakeFiles/__idf_espressif__esp-sr.dir/clean:
	$(MAKE) $(MAKESILENT) -f esp-idf/espressif__esp-sr/CMakeFiles/__idf_espressif__esp-sr.dir/build.make esp-idf/espressif__esp-sr/CMakeFiles/__idf_espressif__esp-sr.dir/clean
.PHONY : esp-idf/espressif__esp-sr/CMakeFiles/__idf_espressif__esp-sr.dir/clean

#=============================================================================
# Target rules for target esp-idf/espressif__esp-sr/CMakeFiles/srmodels_bin.dir

# All Build rule for target.
esp-idf/espressif__esp-sr/CMakeFiles/srmodels_bin.dir/all:
	$(MAKE) $(MAKESILENT) -f esp-idf/espressif__esp-sr/CMakeFiles/srmodels_bin.dir/build.make esp-idf/espressif__esp-sr/CMakeFiles/srmodels_bin.dir/depend
	$(MAKE) $(MAKESILENT) -f esp-idf/espressif__esp-sr/CMakeFiles/srmodels_bin.dir/build.make esp-idf/espressif__esp-sr/CMakeFiles/srmodels_bin.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles --progress-num= "Built target srmodels_bin"
.PHONY : esp-idf/espressif__esp-sr/CMakeFiles/srmodels_bin.dir/all

# Build rule for subdir invocation for target.
esp-idf/espressif__esp-sr/CMakeFiles/srmodels_bin.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles 0
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 esp-idf/espressif__esp-sr/CMakeFiles/srmodels_bin.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles 0
.PHONY : esp-idf/espressif__esp-sr/CMakeFiles/srmodels_bin.dir/rule

# Convenience name for target.
srmodels_bin: esp-idf/espressif__esp-sr/CMakeFiles/srmodels_bin.dir/rule
.PHONY : srmodels_bin

# clean rule for target.
esp-idf/espressif__esp-sr/CMakeFiles/srmodels_bin.dir/clean:
	$(MAKE) $(MAKESILENT) -f esp-idf/espressif__esp-sr/CMakeFiles/srmodels_bin.dir/build.make esp-idf/espressif__esp-sr/CMakeFiles/srmodels_bin.dir/clean
.PHONY : esp-idf/espressif__esp-sr/CMakeFiles/srmodels_bin.dir/clean

#=============================================================================
# Target rules for target esp-idf/espressif__esp_codec_dev/CMakeFiles/__idf_espressif__esp_codec_dev.dir

# All Build rule for target.
esp-idf/espressif__esp_codec_dev/CMakeFiles/__idf_espressif__esp_codec_dev.dir/all: esp-idf/xtensa/CMakeFiles/__idf_xtensa.dir/all
	$(MAKE) $(MAKESILENT) -f esp-idf/espressif__esp_codec_dev/CMakeFiles/__idf_espressif__esp_codec_dev.dir/build.make esp-idf/espressif__esp_codec_dev/CMakeFiles/__idf_espressif__esp_codec_dev.dir/depend
	$(MAKE) $(MAKESILENT) -f esp-idf/espressif__esp_codec_dev/CMakeFiles/__idf_espressif__esp_codec_dev.dir/build.make esp-idf/espressif__esp_codec_dev/CMakeFiles/__idf_espressif__esp_codec_dev.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles --progress-num=39 "Built target __idf_espressif__esp_codec_dev"
.PHONY : esp-idf/espressif__esp_codec_dev/CMakeFiles/__idf_espressif__esp_codec_dev.dir/all

# Build rule for subdir invocation for target.
esp-idf/espressif__esp_codec_dev/CMakeFiles/__idf_espressif__esp_codec_dev.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles 51
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 esp-idf/espressif__esp_codec_dev/CMakeFiles/__idf_espressif__esp_codec_dev.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles 0
.PHONY : esp-idf/espressif__esp_codec_dev/CMakeFiles/__idf_espressif__esp_codec_dev.dir/rule

# Convenience name for target.
__idf_espressif__esp_codec_dev: esp-idf/espressif__esp_codec_dev/CMakeFiles/__idf_espressif__esp_codec_dev.dir/rule
.PHONY : __idf_espressif__esp_codec_dev

# clean rule for target.
esp-idf/espressif__esp_codec_dev/CMakeFiles/__idf_espressif__esp_codec_dev.dir/clean:
	$(MAKE) $(MAKESILENT) -f esp-idf/espressif__esp_codec_dev/CMakeFiles/__idf_espressif__esp_codec_dev.dir/build.make esp-idf/espressif__esp_codec_dev/CMakeFiles/__idf_espressif__esp_codec_dev.dir/clean
.PHONY : esp-idf/espressif__esp_codec_dev/CMakeFiles/__idf_espressif__esp_codec_dev.dir/clean

#=============================================================================
# Target rules for target esp-idf/lvgl__lvgl/CMakeFiles/__idf_lvgl__lvgl.dir

# All Build rule for target.
esp-idf/lvgl__lvgl/CMakeFiles/__idf_lvgl__lvgl.dir/all: esp-idf/xtensa/CMakeFiles/__idf_xtensa.dir/all
	$(MAKE) $(MAKESILENT) -f esp-idf/lvgl__lvgl/CMakeFiles/__idf_lvgl__lvgl.dir/build.make esp-idf/lvgl__lvgl/CMakeFiles/__idf_lvgl__lvgl.dir/depend
	$(MAKE) $(MAKESILENT) -f esp-idf/lvgl__lvgl/CMakeFiles/__idf_lvgl__lvgl.dir/build.make esp-idf/lvgl__lvgl/CMakeFiles/__idf_lvgl__lvgl.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles --progress-num=46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66 "Built target __idf_lvgl__lvgl"
.PHONY : esp-idf/lvgl__lvgl/CMakeFiles/__idf_lvgl__lvgl.dir/all

# Build rule for subdir invocation for target.
esp-idf/lvgl__lvgl/CMakeFiles/__idf_lvgl__lvgl.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles 71
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 esp-idf/lvgl__lvgl/CMakeFiles/__idf_lvgl__lvgl.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles 0
.PHONY : esp-idf/lvgl__lvgl/CMakeFiles/__idf_lvgl__lvgl.dir/rule

# Convenience name for target.
__idf_lvgl__lvgl: esp-idf/lvgl__lvgl/CMakeFiles/__idf_lvgl__lvgl.dir/rule
.PHONY : __idf_lvgl__lvgl

# clean rule for target.
esp-idf/lvgl__lvgl/CMakeFiles/__idf_lvgl__lvgl.dir/clean:
	$(MAKE) $(MAKESILENT) -f esp-idf/lvgl__lvgl/CMakeFiles/__idf_lvgl__lvgl.dir/build.make esp-idf/lvgl__lvgl/CMakeFiles/__idf_lvgl__lvgl.dir/clean
.PHONY : esp-idf/lvgl__lvgl/CMakeFiles/__idf_lvgl__lvgl.dir/clean

#=============================================================================
# Target rules for target esp-idf/espressif__esp_lvgl_port/CMakeFiles/lvgl_port_lib.dir

# All Build rule for target.
esp-idf/espressif__esp_lvgl_port/CMakeFiles/lvgl_port_lib.dir/all: esp-idf/xtensa/CMakeFiles/__idf_xtensa.dir/all
esp-idf/espressif__esp_lvgl_port/CMakeFiles/lvgl_port_lib.dir/all: esp-idf/esp_lcd/CMakeFiles/__idf_esp_lcd.dir/all
esp-idf/espressif__esp_lvgl_port/CMakeFiles/lvgl_port_lib.dir/all: esp-idf/espressif__button/CMakeFiles/__idf_espressif__button.dir/all
esp-idf/espressif__esp_lvgl_port/CMakeFiles/lvgl_port_lib.dir/all: esp-idf/lvgl__lvgl/CMakeFiles/__idf_lvgl__lvgl.dir/all
	$(MAKE) $(MAKESILENT) -f esp-idf/espressif__esp_lvgl_port/CMakeFiles/lvgl_port_lib.dir/build.make esp-idf/espressif__esp_lvgl_port/CMakeFiles/lvgl_port_lib.dir/depend
	$(MAKE) $(MAKESILENT) -f esp-idf/espressif__esp_lvgl_port/CMakeFiles/lvgl_port_lib.dir/build.make esp-idf/espressif__esp_lvgl_port/CMakeFiles/lvgl_port_lib.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles --progress-num= "Built target lvgl_port_lib"
.PHONY : esp-idf/espressif__esp_lvgl_port/CMakeFiles/lvgl_port_lib.dir/all

# Build rule for subdir invocation for target.
esp-idf/espressif__esp_lvgl_port/CMakeFiles/lvgl_port_lib.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles 71
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 esp-idf/espressif__esp_lvgl_port/CMakeFiles/lvgl_port_lib.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles 0
.PHONY : esp-idf/espressif__esp_lvgl_port/CMakeFiles/lvgl_port_lib.dir/rule

# Convenience name for target.
lvgl_port_lib: esp-idf/espressif__esp_lvgl_port/CMakeFiles/lvgl_port_lib.dir/rule
.PHONY : lvgl_port_lib

# clean rule for target.
esp-idf/espressif__esp_lvgl_port/CMakeFiles/lvgl_port_lib.dir/clean:
	$(MAKE) $(MAKESILENT) -f esp-idf/espressif__esp_lvgl_port/CMakeFiles/lvgl_port_lib.dir/build.make esp-idf/espressif__esp_lvgl_port/CMakeFiles/lvgl_port_lib.dir/clean
.PHONY : esp-idf/espressif__esp_lvgl_port/CMakeFiles/lvgl_port_lib.dir/clean

#=============================================================================
# Target rules for target esp-idf/espressif__led_strip/CMakeFiles/__idf_espressif__led_strip.dir

# All Build rule for target.
esp-idf/espressif__led_strip/CMakeFiles/__idf_espressif__led_strip.dir/all: esp-idf/xtensa/CMakeFiles/__idf_xtensa.dir/all
	$(MAKE) $(MAKESILENT) -f esp-idf/espressif__led_strip/CMakeFiles/__idf_espressif__led_strip.dir/build.make esp-idf/espressif__led_strip/CMakeFiles/__idf_espressif__led_strip.dir/depend
	$(MAKE) $(MAKESILENT) -f esp-idf/espressif__led_strip/CMakeFiles/__idf_espressif__led_strip.dir/build.make esp-idf/espressif__led_strip/CMakeFiles/__idf_espressif__led_strip.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles --progress-num=40 "Built target __idf_espressif__led_strip"
.PHONY : esp-idf/espressif__led_strip/CMakeFiles/__idf_espressif__led_strip.dir/all

# Build rule for subdir invocation for target.
esp-idf/espressif__led_strip/CMakeFiles/__idf_espressif__led_strip.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles 51
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 esp-idf/espressif__led_strip/CMakeFiles/__idf_espressif__led_strip.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles 0
.PHONY : esp-idf/espressif__led_strip/CMakeFiles/__idf_espressif__led_strip.dir/rule

# Convenience name for target.
__idf_espressif__led_strip: esp-idf/espressif__led_strip/CMakeFiles/__idf_espressif__led_strip.dir/rule
.PHONY : __idf_espressif__led_strip

# clean rule for target.
esp-idf/espressif__led_strip/CMakeFiles/__idf_espressif__led_strip.dir/clean:
	$(MAKE) $(MAKESILENT) -f esp-idf/espressif__led_strip/CMakeFiles/__idf_espressif__led_strip.dir/build.make esp-idf/espressif__led_strip/CMakeFiles/__idf_espressif__led_strip.dir/clean
.PHONY : esp-idf/espressif__led_strip/CMakeFiles/__idf_espressif__led_strip.dir/clean

#=============================================================================
# Target rules for target esp-idf/main/CMakeFiles/__idf_main.dir

# All Build rule for target.
esp-idf/main/CMakeFiles/__idf_main.dir/all: esp-idf/xtensa/CMakeFiles/__idf_xtensa.dir/all
esp-idf/main/CMakeFiles/__idf_main.dir/all: esp-idf/app_trace/CMakeFiles/__idf_app_trace.dir/all
esp-idf/main/CMakeFiles/__idf_main.dir/all: esp-idf/unity/CMakeFiles/__idf_unity.dir/all
esp-idf/main/CMakeFiles/__idf_main.dir/all: esp-idf/cmock/CMakeFiles/__idf_cmock.dir/all
esp-idf/main/CMakeFiles/__idf_main.dir/all: esp-idf/console/CMakeFiles/__idf_console.dir/all
esp-idf/main/CMakeFiles/__idf_main.dir/all: esp-idf/esp_driver_cam/CMakeFiles/__idf_esp_driver_cam.dir/all
esp-idf/main/CMakeFiles/__idf_main.dir/all: esp-idf/esp_hid/CMakeFiles/__idf_esp_hid.dir/all
esp-idf/main/CMakeFiles/__idf_main.dir/all: esp-idf/esp_https_server/CMakeFiles/__idf_esp_https_server.dir/all
esp-idf/main/CMakeFiles/__idf_main.dir/all: esp-idf/esp_lcd/CMakeFiles/__idf_esp_lcd.dir/all
esp-idf/main/CMakeFiles/__idf_main.dir/all: esp-idf/protobuf-c/CMakeFiles/__idf_protobuf-c.dir/all
esp-idf/main/CMakeFiles/__idf_main.dir/all: esp-idf/protocomm/CMakeFiles/__idf_protocomm.dir/all
esp-idf/main/CMakeFiles/__idf_main.dir/all: esp-idf/esp_local_ctrl/CMakeFiles/__idf_esp_local_ctrl.dir/all
esp-idf/main/CMakeFiles/__idf_main.dir/all: esp-idf/espcoredump/CMakeFiles/__idf_espcoredump.dir/all
esp-idf/main/CMakeFiles/__idf_main.dir/all: esp-idf/wear_levelling/CMakeFiles/__idf_wear_levelling.dir/all
esp-idf/main/CMakeFiles/__idf_main.dir/all: esp-idf/fatfs/CMakeFiles/__idf_fatfs.dir/all
esp-idf/main/CMakeFiles/__idf_main.dir/all: esp-idf/json/CMakeFiles/__idf_json.dir/all
esp-idf/main/CMakeFiles/__idf_main.dir/all: esp-idf/mqtt/CMakeFiles/__idf_mqtt.dir/all
esp-idf/main/CMakeFiles/__idf_main.dir/all: esp-idf/nvs_sec_provider/CMakeFiles/__idf_nvs_sec_provider.dir/all
esp-idf/main/CMakeFiles/__idf_main.dir/all: esp-idf/perfmon/CMakeFiles/__idf_perfmon.dir/all
esp-idf/main/CMakeFiles/__idf_main.dir/all: esp-idf/spiffs/CMakeFiles/__idf_spiffs.dir/all
esp-idf/main/CMakeFiles/__idf_main.dir/all: esp-idf/touch_element/CMakeFiles/__idf_touch_element.dir/all
esp-idf/main/CMakeFiles/__idf_main.dir/all: esp-idf/usb/CMakeFiles/__idf_usb.dir/all
esp-idf/main/CMakeFiles/__idf_main.dir/all: esp-idf/wifi_provisioning/CMakeFiles/__idf_wifi_provisioning.dir/all
esp-idf/main/CMakeFiles/__idf_main.dir/all: esp-idf/78__esp-ml307/CMakeFiles/__idf_78__esp-ml307.dir/all
esp-idf/main/CMakeFiles/__idf_main.dir/all: esp-idf/78__esp-opus/CMakeFiles/__idf_78__esp-opus.dir/all
esp-idf/main/CMakeFiles/__idf_main.dir/all: esp-idf/78__esp-opus-encoder/CMakeFiles/__idf_78__esp-opus-encoder.dir/all
esp-idf/main/CMakeFiles/__idf_main.dir/all: esp-idf/78__esp-wifi-connect/CMakeFiles/__idf_78__esp-wifi-connect.dir/all
esp-idf/main/CMakeFiles/__idf_main.dir/all: esp-idf/espressif__button/CMakeFiles/__idf_espressif__button.dir/all
esp-idf/main/CMakeFiles/__idf_main.dir/all: esp-idf/espressif__esp-dsp/CMakeFiles/__idf_espressif__esp-dsp.dir/all
esp-idf/main/CMakeFiles/__idf_main.dir/all: esp-idf/espressif__esp-sr/CMakeFiles/__idf_espressif__esp-sr.dir/all
esp-idf/main/CMakeFiles/__idf_main.dir/all: esp-idf/espressif__esp_codec_dev/CMakeFiles/__idf_espressif__esp_codec_dev.dir/all
esp-idf/main/CMakeFiles/__idf_main.dir/all: esp-idf/lvgl__lvgl/CMakeFiles/__idf_lvgl__lvgl.dir/all
esp-idf/main/CMakeFiles/__idf_main.dir/all: esp-idf/espressif__esp_lvgl_port/CMakeFiles/lvgl_port_lib.dir/all
esp-idf/main/CMakeFiles/__idf_main.dir/all: esp-idf/espressif__led_strip/CMakeFiles/__idf_espressif__led_strip.dir/all
	$(MAKE) $(MAKESILENT) -f esp-idf/main/CMakeFiles/__idf_main.dir/build.make esp-idf/main/CMakeFiles/__idf_main.dir/depend
	$(MAKE) $(MAKESILENT) -f esp-idf/main/CMakeFiles/__idf_main.dir/build.make esp-idf/main/CMakeFiles/__idf_main.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles --progress-num=72,73,74 "Built target __idf_main"
.PHONY : esp-idf/main/CMakeFiles/__idf_main.dir/all

# Build rule for subdir invocation for target.
esp-idf/main/CMakeFiles/__idf_main.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles 99
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 esp-idf/main/CMakeFiles/__idf_main.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles 0
.PHONY : esp-idf/main/CMakeFiles/__idf_main.dir/rule

# Convenience name for target.
__idf_main: esp-idf/main/CMakeFiles/__idf_main.dir/rule
.PHONY : __idf_main

# clean rule for target.
esp-idf/main/CMakeFiles/__idf_main.dir/clean:
	$(MAKE) $(MAKESILENT) -f esp-idf/main/CMakeFiles/__idf_main.dir/build.make esp-idf/main/CMakeFiles/__idf_main.dir/clean
.PHONY : esp-idf/main/CMakeFiles/__idf_main.dir/clean

#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system

