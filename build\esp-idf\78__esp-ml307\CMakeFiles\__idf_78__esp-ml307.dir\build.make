# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.22

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build

# Include any dependencies generated for this target.
include esp-idf/78__esp-ml307/CMakeFiles/__idf_78__esp-ml307.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include esp-idf/78__esp-ml307/CMakeFiles/__idf_78__esp-ml307.dir/compiler_depend.make

# Include the progress variables for this target.
include esp-idf/78__esp-ml307/CMakeFiles/__idf_78__esp-ml307.dir/progress.make

# Include the compile flags for this target's objects.
include esp-idf/78__esp-ml307/CMakeFiles/__idf_78__esp-ml307.dir/flags.make

esp-idf/78__esp-ml307/CMakeFiles/__idf_78__esp-ml307.dir/ml307_at_modem.cc.obj: esp-idf/78__esp-ml307/CMakeFiles/__idf_78__esp-ml307.dir/flags.make
esp-idf/78__esp-ml307/CMakeFiles/__idf_78__esp-ml307.dir/ml307_at_modem.cc.obj: ../managed_components/78__esp-ml307/ml307_at_modem.cc
esp-idf/78__esp-ml307/CMakeFiles/__idf_78__esp-ml307.dir/ml307_at_modem.cc.obj: esp-idf/78__esp-ml307/CMakeFiles/__idf_78__esp-ml307.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building CXX object esp-idf/78__esp-ml307/CMakeFiles/__idf_78__esp-ml307.dir/ml307_at_modem.cc.obj"
	cd /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/esp-idf/78__esp-ml307 && /home/<USER>/.espressif/tools/xtensa-esp-elf/esp-13.2.0_20240530/xtensa-esp-elf/bin/xtensa-esp32s3-elf-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT esp-idf/78__esp-ml307/CMakeFiles/__idf_78__esp-ml307.dir/ml307_at_modem.cc.obj -MF CMakeFiles/__idf_78__esp-ml307.dir/ml307_at_modem.cc.obj.d -o CMakeFiles/__idf_78__esp-ml307.dir/ml307_at_modem.cc.obj -c /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/managed_components/78__esp-ml307/ml307_at_modem.cc

esp-idf/78__esp-ml307/CMakeFiles/__idf_78__esp-ml307.dir/ml307_at_modem.cc.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/__idf_78__esp-ml307.dir/ml307_at_modem.cc.i"
	cd /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/esp-idf/78__esp-ml307 && /home/<USER>/.espressif/tools/xtensa-esp-elf/esp-13.2.0_20240530/xtensa-esp-elf/bin/xtensa-esp32s3-elf-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/managed_components/78__esp-ml307/ml307_at_modem.cc > CMakeFiles/__idf_78__esp-ml307.dir/ml307_at_modem.cc.i

esp-idf/78__esp-ml307/CMakeFiles/__idf_78__esp-ml307.dir/ml307_at_modem.cc.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/__idf_78__esp-ml307.dir/ml307_at_modem.cc.s"
	cd /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/esp-idf/78__esp-ml307 && /home/<USER>/.espressif/tools/xtensa-esp-elf/esp-13.2.0_20240530/xtensa-esp-elf/bin/xtensa-esp32s3-elf-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/managed_components/78__esp-ml307/ml307_at_modem.cc -o CMakeFiles/__idf_78__esp-ml307.dir/ml307_at_modem.cc.s

esp-idf/78__esp-ml307/CMakeFiles/__idf_78__esp-ml307.dir/ml307_ssl_transport.cc.obj: esp-idf/78__esp-ml307/CMakeFiles/__idf_78__esp-ml307.dir/flags.make
esp-idf/78__esp-ml307/CMakeFiles/__idf_78__esp-ml307.dir/ml307_ssl_transport.cc.obj: ../managed_components/78__esp-ml307/ml307_ssl_transport.cc
esp-idf/78__esp-ml307/CMakeFiles/__idf_78__esp-ml307.dir/ml307_ssl_transport.cc.obj: esp-idf/78__esp-ml307/CMakeFiles/__idf_78__esp-ml307.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Building CXX object esp-idf/78__esp-ml307/CMakeFiles/__idf_78__esp-ml307.dir/ml307_ssl_transport.cc.obj"
	cd /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/esp-idf/78__esp-ml307 && /home/<USER>/.espressif/tools/xtensa-esp-elf/esp-13.2.0_20240530/xtensa-esp-elf/bin/xtensa-esp32s3-elf-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT esp-idf/78__esp-ml307/CMakeFiles/__idf_78__esp-ml307.dir/ml307_ssl_transport.cc.obj -MF CMakeFiles/__idf_78__esp-ml307.dir/ml307_ssl_transport.cc.obj.d -o CMakeFiles/__idf_78__esp-ml307.dir/ml307_ssl_transport.cc.obj -c /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/managed_components/78__esp-ml307/ml307_ssl_transport.cc

esp-idf/78__esp-ml307/CMakeFiles/__idf_78__esp-ml307.dir/ml307_ssl_transport.cc.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/__idf_78__esp-ml307.dir/ml307_ssl_transport.cc.i"
	cd /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/esp-idf/78__esp-ml307 && /home/<USER>/.espressif/tools/xtensa-esp-elf/esp-13.2.0_20240530/xtensa-esp-elf/bin/xtensa-esp32s3-elf-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/managed_components/78__esp-ml307/ml307_ssl_transport.cc > CMakeFiles/__idf_78__esp-ml307.dir/ml307_ssl_transport.cc.i

esp-idf/78__esp-ml307/CMakeFiles/__idf_78__esp-ml307.dir/ml307_ssl_transport.cc.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/__idf_78__esp-ml307.dir/ml307_ssl_transport.cc.s"
	cd /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/esp-idf/78__esp-ml307 && /home/<USER>/.espressif/tools/xtensa-esp-elf/esp-13.2.0_20240530/xtensa-esp-elf/bin/xtensa-esp32s3-elf-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/managed_components/78__esp-ml307/ml307_ssl_transport.cc -o CMakeFiles/__idf_78__esp-ml307.dir/ml307_ssl_transport.cc.s

esp-idf/78__esp-ml307/CMakeFiles/__idf_78__esp-ml307.dir/ml307_http.cc.obj: esp-idf/78__esp-ml307/CMakeFiles/__idf_78__esp-ml307.dir/flags.make
esp-idf/78__esp-ml307/CMakeFiles/__idf_78__esp-ml307.dir/ml307_http.cc.obj: ../managed_components/78__esp-ml307/ml307_http.cc
esp-idf/78__esp-ml307/CMakeFiles/__idf_78__esp-ml307.dir/ml307_http.cc.obj: esp-idf/78__esp-ml307/CMakeFiles/__idf_78__esp-ml307.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_3) "Building CXX object esp-idf/78__esp-ml307/CMakeFiles/__idf_78__esp-ml307.dir/ml307_http.cc.obj"
	cd /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/esp-idf/78__esp-ml307 && /home/<USER>/.espressif/tools/xtensa-esp-elf/esp-13.2.0_20240530/xtensa-esp-elf/bin/xtensa-esp32s3-elf-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT esp-idf/78__esp-ml307/CMakeFiles/__idf_78__esp-ml307.dir/ml307_http.cc.obj -MF CMakeFiles/__idf_78__esp-ml307.dir/ml307_http.cc.obj.d -o CMakeFiles/__idf_78__esp-ml307.dir/ml307_http.cc.obj -c /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/managed_components/78__esp-ml307/ml307_http.cc

esp-idf/78__esp-ml307/CMakeFiles/__idf_78__esp-ml307.dir/ml307_http.cc.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/__idf_78__esp-ml307.dir/ml307_http.cc.i"
	cd /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/esp-idf/78__esp-ml307 && /home/<USER>/.espressif/tools/xtensa-esp-elf/esp-13.2.0_20240530/xtensa-esp-elf/bin/xtensa-esp32s3-elf-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/managed_components/78__esp-ml307/ml307_http.cc > CMakeFiles/__idf_78__esp-ml307.dir/ml307_http.cc.i

esp-idf/78__esp-ml307/CMakeFiles/__idf_78__esp-ml307.dir/ml307_http.cc.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/__idf_78__esp-ml307.dir/ml307_http.cc.s"
	cd /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/esp-idf/78__esp-ml307 && /home/<USER>/.espressif/tools/xtensa-esp-elf/esp-13.2.0_20240530/xtensa-esp-elf/bin/xtensa-esp32s3-elf-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/managed_components/78__esp-ml307/ml307_http.cc -o CMakeFiles/__idf_78__esp-ml307.dir/ml307_http.cc.s

esp-idf/78__esp-ml307/CMakeFiles/__idf_78__esp-ml307.dir/ml307_mqtt.cc.obj: esp-idf/78__esp-ml307/CMakeFiles/__idf_78__esp-ml307.dir/flags.make
esp-idf/78__esp-ml307/CMakeFiles/__idf_78__esp-ml307.dir/ml307_mqtt.cc.obj: ../managed_components/78__esp-ml307/ml307_mqtt.cc
esp-idf/78__esp-ml307/CMakeFiles/__idf_78__esp-ml307.dir/ml307_mqtt.cc.obj: esp-idf/78__esp-ml307/CMakeFiles/__idf_78__esp-ml307.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_4) "Building CXX object esp-idf/78__esp-ml307/CMakeFiles/__idf_78__esp-ml307.dir/ml307_mqtt.cc.obj"
	cd /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/esp-idf/78__esp-ml307 && /home/<USER>/.espressif/tools/xtensa-esp-elf/esp-13.2.0_20240530/xtensa-esp-elf/bin/xtensa-esp32s3-elf-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT esp-idf/78__esp-ml307/CMakeFiles/__idf_78__esp-ml307.dir/ml307_mqtt.cc.obj -MF CMakeFiles/__idf_78__esp-ml307.dir/ml307_mqtt.cc.obj.d -o CMakeFiles/__idf_78__esp-ml307.dir/ml307_mqtt.cc.obj -c /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/managed_components/78__esp-ml307/ml307_mqtt.cc

esp-idf/78__esp-ml307/CMakeFiles/__idf_78__esp-ml307.dir/ml307_mqtt.cc.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/__idf_78__esp-ml307.dir/ml307_mqtt.cc.i"
	cd /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/esp-idf/78__esp-ml307 && /home/<USER>/.espressif/tools/xtensa-esp-elf/esp-13.2.0_20240530/xtensa-esp-elf/bin/xtensa-esp32s3-elf-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/managed_components/78__esp-ml307/ml307_mqtt.cc > CMakeFiles/__idf_78__esp-ml307.dir/ml307_mqtt.cc.i

esp-idf/78__esp-ml307/CMakeFiles/__idf_78__esp-ml307.dir/ml307_mqtt.cc.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/__idf_78__esp-ml307.dir/ml307_mqtt.cc.s"
	cd /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/esp-idf/78__esp-ml307 && /home/<USER>/.espressif/tools/xtensa-esp-elf/esp-13.2.0_20240530/xtensa-esp-elf/bin/xtensa-esp32s3-elf-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/managed_components/78__esp-ml307/ml307_mqtt.cc -o CMakeFiles/__idf_78__esp-ml307.dir/ml307_mqtt.cc.s

esp-idf/78__esp-ml307/CMakeFiles/__idf_78__esp-ml307.dir/ml307_udp.cc.obj: esp-idf/78__esp-ml307/CMakeFiles/__idf_78__esp-ml307.dir/flags.make
esp-idf/78__esp-ml307/CMakeFiles/__idf_78__esp-ml307.dir/ml307_udp.cc.obj: ../managed_components/78__esp-ml307/ml307_udp.cc
esp-idf/78__esp-ml307/CMakeFiles/__idf_78__esp-ml307.dir/ml307_udp.cc.obj: esp-idf/78__esp-ml307/CMakeFiles/__idf_78__esp-ml307.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_5) "Building CXX object esp-idf/78__esp-ml307/CMakeFiles/__idf_78__esp-ml307.dir/ml307_udp.cc.obj"
	cd /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/esp-idf/78__esp-ml307 && /home/<USER>/.espressif/tools/xtensa-esp-elf/esp-13.2.0_20240530/xtensa-esp-elf/bin/xtensa-esp32s3-elf-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT esp-idf/78__esp-ml307/CMakeFiles/__idf_78__esp-ml307.dir/ml307_udp.cc.obj -MF CMakeFiles/__idf_78__esp-ml307.dir/ml307_udp.cc.obj.d -o CMakeFiles/__idf_78__esp-ml307.dir/ml307_udp.cc.obj -c /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/managed_components/78__esp-ml307/ml307_udp.cc

esp-idf/78__esp-ml307/CMakeFiles/__idf_78__esp-ml307.dir/ml307_udp.cc.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/__idf_78__esp-ml307.dir/ml307_udp.cc.i"
	cd /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/esp-idf/78__esp-ml307 && /home/<USER>/.espressif/tools/xtensa-esp-elf/esp-13.2.0_20240530/xtensa-esp-elf/bin/xtensa-esp32s3-elf-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/managed_components/78__esp-ml307/ml307_udp.cc > CMakeFiles/__idf_78__esp-ml307.dir/ml307_udp.cc.i

esp-idf/78__esp-ml307/CMakeFiles/__idf_78__esp-ml307.dir/ml307_udp.cc.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/__idf_78__esp-ml307.dir/ml307_udp.cc.s"
	cd /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/esp-idf/78__esp-ml307 && /home/<USER>/.espressif/tools/xtensa-esp-elf/esp-13.2.0_20240530/xtensa-esp-elf/bin/xtensa-esp32s3-elf-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/managed_components/78__esp-ml307/ml307_udp.cc -o CMakeFiles/__idf_78__esp-ml307.dir/ml307_udp.cc.s

esp-idf/78__esp-ml307/CMakeFiles/__idf_78__esp-ml307.dir/web_socket.cc.obj: esp-idf/78__esp-ml307/CMakeFiles/__idf_78__esp-ml307.dir/flags.make
esp-idf/78__esp-ml307/CMakeFiles/__idf_78__esp-ml307.dir/web_socket.cc.obj: ../managed_components/78__esp-ml307/web_socket.cc
esp-idf/78__esp-ml307/CMakeFiles/__idf_78__esp-ml307.dir/web_socket.cc.obj: esp-idf/78__esp-ml307/CMakeFiles/__idf_78__esp-ml307.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_6) "Building CXX object esp-idf/78__esp-ml307/CMakeFiles/__idf_78__esp-ml307.dir/web_socket.cc.obj"
	cd /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/esp-idf/78__esp-ml307 && /home/<USER>/.espressif/tools/xtensa-esp-elf/esp-13.2.0_20240530/xtensa-esp-elf/bin/xtensa-esp32s3-elf-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT esp-idf/78__esp-ml307/CMakeFiles/__idf_78__esp-ml307.dir/web_socket.cc.obj -MF CMakeFiles/__idf_78__esp-ml307.dir/web_socket.cc.obj.d -o CMakeFiles/__idf_78__esp-ml307.dir/web_socket.cc.obj -c /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/managed_components/78__esp-ml307/web_socket.cc

esp-idf/78__esp-ml307/CMakeFiles/__idf_78__esp-ml307.dir/web_socket.cc.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/__idf_78__esp-ml307.dir/web_socket.cc.i"
	cd /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/esp-idf/78__esp-ml307 && /home/<USER>/.espressif/tools/xtensa-esp-elf/esp-13.2.0_20240530/xtensa-esp-elf/bin/xtensa-esp32s3-elf-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/managed_components/78__esp-ml307/web_socket.cc > CMakeFiles/__idf_78__esp-ml307.dir/web_socket.cc.i

esp-idf/78__esp-ml307/CMakeFiles/__idf_78__esp-ml307.dir/web_socket.cc.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/__idf_78__esp-ml307.dir/web_socket.cc.s"
	cd /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/esp-idf/78__esp-ml307 && /home/<USER>/.espressif/tools/xtensa-esp-elf/esp-13.2.0_20240530/xtensa-esp-elf/bin/xtensa-esp32s3-elf-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/managed_components/78__esp-ml307/web_socket.cc -o CMakeFiles/__idf_78__esp-ml307.dir/web_socket.cc.s

esp-idf/78__esp-ml307/CMakeFiles/__idf_78__esp-ml307.dir/tls_transport.cc.obj: esp-idf/78__esp-ml307/CMakeFiles/__idf_78__esp-ml307.dir/flags.make
esp-idf/78__esp-ml307/CMakeFiles/__idf_78__esp-ml307.dir/tls_transport.cc.obj: ../managed_components/78__esp-ml307/tls_transport.cc
esp-idf/78__esp-ml307/CMakeFiles/__idf_78__esp-ml307.dir/tls_transport.cc.obj: esp-idf/78__esp-ml307/CMakeFiles/__idf_78__esp-ml307.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_7) "Building CXX object esp-idf/78__esp-ml307/CMakeFiles/__idf_78__esp-ml307.dir/tls_transport.cc.obj"
	cd /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/esp-idf/78__esp-ml307 && /home/<USER>/.espressif/tools/xtensa-esp-elf/esp-13.2.0_20240530/xtensa-esp-elf/bin/xtensa-esp32s3-elf-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT esp-idf/78__esp-ml307/CMakeFiles/__idf_78__esp-ml307.dir/tls_transport.cc.obj -MF CMakeFiles/__idf_78__esp-ml307.dir/tls_transport.cc.obj.d -o CMakeFiles/__idf_78__esp-ml307.dir/tls_transport.cc.obj -c /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/managed_components/78__esp-ml307/tls_transport.cc

esp-idf/78__esp-ml307/CMakeFiles/__idf_78__esp-ml307.dir/tls_transport.cc.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/__idf_78__esp-ml307.dir/tls_transport.cc.i"
	cd /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/esp-idf/78__esp-ml307 && /home/<USER>/.espressif/tools/xtensa-esp-elf/esp-13.2.0_20240530/xtensa-esp-elf/bin/xtensa-esp32s3-elf-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/managed_components/78__esp-ml307/tls_transport.cc > CMakeFiles/__idf_78__esp-ml307.dir/tls_transport.cc.i

esp-idf/78__esp-ml307/CMakeFiles/__idf_78__esp-ml307.dir/tls_transport.cc.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/__idf_78__esp-ml307.dir/tls_transport.cc.s"
	cd /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/esp-idf/78__esp-ml307 && /home/<USER>/.espressif/tools/xtensa-esp-elf/esp-13.2.0_20240530/xtensa-esp-elf/bin/xtensa-esp32s3-elf-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/managed_components/78__esp-ml307/tls_transport.cc -o CMakeFiles/__idf_78__esp-ml307.dir/tls_transport.cc.s

esp-idf/78__esp-ml307/CMakeFiles/__idf_78__esp-ml307.dir/tcp_transport.cc.obj: esp-idf/78__esp-ml307/CMakeFiles/__idf_78__esp-ml307.dir/flags.make
esp-idf/78__esp-ml307/CMakeFiles/__idf_78__esp-ml307.dir/tcp_transport.cc.obj: ../managed_components/78__esp-ml307/tcp_transport.cc
esp-idf/78__esp-ml307/CMakeFiles/__idf_78__esp-ml307.dir/tcp_transport.cc.obj: esp-idf/78__esp-ml307/CMakeFiles/__idf_78__esp-ml307.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_8) "Building CXX object esp-idf/78__esp-ml307/CMakeFiles/__idf_78__esp-ml307.dir/tcp_transport.cc.obj"
	cd /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/esp-idf/78__esp-ml307 && /home/<USER>/.espressif/tools/xtensa-esp-elf/esp-13.2.0_20240530/xtensa-esp-elf/bin/xtensa-esp32s3-elf-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT esp-idf/78__esp-ml307/CMakeFiles/__idf_78__esp-ml307.dir/tcp_transport.cc.obj -MF CMakeFiles/__idf_78__esp-ml307.dir/tcp_transport.cc.obj.d -o CMakeFiles/__idf_78__esp-ml307.dir/tcp_transport.cc.obj -c /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/managed_components/78__esp-ml307/tcp_transport.cc

esp-idf/78__esp-ml307/CMakeFiles/__idf_78__esp-ml307.dir/tcp_transport.cc.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/__idf_78__esp-ml307.dir/tcp_transport.cc.i"
	cd /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/esp-idf/78__esp-ml307 && /home/<USER>/.espressif/tools/xtensa-esp-elf/esp-13.2.0_20240530/xtensa-esp-elf/bin/xtensa-esp32s3-elf-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/managed_components/78__esp-ml307/tcp_transport.cc > CMakeFiles/__idf_78__esp-ml307.dir/tcp_transport.cc.i

esp-idf/78__esp-ml307/CMakeFiles/__idf_78__esp-ml307.dir/tcp_transport.cc.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/__idf_78__esp-ml307.dir/tcp_transport.cc.s"
	cd /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/esp-idf/78__esp-ml307 && /home/<USER>/.espressif/tools/xtensa-esp-elf/esp-13.2.0_20240530/xtensa-esp-elf/bin/xtensa-esp32s3-elf-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/managed_components/78__esp-ml307/tcp_transport.cc -o CMakeFiles/__idf_78__esp-ml307.dir/tcp_transport.cc.s

esp-idf/78__esp-ml307/CMakeFiles/__idf_78__esp-ml307.dir/esp_http.cc.obj: esp-idf/78__esp-ml307/CMakeFiles/__idf_78__esp-ml307.dir/flags.make
esp-idf/78__esp-ml307/CMakeFiles/__idf_78__esp-ml307.dir/esp_http.cc.obj: ../managed_components/78__esp-ml307/esp_http.cc
esp-idf/78__esp-ml307/CMakeFiles/__idf_78__esp-ml307.dir/esp_http.cc.obj: esp-idf/78__esp-ml307/CMakeFiles/__idf_78__esp-ml307.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_9) "Building CXX object esp-idf/78__esp-ml307/CMakeFiles/__idf_78__esp-ml307.dir/esp_http.cc.obj"
	cd /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/esp-idf/78__esp-ml307 && /home/<USER>/.espressif/tools/xtensa-esp-elf/esp-13.2.0_20240530/xtensa-esp-elf/bin/xtensa-esp32s3-elf-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT esp-idf/78__esp-ml307/CMakeFiles/__idf_78__esp-ml307.dir/esp_http.cc.obj -MF CMakeFiles/__idf_78__esp-ml307.dir/esp_http.cc.obj.d -o CMakeFiles/__idf_78__esp-ml307.dir/esp_http.cc.obj -c /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/managed_components/78__esp-ml307/esp_http.cc

esp-idf/78__esp-ml307/CMakeFiles/__idf_78__esp-ml307.dir/esp_http.cc.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/__idf_78__esp-ml307.dir/esp_http.cc.i"
	cd /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/esp-idf/78__esp-ml307 && /home/<USER>/.espressif/tools/xtensa-esp-elf/esp-13.2.0_20240530/xtensa-esp-elf/bin/xtensa-esp32s3-elf-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/managed_components/78__esp-ml307/esp_http.cc > CMakeFiles/__idf_78__esp-ml307.dir/esp_http.cc.i

esp-idf/78__esp-ml307/CMakeFiles/__idf_78__esp-ml307.dir/esp_http.cc.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/__idf_78__esp-ml307.dir/esp_http.cc.s"
	cd /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/esp-idf/78__esp-ml307 && /home/<USER>/.espressif/tools/xtensa-esp-elf/esp-13.2.0_20240530/xtensa-esp-elf/bin/xtensa-esp32s3-elf-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/managed_components/78__esp-ml307/esp_http.cc -o CMakeFiles/__idf_78__esp-ml307.dir/esp_http.cc.s

esp-idf/78__esp-ml307/CMakeFiles/__idf_78__esp-ml307.dir/esp_mqtt.cc.obj: esp-idf/78__esp-ml307/CMakeFiles/__idf_78__esp-ml307.dir/flags.make
esp-idf/78__esp-ml307/CMakeFiles/__idf_78__esp-ml307.dir/esp_mqtt.cc.obj: ../managed_components/78__esp-ml307/esp_mqtt.cc
esp-idf/78__esp-ml307/CMakeFiles/__idf_78__esp-ml307.dir/esp_mqtt.cc.obj: esp-idf/78__esp-ml307/CMakeFiles/__idf_78__esp-ml307.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_10) "Building CXX object esp-idf/78__esp-ml307/CMakeFiles/__idf_78__esp-ml307.dir/esp_mqtt.cc.obj"
	cd /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/esp-idf/78__esp-ml307 && /home/<USER>/.espressif/tools/xtensa-esp-elf/esp-13.2.0_20240530/xtensa-esp-elf/bin/xtensa-esp32s3-elf-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT esp-idf/78__esp-ml307/CMakeFiles/__idf_78__esp-ml307.dir/esp_mqtt.cc.obj -MF CMakeFiles/__idf_78__esp-ml307.dir/esp_mqtt.cc.obj.d -o CMakeFiles/__idf_78__esp-ml307.dir/esp_mqtt.cc.obj -c /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/managed_components/78__esp-ml307/esp_mqtt.cc

esp-idf/78__esp-ml307/CMakeFiles/__idf_78__esp-ml307.dir/esp_mqtt.cc.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/__idf_78__esp-ml307.dir/esp_mqtt.cc.i"
	cd /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/esp-idf/78__esp-ml307 && /home/<USER>/.espressif/tools/xtensa-esp-elf/esp-13.2.0_20240530/xtensa-esp-elf/bin/xtensa-esp32s3-elf-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/managed_components/78__esp-ml307/esp_mqtt.cc > CMakeFiles/__idf_78__esp-ml307.dir/esp_mqtt.cc.i

esp-idf/78__esp-ml307/CMakeFiles/__idf_78__esp-ml307.dir/esp_mqtt.cc.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/__idf_78__esp-ml307.dir/esp_mqtt.cc.s"
	cd /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/esp-idf/78__esp-ml307 && /home/<USER>/.espressif/tools/xtensa-esp-elf/esp-13.2.0_20240530/xtensa-esp-elf/bin/xtensa-esp32s3-elf-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/managed_components/78__esp-ml307/esp_mqtt.cc -o CMakeFiles/__idf_78__esp-ml307.dir/esp_mqtt.cc.s

esp-idf/78__esp-ml307/CMakeFiles/__idf_78__esp-ml307.dir/esp_udp.cc.obj: esp-idf/78__esp-ml307/CMakeFiles/__idf_78__esp-ml307.dir/flags.make
esp-idf/78__esp-ml307/CMakeFiles/__idf_78__esp-ml307.dir/esp_udp.cc.obj: ../managed_components/78__esp-ml307/esp_udp.cc
esp-idf/78__esp-ml307/CMakeFiles/__idf_78__esp-ml307.dir/esp_udp.cc.obj: esp-idf/78__esp-ml307/CMakeFiles/__idf_78__esp-ml307.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_11) "Building CXX object esp-idf/78__esp-ml307/CMakeFiles/__idf_78__esp-ml307.dir/esp_udp.cc.obj"
	cd /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/esp-idf/78__esp-ml307 && /home/<USER>/.espressif/tools/xtensa-esp-elf/esp-13.2.0_20240530/xtensa-esp-elf/bin/xtensa-esp32s3-elf-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT esp-idf/78__esp-ml307/CMakeFiles/__idf_78__esp-ml307.dir/esp_udp.cc.obj -MF CMakeFiles/__idf_78__esp-ml307.dir/esp_udp.cc.obj.d -o CMakeFiles/__idf_78__esp-ml307.dir/esp_udp.cc.obj -c /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/managed_components/78__esp-ml307/esp_udp.cc

esp-idf/78__esp-ml307/CMakeFiles/__idf_78__esp-ml307.dir/esp_udp.cc.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/__idf_78__esp-ml307.dir/esp_udp.cc.i"
	cd /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/esp-idf/78__esp-ml307 && /home/<USER>/.espressif/tools/xtensa-esp-elf/esp-13.2.0_20240530/xtensa-esp-elf/bin/xtensa-esp32s3-elf-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/managed_components/78__esp-ml307/esp_udp.cc > CMakeFiles/__idf_78__esp-ml307.dir/esp_udp.cc.i

esp-idf/78__esp-ml307/CMakeFiles/__idf_78__esp-ml307.dir/esp_udp.cc.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/__idf_78__esp-ml307.dir/esp_udp.cc.s"
	cd /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/esp-idf/78__esp-ml307 && /home/<USER>/.espressif/tools/xtensa-esp-elf/esp-13.2.0_20240530/xtensa-esp-elf/bin/xtensa-esp32s3-elf-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/managed_components/78__esp-ml307/esp_udp.cc -o CMakeFiles/__idf_78__esp-ml307.dir/esp_udp.cc.s

# Object files for target __idf_78__esp-ml307
__idf_78__esp__ml307_OBJECTS = \
"CMakeFiles/__idf_78__esp-ml307.dir/ml307_at_modem.cc.obj" \
"CMakeFiles/__idf_78__esp-ml307.dir/ml307_ssl_transport.cc.obj" \
"CMakeFiles/__idf_78__esp-ml307.dir/ml307_http.cc.obj" \
"CMakeFiles/__idf_78__esp-ml307.dir/ml307_mqtt.cc.obj" \
"CMakeFiles/__idf_78__esp-ml307.dir/ml307_udp.cc.obj" \
"CMakeFiles/__idf_78__esp-ml307.dir/web_socket.cc.obj" \
"CMakeFiles/__idf_78__esp-ml307.dir/tls_transport.cc.obj" \
"CMakeFiles/__idf_78__esp-ml307.dir/tcp_transport.cc.obj" \
"CMakeFiles/__idf_78__esp-ml307.dir/esp_http.cc.obj" \
"CMakeFiles/__idf_78__esp-ml307.dir/esp_mqtt.cc.obj" \
"CMakeFiles/__idf_78__esp-ml307.dir/esp_udp.cc.obj"

# External object files for target __idf_78__esp-ml307
__idf_78__esp__ml307_EXTERNAL_OBJECTS =

esp-idf/78__esp-ml307/lib78__esp-ml307.a: esp-idf/78__esp-ml307/CMakeFiles/__idf_78__esp-ml307.dir/ml307_at_modem.cc.obj
esp-idf/78__esp-ml307/lib78__esp-ml307.a: esp-idf/78__esp-ml307/CMakeFiles/__idf_78__esp-ml307.dir/ml307_ssl_transport.cc.obj
esp-idf/78__esp-ml307/lib78__esp-ml307.a: esp-idf/78__esp-ml307/CMakeFiles/__idf_78__esp-ml307.dir/ml307_http.cc.obj
esp-idf/78__esp-ml307/lib78__esp-ml307.a: esp-idf/78__esp-ml307/CMakeFiles/__idf_78__esp-ml307.dir/ml307_mqtt.cc.obj
esp-idf/78__esp-ml307/lib78__esp-ml307.a: esp-idf/78__esp-ml307/CMakeFiles/__idf_78__esp-ml307.dir/ml307_udp.cc.obj
esp-idf/78__esp-ml307/lib78__esp-ml307.a: esp-idf/78__esp-ml307/CMakeFiles/__idf_78__esp-ml307.dir/web_socket.cc.obj
esp-idf/78__esp-ml307/lib78__esp-ml307.a: esp-idf/78__esp-ml307/CMakeFiles/__idf_78__esp-ml307.dir/tls_transport.cc.obj
esp-idf/78__esp-ml307/lib78__esp-ml307.a: esp-idf/78__esp-ml307/CMakeFiles/__idf_78__esp-ml307.dir/tcp_transport.cc.obj
esp-idf/78__esp-ml307/lib78__esp-ml307.a: esp-idf/78__esp-ml307/CMakeFiles/__idf_78__esp-ml307.dir/esp_http.cc.obj
esp-idf/78__esp-ml307/lib78__esp-ml307.a: esp-idf/78__esp-ml307/CMakeFiles/__idf_78__esp-ml307.dir/esp_mqtt.cc.obj
esp-idf/78__esp-ml307/lib78__esp-ml307.a: esp-idf/78__esp-ml307/CMakeFiles/__idf_78__esp-ml307.dir/esp_udp.cc.obj
esp-idf/78__esp-ml307/lib78__esp-ml307.a: esp-idf/78__esp-ml307/CMakeFiles/__idf_78__esp-ml307.dir/build.make
esp-idf/78__esp-ml307/lib78__esp-ml307.a: esp-idf/78__esp-ml307/CMakeFiles/__idf_78__esp-ml307.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --bold --progress-dir=/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_12) "Linking C static library lib78__esp-ml307.a"
	cd /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/esp-idf/78__esp-ml307 && $(CMAKE_COMMAND) -P CMakeFiles/__idf_78__esp-ml307.dir/cmake_clean_target.cmake
	cd /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/esp-idf/78__esp-ml307 && $(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/__idf_78__esp-ml307.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
esp-idf/78__esp-ml307/CMakeFiles/__idf_78__esp-ml307.dir/build: esp-idf/78__esp-ml307/lib78__esp-ml307.a
.PHONY : esp-idf/78__esp-ml307/CMakeFiles/__idf_78__esp-ml307.dir/build

esp-idf/78__esp-ml307/CMakeFiles/__idf_78__esp-ml307.dir/clean:
	cd /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/esp-idf/78__esp-ml307 && $(CMAKE_COMMAND) -P CMakeFiles/__idf_78__esp-ml307.dir/cmake_clean.cmake
.PHONY : esp-idf/78__esp-ml307/CMakeFiles/__idf_78__esp-ml307.dir/clean

esp-idf/78__esp-ml307/CMakeFiles/__idf_78__esp-ml307.dir/depend:
	cd /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/managed_components/78__esp-ml307 /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/esp-idf/78__esp-ml307 /home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/esp-idf/78__esp-ml307/CMakeFiles/__idf_78__esp-ml307.dir/DependInfo.cmake --color=$(COLOR)
.PHONY : esp-idf/78__esp-ml307/CMakeFiles/__idf_78__esp-ml307.dir/depend

