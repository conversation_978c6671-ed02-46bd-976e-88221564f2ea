
# Consider dependencies only in project.
set(CMAKE_DEPENDS_IN_PROJECT_ONLY OFF)

# The set of languages for which implicit dependencies are needed:
set(CMAKE_DEPENDS_LANGUAGES
  )

# The set of dependency files which are needed:
set(CMAKE_DEPENDS_DEPENDENCY_FILES
  "/home/<USER>/esp/esp-idf-v5.3/components/hal/cache_hal.c" "esp-idf/hal/CMakeFiles/__idf_hal.dir/cache_hal.c.obj" "gcc" "esp-idf/hal/CMakeFiles/__idf_hal.dir/cache_hal.c.obj.d"
  "/home/<USER>/esp/esp-idf-v5.3/components/hal/efuse_hal.c" "esp-idf/hal/CMakeFiles/__idf_hal.dir/efuse_hal.c.obj" "gcc" "esp-idf/hal/CMakeFiles/__idf_hal.dir/efuse_hal.c.obj.d"
  "/home/<USER>/esp/esp-idf-v5.3/components/hal/esp32s3/efuse_hal.c" "esp-idf/hal/CMakeFiles/__idf_hal.dir/esp32s3/efuse_hal.c.obj" "gcc" "esp-idf/hal/CMakeFiles/__idf_hal.dir/esp32s3/efuse_hal.c.obj.d"
  "/home/<USER>/esp/esp-idf-v5.3/components/hal/hal_utils.c" "esp-idf/hal/CMakeFiles/__idf_hal.dir/hal_utils.c.obj" "gcc" "esp-idf/hal/CMakeFiles/__idf_hal.dir/hal_utils.c.obj.d"
  "/home/<USER>/esp/esp-idf-v5.3/components/hal/mmu_hal.c" "esp-idf/hal/CMakeFiles/__idf_hal.dir/mmu_hal.c.obj" "gcc" "esp-idf/hal/CMakeFiles/__idf_hal.dir/mmu_hal.c.obj.d"
  "/home/<USER>/esp/esp-idf-v5.3/components/hal/mpu_hal.c" "esp-idf/hal/CMakeFiles/__idf_hal.dir/mpu_hal.c.obj" "gcc" "esp-idf/hal/CMakeFiles/__idf_hal.dir/mpu_hal.c.obj.d"
  )

# Targets to which this target links.
set(CMAKE_TARGET_LINKED_INFO_FILES
  "/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/bootloader/esp-idf/log/CMakeFiles/__idf_log.dir/DependInfo.cmake"
  "/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/bootloader/esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/DependInfo.cmake"
  "/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/bootloader/esp-idf/esp_common/CMakeFiles/__idf_esp_common.dir/DependInfo.cmake"
  "/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/bootloader/esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/DependInfo.cmake"
  "/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/bootloader/esp-idf/xtensa/CMakeFiles/__idf_xtensa.dir/DependInfo.cmake"
  "/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/bootloader/esp-idf/soc/CMakeFiles/__idf_soc.dir/DependInfo.cmake"
  "/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/bootloader/esp-idf/efuse/CMakeFiles/__idf_efuse.dir/DependInfo.cmake"
  "/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/bootloader/esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/DependInfo.cmake"
  "/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/bootloader/esp-idf/spi_flash/CMakeFiles/__idf_spi_flash.dir/DependInfo.cmake"
  "/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/bootloader/esp-idf/esp_system/CMakeFiles/__idf_esp_system.dir/DependInfo.cmake"
  "/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/bootloader/esp-idf/micro-ecc/CMakeFiles/__idf_micro-ecc.dir/DependInfo.cmake"
  "/home/<USER>/xiaozhi-project/xiaozhi-esp32-fixed/fixed_project/build/bootloader/esp-idf/esp_bootloader_format/CMakeFiles/__idf_esp_bootloader_format.dir/DependInfo.cmake"
  )

# Fortran module output directory.
set(CMAKE_Fortran_TARGET_MODULE_DIR "")
