#IncludeRegexLine: ^[ 	]*[#%][ 	]*(include|import)[ 	]*[<"]([^">]+)([">])

#IncludeRegexScan: ^.*$

#IncludeRegexComplain: ^$

#IncludeRegexTransform: 

/home/<USER>/esp/esp-idf-v5.3/components/esp_common/include/esp_assert.h
assert.h
/home/<USER>/esp/esp-idf-v5.3/components/esp_common/include/assert.h

/home/<USER>/esp/esp-idf-v5.3/components/esp_common/include/esp_bit_defs.h

/home/<USER>/esp/esp-idf-v5.3/components/esp_rom/patches/esp_rom_cache_writeback_esp32s3.S
xtensa/corebits.h
-
sdkconfig.h
-
soc/extmem_reg.h
/home/<USER>/esp/esp-idf-v5.3/components/esp_rom/patches/soc/extmem_reg.h

/home/<USER>/esp/esp-idf-v5.3/components/esp_rom/patches/esp_rom_longjmp.S
xtensa/corebits.h
-
sdkconfig.h
-

/home/<USER>/esp/esp-idf-v5.3/components/newlib/platform_include/assert.h
sdkconfig.h
-
stdlib.h
-
stdint.h
-

/home/<USER>/esp/esp-idf-v5.3/components/soc/esp32s3/include/soc/extmem_reg.h
soc.h
/home/<USER>/esp/esp-idf-v5.3/components/soc/esp32s3/include/soc/soc.h

/home/<USER>/esp/esp-idf-v5.3/components/soc/esp32s3/include/soc/reg_base.h

/home/<USER>/esp/esp-idf-v5.3/components/soc/esp32s3/include/soc/soc.h
stdint.h
-
esp_assert.h
/home/<USER>/esp/esp-idf-v5.3/components/soc/esp32s3/include/soc/esp_assert.h
esp_bit_defs.h
/home/<USER>/esp/esp-idf-v5.3/components/soc/esp32s3/include/soc/esp_bit_defs.h
reg_base.h
/home/<USER>/esp/esp-idf-v5.3/components/soc/esp32s3/include/soc/reg_base.h

/home/<USER>/esp/esp-idf-v5.3/components/xtensa/include/xtensa/corebits.h

config/sdkconfig.h

